<%@ page import="com.cnvd.DetailedInfo; com.cnvd.utils.*" %>
    <%@ page contentType="text/html;charset=UTF-8" %>
        <%@ page defaultCodec="html" %>
            <%@ page import="com.cnvd.flawInfo.*" %>
                <%@ page import="com.cnvd.patchInfo.*" %>
                    <html>

                    <head>
                        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
                        <meta name="layout" content="newmain2" />
                    </head>

                    <body>
                        <style style="text/css">
                            .top-nav {
                                display: flex;
                                margin-top: 15px;
                            }

                            .tab-li {
                                background-color: #fff;
                                color: #375ab4;
                                border: 2px solid #375ab4;
                            }

                            .curSelect {
                                background-color: #375ab4;
                                color: #fff;
                                border: 2px solid #375ab4;
                            }

                            .loophole-title {
                                font-size: 20px;
                                font-weight: bold;
                                text-align: center;
                            }

                            .disFlawTypes {
                                display: none;
                            }

                            .titleInfo {
                                display: flex;
                            }

                            .titleInfo p {
                                font-family: <PERSON><PERSON>, <PERSON><PERSON>;
                                font-weight: 900;
                                font-size: 24px;
                                color: #3541CB;
                                text-align: center;
                                font-style: normal;
                                text-transform: none;
                                margin-right: 10px;
                            }

                            .titleInfo span {
                                position: relative;
                                color: #3541CB;
                                top: 10px;
                            }

                            .flist .txtarea {
                                width: 654px;
                                max-width: 654px;
                            }

                            .select1 {
                                margin: 60px auto 0;
                                width: 248px;
                            }

                            .top_title {
                                display: flex;
                                align-items: center;
                                height: 80px;
                                border-bottom: 1px solid #e5e5e5;
                                margin-bottom: 10px;

                            }

                            .top_title p {
                                font-family: Arial, Arial;
                                font-weight: 900;
                                font-size: 24px;
                                color: #333333;
                                text-align: left;
                                font-style: normal;
                                text-transform: none;
                                margin: 0 10px;
                                letter-spacing: 1px;
                            }

                            .top_title span {
                                position: relative;
                                top: 7px;
                            }

                            .select2-container--default .select2-selection--single .select2-selection__rendered {
                                line-height: 38px;
                            }

                            .select2-container--default .select2-selection--single {
                                height: 38px;
                                border: 1px solid #ccc;
                            }

                            .select2-container {
                                margin-top: 11px;
                            }

                            .t-clear:after {
                                content: '';
                                display: block;
                                clear: both;
                            }

                            .clf img {
                                margin-top: 10px;
                            }

                            .select2-selection__rendered {
                                font-family: "MS Shell Dlg 2";
                            }

                            .toastInput {
                                height: 40px;
                                width: 240px;
                                border-radius: 5px 5px 5px 5px;
                                border: 1px solid #E5E8EF;
                                padding-left: 20px;
                                box-sizing: border-box;
                                padding: 4px 3px 4px 3px;
                                ;
                            }

                            .newldsb .ipt,
                            .newldsb select {
                                width: 93%;
                                height: 40px;
                                border-radius: 5px 5px 5px 5px;
                                border: 1px solid #E5E8EF;
                                padding-left: 20px;
                                box-sizing: border-box;
                                padding: 4px 3px 4px 3px;
                                line-height: 26px;
                                font-size: 14px;
                                color: #666666;
                                outline: none;
                            }

                            .newldsb .curLabel {
                                font-family: Arial, Arial;
                                font-weight: 400;
                                font-size: 18px;
                                color: #000;
                                text-align: left;
                                font-style: normal;
                                text-transform: none;
                            }

                            .newldsb .curLabel .min-cn {
                                margin-left: 6px;
                            }

                            .mt-40 {
                                margin-bottom: 40px;
                            }

                            .newldsb .txtarea {
                                max-width: 92%;
                                width: 92%;
                                max-width: 1200px;
                                margin-top: 10px;
                            }

                            .pl20 {
                                padding-left: 20px;
                            }

                            .titleInfo .edit-btn {
                                position: absolute;
                                right: 0;
                                top: 0px;
                                color: #fff
                            }

                            .textStyle {
                                min-height: 40px;
                                line-height: 40px;
                                padding-left: 4px;
                            }
                            .textStyle a{
                                color: blue!important;
                            }
                            .basicSelect{
                                display: flex;
                            }
                            .basicSelect li{
                                padding: 0px 16px;
                                border: 1px solid #ccc;
                                margin-right: -1px;
                                cursor: pointer;
                            }
                             .basicSelect li.basicSelectActive{
                                background-color: #3541CB;
                                color: #fff;
                             }
                             .layui-panel-add{
                                padding: 15px;
                                border-radius: 20px;
                                margin-bottom: 15px;
                             }
                        </style>
                        <div id="command_center" class="clf main p_t u_comment">
                            <g:render template="/usermenu/usermenutemp2"></g:render>
                            <div class="info i_report" id='tab-basic'>
                                <div class="layui-panel" style="padding: 15px;border-radius: 20px;">
                                    <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                                        <ul class="layui-tab-title"
                                            style="border-bottom: 4px solid #375ab4; font-weight: bold;display: flex;padding-bottom: 2px;">
                                            <li class="tab-li curSelect" target="tab-basic"
                                                style="display: flex;width: 190px;margin-right: 4px;"><span
                                                    style="font-size: 16px;">Basic Information</span>
                                                <p class="min-cn" style="margin-left:2px">基本信息</p>
                                            </li>
                                            <li class="tab-li" target="tab-verification"
                                                style="display: flex;width: 220px;margin-right: 4px;">
                                                <span style="font-size: 16px;">Verification Information</span>
                                                <p class="min-cn" style="margin-left:2px">验证信息</p>
                                            </li>
                                            <li class="tab-li" target="tab-disposal"
                                                style="display: flex;width: 212px;margin-right: 4px;">
                                                <span style="font-size: 16px;">Disposal Information</span>
                                                <p class="min-cn" style="margin-left:2px">处置信息</p>
                                            </li>
                                            <li class="tab-li" target="tab-patch"
                                                style="display: flex;width: 190px;margin-right: 4px;">
                                                <span style="font-size: 16px;">Patch Information</span>
                                                <p class="min-cn" style="margin-left:2px">补丁信息</p>
                                            </li>
                                            <li class="tab-li" target="tab-time"
                                                style="display: flex;width: 190px;margin-right: 4px;">
                                                <span style="font-size: 16px;">Time Information</span>
                                                <p class="min-cn" style="margin-left:2px">时间信息</p>
                                            </li>
                                         </ul>
                                    </div>
                                    <div>
                                        <div class="m_h list_wrap newldsb" style="margin:15px 0">
                                            <g:form action="editLoopholeUpdate" method="post" name="addAssetForm"  controller="TUser"
                                                enctype="multipart/form-data">
                                                <div class="flist">
                                                    <g:hiddenField name="id" id="idsa" value="${flaw?.id}" />
                                                    <g:hiddenField name="number" id="number" value="${flaw?.number}" />
                                                    <g:hiddenField name="productName" id="productName"
                                                        value="${flaw.title}" />
                                                    <div class="layui-row layui-panel layui-panel-add">
                                                        <div class="layui-col-md12">
                                                            <p class="loophole-title"> ${flaw.title}</p>
                                                        </div>
                                                        <div class="layui-col-md12">
                                                            <div class="titleInfo">
                                                                <p>Basic Information</p> <span>基本信息</span>
                                                                <span class="layui-btn layui-btn-normal edit-btn"
                                                                    onclick="switchSattus()">Edit / 编辑</span>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Vulnerability Name<span
                                                                    class="min-cn">漏洞编号</span></p>
                                                            <div>
                                                                <div class="textStyle">${flaw?.number}</div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Vulnerability Title<span
                                                                    class="min-cn">漏洞标题</span></p>
                                                            <div>
                                                                <div class="looking textStyle">${flaw?.title}</div>
                                                                <div class="editing">
                                                                    <g:textField class="ipt" name="title"
                                                                        value="${flaw?.title}" />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Affiliation<span
                                                                    class="min-cn">所属单位</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    ${flaw?.manufacturer?.name}</div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Vulnerability Description<span
                                                                    class="min-cn">漏洞描述</span></p>
                                                            <div>
                                                                <div class="looking textStyle">
                                                                    ${flaw?.detailedInfo.description}</div>
                                                                <div class="editing">
                                                                    <g:textArea name="description"
                                                                        class="txtarea"
                                                                        value="${flaw?.detailedInfo.description}" />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Vulnerability Unit<span
                                                                    class="min-cn">漏洞单位</span></p>
                                                            <div>
                                                                <div class="textStyle">${flaw?.unitName}</div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Vulnerability Attachment<span
                                                                    class="min-cn">漏洞附件</span></p>
                                                       
                                                                <div class="textStyle">
                                                                     <g:link title="下载附件" controller="flaw" action="downloadAttachment" params="[id:flaw?.attachment?.id,flawId:flaw?.id]">
                                                                        ${flaw?.attachment?.fileName}
                                                                    </g:link>
                                                                    <g:if test="${attPdfList}">
                                                                        <g:each in="${attPdfList}" var="attachmentPdf">
                                                                            <g:if test="${attachmentPdf?.attachmentId==flaw?.attachment?.id}">
                                                                                <g:link title="下载附件" style="color:blue;" controller="common" action="flawDownloadPdf" params="[id:attachmentPdf?.id]">
                                                                                    ${attachmentPdf?.fileName}
                                                                                </g:link>
                                                                            </g:if>
                                                                        </g:each>
                                                                    </g:if>
                                                                </div>
                                                        </div>
                                                         <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">CVE Number<span
                                                                    class="min-cn">CVE编号</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                       <%
                                                                            def cveList = ReferenceInfo.findAllByFlawAndReferenceType(flaw, ReferenceType.get(1))
                                                                        %>
                                                                        <g:if test="${cveList}">
                                                                            <tr>
                                                                                <td>
                                                                                    <g:each in="${cveList}" var="referenceInfoInstance">
                                                                                        <a href="http://cve.mitre.org/cgi-bin/cvename.cgi?name=${referenceInfoInstance?.referenceNumber}"
                                                                                        target="_blank">${referenceInfoInstance?.referenceNumber}</a>
                                                                                    </g:each>
                                                                                </td>
                                                                            </tr>
                                                                        </g:if>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                               <p class="curLabel">Vulnerability Fix<span
                                                                    class="min-cn">漏洞解决方案</span></p>
                                                              <% def formalWay = flaw?.detailedInfo?.formalWay %>
                                                            <div class="looking textStyle">
                                                                    ${formalWay}
                                                            </div>
                                                            <div class="editing">
                                                                    <g:textArea name="formalWay"
                                                                        class="txtarea"
                                                                        value="${formalWay}" />
                                                            </div>

                                                        </div>
                                                        <div class="layui-col-md6 pl20" style="margin-bottom: 12px;">
                                                            <p class="curLabel"> Severity Level<span
                                                                    class="min-cn">危害等级</span></p>
                                                            <div>
                                                                <div class="looking textStyle">
                                                                    ${DictionaryInfo.get(flaw?.serverityId)?.name}危
                                                                </div>
                                                                <div class="editing">
                                                        <% def severityDi = DictionaryInfo.get(flaw?.serverityId); %>
                                                            <g:if test="${severityDi}">
                                                                <g:if test="${severityDi?.value == 1}">
                                                                    <span class="green showInfo"></span>
                                                                </g:if>
                                                                <g:elseif test="${severityDi?.value == 2}">
                                                                    <span class="yellow showInfo"></span>
                                                                </g:elseif>
                                                                <g:elseif test="${severityDi?.value == 3}">
                                                                    <span class="red showInfo"></span>
                                                                </g:elseif>
                                                              <input type="text" id="serverity-id" class="ipt" value="${DictionaryInfo.get(flaw?.serverityId)?.name}" readonly />
                                                                <input type="hidden" name="serverityId " value="${flaw?.serverityId }" />
                                                            </g:if>
                                                            <g:else>
                                                                未评级
                                                            </g:else>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <hr style="border: 1px dashed #ccc;"/>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Attack Vector<span
                                                                    class="min-cn">攻击途径</span></p>
                                                            <div>
                                                                <div class="looking textStyle">
                                                                    ${flaw?.basemetric?.accessVector?.name}
                                                                </div>
                                                                <div class="editing textStyle">
                                                                    <ul class="basicSelect">
                                                                        <g:each in="${MetricInfo.findAllByType(1)}" var="accessVector">
                                                                            <li id="accessVector_${accessVector?.id}" ${flaw?.basemetric?.accessVector?.id == accessVector.id ? 'class=basicSelectActive' : ''}>${accessVector.name}</li>
                                                                        </g:each> 
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Attack Complexity<span
                                                                    class="min-cn">攻击复杂度</span></p>
                                                            <div>
                                                                <div class="looking textStyle">
                                                                    ${flaw?.basemetric?.accessComplexity?.name}
                                                                </div>
                                                                  <div class="editing textStyle">
                                                                    <ul class="basicSelect">
                                                                        <g:each in="${MetricInfo.findAllByType(2)}" var="accessComplexity">
                                                                            <li id="accessComplexity_${accessComplexity?.id}" ${flaw?.basemetric?.accessComplexity?.id == accessComplexity.id ? 'class=basicSelectActive' : ''}>${accessComplexity.name}</li>
                                                                        </g:each> 
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Authentication<span
                                                                    class="min-cn">认证</span></p>
                                                            <div>
                                                                <div class="looking textStyle">
                                                                    ${flaw?.basemetric?.authentication?.name}
                                                                </div>
                                                                <div class="editing textStyle">
                                                                    <ul class="basicSelect">
                                                                        <g:each in="${MetricInfo.findAllByType(3)}" var="authentication">
                                                                            <li id="authentication_${authentication?.id}" ${flaw?.basemetric?.authentication?.id == authentication.id ? 'class=basicSelectActive' : ''}>${authentication.name}</li>
                                                                        </g:each> 
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Confidentiality<span
                                                                    class="min-cn">机密性</span></p>
                                                            <div>
                                                                <div class="looking textStyle">
                                                                    ${flaw?.basemetric?.confidentialityImpact?.name}
                                                                </div>
                                                                <div class="editing textStyle">
                                                                  <ul class="basicSelect">
                                                                        <g:each in="${MetricInfo.findAllByType(4)}" var="confidentialityImpact">
                                                                            <li id="confidentialityImpact_${confidentialityImpact?.id}" ${flaw?.basemetric?.confidentialityImpact?.id == confidentialityImpact.id ? 'class=basicSelectActive' : ''}>${confidentialityImpact.name}</li>
                                                                        </g:each> 
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Integrity<span class="min-cn">完整性</span>
                                                            </p>
                                                            <div>
                                                                <div class="looking textStyle">
                                                                    ${flaw?.basemetric?.integrityImpact?.name}
                                                                </div>
                                                                <div class="editing textStyle">
                                                                  <ul class="basicSelect">
                                                                        <g:each in="${MetricInfo.findAllByType(5)}" var="integrityImpact">
                                                                            <li id="integrityImpact_${integrityImpact?.id}" ${flaw?.basemetric?.integrityImpact?.id == integrityImpact.id ? 'class=basicSelectActive' : ''}>${integrityImpact.name}</li>
                                                                        </g:each> 
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20" style="margin-bottom: 12px;">
                                                            <p class="curLabel">Availability<span
                                                                    class="min-cn">可用性</span></p>
                                                            <div>
                                                                <div class="looking textStyle">
                                                                    ${flaw?.basemetric?.availabilityImpact?.name}</div>
                                                                <div class="editing textStyle">
                                                                  <ul class="basicSelect">
                                                                        <g:each in="${MetricInfo.findAllByType(5)}" var="availabilityImpact">
                                                                            <li id="availabilityImpact_${availabilityImpact?.id}" ${flaw?.basemetric?.availabilityImpact?.id == availabilityImpact.id ? 'class=basicSelectActive' : ''}>${availabilityImpact.name}</li>
                                                                        </g:each> 
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                          <hr style="border: 1px dashed #ccc;" />
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Vulnerability URL<span
                                                                    class="min-cn">漏洞URL</span></p>
                                                            <div>
                                                                <div class=" textStyle">${flaw?.getFlawUrl()?.url}</div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Vulnerability Type<span
                                                                    class="min-cn">漏洞所属类型</span></p>
                                                            <div>
                                                                <div class="textStyle">${flaw?.flawTypes?.name }</div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Vulnerability Cause<span
                                                                    class="min-cn">漏洞产生原因</span></p>
                                                            <div>
                                                                <div class="looking textStyle">
                                                                    ${DictionaryInfo.get(flaw?.causeId)?.name } </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Associated Threats<span
                                                                    class="min-cn">漏洞引发的威胁</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    ${DictionaryInfo.get(flaw?.threadId)?.name } </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Attack Surface<span
                                                                    class="min-cn">漏洞利用的攻击位置</span>
                                                            </p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    ${DictionaryInfo.get(flaw?.positionId)?.name }</div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Affected Component Type<span
                                                                    class="min-cn">漏洞影响对象类型</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    ${DictionaryInfo.get(flaw?.softStyleId)?.name }
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Affected Product(s)<span
                                                                    class="min-cn">影响产品</span></p>
                                                            <div>
                                                                <textarea id="productValue" name="productValue" rows="5"
                                                                    cols="60" disabled>${productName}</textarea>

                                                                <input id="productIds" name="productIds"
                                                                    value="${productIds}" type="hidden" />

                                                                <a href="javascript:showProductList();" class="editing">
                                                                    <img style="vertical-align: middle; cursor: pointer;"
                                                                        src="${request.contextPath}/images/lookup.png" /></a>

                                                                <a href="javascript:delProductList();" class="editing">
                                                                    <img style="vertical-align: middle; cursor: pointer;"
                                                                        src="${request.contextPath}/images/error_icon.png" /></a>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">IP<span class="min-cn">所属IP</span></p>
                                                            <div>
                                                                <div class="textStyle">${flaw?.flowIP}</div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">SQLmap Command<span
                                                                    class="min-cn">sqlmap命令</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    <g:if
                                                                        test="${flaw.flawTypes!=null && flaw.flawTypes?.valueId=='sqlInjectionVulnerability' }">
                                                                        <g:each in="${flawTypesParamMiddle}"
                                                                            var="flawTypesParamMiddle">
                                                                            <g:if
                                                                                test="${flawTypesParamMiddle.flawTypesParam?.valueId=='sqlInjectionVulnerabilitySqlMap' }">
                                                                                ${flawTypesParamMiddle?.param_values}
                                                                            </g:if>
                                                                        </g:each>
                                                                    </g:if>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">XSS Trigger Payload<span
                                                                    class="min-cn">触发xss的payload</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    <g:if
                                                                        test="${flaw.flawTypes!=null && flaw.flawTypes?.valueId=='xssVulnerability' }">
                                                                        <g:each in="${flawTypesParamMiddle}"
                                                                            var="flawTypesParamMiddle">
                                                                            <g:if
                                                                                test="${flawTypesParamMiddle.flawTypesParam?.valueId=='xssVulnerabilityPayload' }">
                                                                                ${flawTypesParamMiddle?.param_values}
                                                                            </g:if>
                                                                        </g:each>
                                                                    </g:if>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Weak-Credential Username<span
                                                                    class="min-cn">弱口令账号</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    <g:if
                                                                        test="${flaw.flawTypes!=null && flaw.flawTypes?.valueId=='weakPassword' }">
                                                                        <g:each in="${flawTypesParamMiddle}"
                                                                            var="flawTypesParamMiddle">
                                                                            <g:if
                                                                                test="${flawTypesParamMiddle.flawTypesParam?.valueId=='weakPasswordAccountNumber' }">
                                                                                ${flawTypesParamMiddle?.param_values}
                                                                            </g:if>
                                                                        </g:each>
                                                                    </g:if>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Weak-Credential Password<span
                                                                    class="min-cn">弱口令密码</span></p>
                                                            <div>
                                                                <div class="looking textStyle">
                                                                    <g:if
                                                                        test="${flaw.flawTypes!=null && flaw.flawTypes?.valueId=='weakPassword' }">
                                                                        <g:each in="${flawTypesParamMiddle}"
                                                                            var="flawTypesParamMiddle">
                                                                            <g:if
                                                                                test="${flawTypesParamMiddle.flawTypesParam?.valueId=='weakPasswordPwd' }">
                                                                                ${flawTypesParamMiddle?.param_values}
                                                                            </g:if>
                                                                        </g:each>
                                                                    </g:if>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Middleware/Framework<span
                                                                    class="min-cn">中间件/框架</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    <g:if
                                                                        test="${flawTypesParamMiddle.flawTypesParam?.valueId=='remoteCommandExecutionMiddleware' }">
                                                                        ${flawTypesParamMiddle?.param_values}
                                                                    </g:if>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Exploitation Tool(s)<span
                                                                    class="min-cn">利用工具</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    <g:if
                                                                        test="${flawTypesParamMiddle.flawTypesParam?.valueId=='remoteCommandExecutionTool'}">
                                                                        ${flawTypesParamMiddle?.param_values}
                                                                    </g:if>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Poc<span class="min-cn">poc</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    <g:if
                                                                        test="${flawTypesParamMiddle.flawTypesParam?.valueId=='denialOfServicePoc' }">
                                                                        ${flawTypesParamMiddle?.param_values}
                                                                    </g:if>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Trigger Location<span
                                                                    class="min-cn">触发位置</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    <g:if
                                                                        test="${flawTypesParamMiddle.flawTypesParam?.valueId=='binaryVulnerabilityTriggerPosition' }">
                                                                        ${flawTypesParamMiddle?.param_values}
                                                                    </g:if>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Permanent Solution<span
                                                                    class="min-cn">正式解决方案</span></p>
                                                                <div class="textStyle"> 
                                                                    ${flaw?.detailedInfo?.formalWay}
                                                                </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Interim Mitigation<span
                                                                    class="min-cn">临时解决方案</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    ${flaw?.detailedInfo?.tempWay}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Zero-Day Status<span
                                                                    class="min-cn">是否零日漏洞</span></p>
                                                            <div>
                                                                <div class="textStyle">${flaw?.isZero }</div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Current Status<span
                                                                    class="min-cn">当前状态</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    <g:if test="${flaw.status==1}">
                                                                        <g:if test="${flaw.lastAction==1}">
                                                                            一级审核
                                                                        </g:if>
                                                                        <g:if test="${flaw.lastAction==2}">
                                                                            驳回待审
                                                                        </g:if>
                                                                    </g:if>
                                                                    <g:if test="${flaw.status==2}">
                                                                        二级审核
                                                                    </g:if>
                                                                    <g:if test="${flaw.status==3}">
                                                                        三级审核
                                                                    </g:if>
                                                                    <g:if test="${flaw.status==4}">
                                                                        补录漏洞
                                                                    </g:if>
                                                                    <g:if test="${flaw.status==5}">
                                                                        补录待审核
                                                                    </g:if>
                                                                    <g:if test="${flaw.status==9}">
                                                                        已归档
                                                                    </g:if>
                                                                    <g:if test="${flaw.status==-1}">
                                                                        驳回
                                                                    </g:if>
                                                                    <g:if test="${flaw.status==-2}">
                                                                        作废
                                                                    </g:if>
                                                                    <g:if test="${flaw.status==6}">
                                                                        暂不归档
                                                                    </g:if>
                                                                    <g:if test="${flaw.status==7}">
                                                                        补充信息
                                                                    </g:if>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Reference(s)<span
                                                                    class="min-cn">引用链接</span></p>
                                                            <div>
                                                                <div class="textStyle">${flaw.referenceLink}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="layui-row layui-panel layui-panel-add">
                                                        <div class="layui-col-md12" id="tab-verification">
                                                            <div class="titleInfo">
                                                                <p>Verification Details</p> <span>验证信息</span>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Verification Method<span
                                                                    class="min-cn">验证名称</span></p>
                                                                <div class="looking textStyle">${exploitInstance?.exploitName }
                                                                </div>
                                                                  <div class="editing textStyle">
                                                                    <g:textField class="ipt exploitInstance" name="exploitInstance.exploitName"
                                                                        value="${exploitInstance?.exploitName}" />
                                                                </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Verification Principle<span
                                                                    class="min-cn">验证原理</span></p>
                                                                <div class="looking textStyle">${exploitInstance?.concept }
                                                                </div>
                                                                <div class="editing textStyle">
                                                                    <g:textField class="ipt exploitInstance" name="exploitInstance.concept"
                                                                        value="${exploitInstance?.concept }" />
                                                                </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Proof of Concept (PoC)<span
                                                                    class="min-cn">验证poc</span></p>
                                                                <div class="looking textStyle">${exploitInstance?.poc }</div>
                                                                
                                                                <div class="editing textStyle">
                                                                    <g:textField class="ipt exploitInstance" name="exploitInstance.poc"
                                                                        value="${exploitInstance?.poc }" />
                                                                </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Verification Recommendation<span
                                                                    class="min-cn">验证建议</span></p>
                                                                <div class="looking textStyle">${exploitInstance?.suggestion }</div>
                                                                <div class="editing textStyle">
                                                                    <g:textField class="ipt exploitInstance" name="exploitInstance.suggestion"
                                                                        value="${exploitInstance?.suggestion }" />
                                                                </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Verification Date<span
                                                                    class="min-cn">验证日期</span></p>
                                                            <div>
                                                                <div class="looking textStyle">${exploitInstance?.exploitTime }</div>
                                                                 <div class="editing textStyle">
                                                                    <g:textField class="ipt exploitInstance" name="exploitInstance.exploitTime"
                                                                        value="${exploitInstance?.exploitTime }" />
                                                                </div>
                                                            </div>
                                                        </div>
  
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Verification Attachment<span
                                                                    class="min-cn">验证附件</span></p>
                                                            <div>
                                                                <% 
                                                                def attList=ExploitAttachment.executeQuery('select ea.attachment from ExploitAttachment ea where ea.exploit=?',[exploitInstance]);
                                                                def exploitAttPdfList=ExploitAttachmentPdf.executeQuery('select ea.attachmentPdf from ExploitAttachmentPdf ea where ea.exploit=?',[exploitInstance]) %>
                                                                <div class="looking textStyle">
                                                                        <g:if test="${attList}">
                                                                            <g:each in="${attList}" var="attachment">
                                                                                <g:link title="下载附件" style="color:blue;"
                                                                                    controller="certificate"
                                                                                    action="download"
                                                                                    params="[id:attachment?.id]">
                                                                                    ${attachment?.fileName }
                                                                                </g:link>&nbsp;&nbsp;
                                                                                <g:if test="${exploitAttPdfList}">
                                                                                    <g:each in="${exploitAttPdfList}"
                                                                                        var="attachmentPdf">
                                                                                        <g:if
                                                                                            test="${attachmentPdf?.attachmentId==attachment?.id}">
                                                                                            <g:link title="下载附件"
                                                                                                style="color:blue;"
                                                                                                controller="common"
                                                                                                action="downloadPdf"
                                                                                                params="[id:attachmentPdf?.id]">
                                                                                                ${attachmentPdf?.fileName}
                                                                                            </g:link>
                                                                                        </g:if>
                                                                                    </g:each>
                                                                                </g:if>
                                                                                <br />
                                                                            </g:each>
                                                                        </g:if>
                                                                        <g:else>
                                                                            (无附件)
                                                                        </g:else>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Verification Reference Links<span
                                                                    class="min-cn">验证信息参考链接</span></p>
                                                            <div>
                                                                <div class="looking textStyle">
                                                                    ${exploitInstance?.referenceLink}
                                                                </div>
                                                                   <div class="editing textStyle">
                                                                    <g:textField class="ipt exploitInstance" name="exploitInstance.referenceLink"
                                                                        value="${exploitInstance?.referenceLink }" />
                                                                </div>
                                                            </div>
                                                        </div>
                                                     </div>
                                                        
                                                    <div class="layui-row layui-panel layui-panel-add">
                                                        <div class="layui-col-md12" id="tab-disposal">
                                                            <div class="titleInfo">
                                                                <p>Remediation Information</p> <span>处置信息</span>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Remediation Name<span
                                                                    class="min-cn">处置名称</span></p>
                                                            <div>
                                                                <div class="textStyle">${disposalInstance?.patchName }
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Remediation Description<span
                                                                    class="min-cn">处置描述</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    ${disposalInstance?.patchDescription }</div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Remediation Contact<span
                                                                    class="min-cn">处置联系方式</span></p>
                                                            <div>
                                                                <div class="looking textStyle">${disposalInstance?.tel}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Remediation Attachment<span
                                                                    class="min-cn">处置附件</span></p>
                                                            <div>
                                                                <div class="textStyle">
                                                                    <div id="patchInfoEditAttSpan">
                                                                        <g:if test="${attPdfList}">
                                                                            <g:each in="${attPdfList }"
                                                                                var="attachment">
                                                                                <span id="patchInfoAtt${attachment.id}">
                                                                                    <g:link title="下载附件"
                                                                                        controller="common"
                                                                                        action="download"
                                                                                        params="[id:attachment?.id]">
                                                                                        <font color="blue">
                                                                                            ${attachment?.fileName}
                                                                                        </font>
                                                                                    </g:link>
                                                                                    &nbsp;&nbsp;&nbsp;
                                                                                    <a
                                                                                        href="javascript:delAttachment('${attachment.id}')">删除</a>
                                                                                    <br />
                                                                                </span>
                                                                            </g:each>
                                                                        </g:if>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Remediation Status<span
                                                                    class="min-cn">处置状态</span></p>
                                                            <div>
                                                                <div class="textStyle">${disposalInstance?.status}</div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Other<span class="min-cn">其他</span></p>
                                                            <div>
                                                                <div class="textStyle">${disposalInstance?.remark }
                                                                </div>
                                                            </div>
                                                        </div>
                                                        </div>
                                                        
                                                    <div class="layui-row layui-panel layui-panel-add">
                                                        <div class="layui-col-md12" id="tab-patch">
                                                            <div class="titleInfo">
                                                                <p>Patch Information</p> <span>补丁信息</span>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Patch Name<span
                                                                    class="min-cn">补丁名称</span></p>
                                                            <div>
                                                                <div class="looking textStyle">${patchInfoInstance?.patchName }
                                                                </div>
                                                                    <div class="editing">
                                                                    <g:textField class="ipt" name="patchName" value="${patchInfoInstance?.patchName}" />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Patch Description<span
                                                                    class="min-cn">补丁描述</span></p>
                                                                <div class="looking textStyle">${patchInfoInstance?.patchDescription }	</div>
                                                                <div class="editing">
                                                                      <g:textArea name="patchDescription"
                                                                        class="txtarea"
                                                                        value="${patchInfoInstance?.patchDescription}" />
                                                                </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Patch Verification Principle<span
                                                                    class="min-cn">补丁验证原理</span></p>
                                                            <div>
                                                                <div class="looking textStyle">${patchInfoInstance?.function }</div>
                                                                    
                                                                <div class="editing">
                                                                    <g:textArea name="function"
                                                                        class="txtarea"
                                                                        value="${patchInfoInstance?.function}" />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Patch Link<span
                                                                    class="min-cn">补丁链接</span></p>
                                                            <div>
                                                                <div class="looking textStyle">${patchInfoInstance?.patchUrl }</div>
                                                                <div class="editing">
                                                                     <g:textArea name="patchUrl"
                                                                        class="txtarea"
                                                                        value="${patchInfoInstance?.patchUrl}" />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Patch Attachment<span
                                                                    class="min-cn">补丁附件</span></p>
                                                            <div>
                                                                <div class="looking textStyle">
                                                                    <% def
                                                                        patchInfoAttList=PatchInfoAttachment.executeQuery('select pia.attachment from PatchInfoAttachment pia where pia.patchInfo=?',[patchInfoInstance]) %>
                                                                        <g:if test="${patchInfoAttList}">
                                                                            <g:each in="${patchInfoAttList}"
                                                                                var="attachment">
                                                                                <g:link title="下载附件" style="color:blue;"
                                                                                    controller="patchInfo"
                                                                                    action="download"
                                                                                    params="[id:attachment?.id]">
                                                                                    ${attachment?.fileName }
                                                                                </g:link>
                                                                                <br />
                                                                            </g:each>
                                                                        </g:if>
                                                                        <g:else>
                                                                            (无附件)
                                                                        </g:else>
                                                                </div>
                                                                <div class="editing">
                                                                     <input type="file" name="patchFile"/>${patchInfoInstance?.attachment?.fileName}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                        
                                                    <div class="layui-row layui-panel layui-panel-add">
                                                        <div class="layui-col-md12" id="tab-time">
                                                            <div class="titleInfo">
                                                                <p>Timeline Information</p> <span>时间信息</span>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Submission Time<span
                                                                    class="min-cn">报送时间</span></p>
                                                            <div>
                                                                <div class="textStyle">${flaw?.submitTime}</div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Disclosure Time<span
                                                                    class="min-cn">公开时间</span></p>
                                                            <div>
                                                                <div class="textStyle">${flaw?.openTime}</div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-col-md6 pl20">
                                                            <p class="curLabel">Vendor Edit Time<span
                                                                    class="min-cn">厂商编辑时间</span></p>
                                                            <div>
                                                                <div class="textStyle">${flawUseLog?.lastUpdated}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </g:form>
                                                     <p style="text-align: end;">
                                            <a href="${request.contextPath}/user/userFlawList" class="layui-btn layui-btn-primary">
                                                <span>Cancel / 取消</span>
                                            </a>
                                            <a href="javascript:void(0);" id="subForm" class="layui-btn editing">
                                                <span>Submit / 提交</span>
                                            </a>
                                        </p>
                                        </div>
                                        <div id="productListDialog" class="adv" title="选择产品" style="display: none;">
                                            <div>
                                                单位：
                                                <input id="manuName" name="manuName" type="text"
                                                    value="${params.manuName }" />
                                                <a href="javascript:showManuList();"> <img
                                                        style="vertical-align: middle; cursor: pointer;"
                                                        src="${request.contextPath}/images/lookup.png" /></a>
                                                产品：
                                                <input id="productCategoryName" name="productCategoryName" type="text"
                                                    value="${params.productCategoryName }" />
                                                <a href="javascript:showProductCategoryList();"> <img
                                                        style="vertical-align: middle; cursor: pointer;"
                                                        src="${request.contextPath}/images/lookup.png" /></a>
                                                版本：
                                                <input id="edition" placeholder="多版本用中文分号隔开" name="productInfo.edition"
                                                    type="text" value="${params." productInfo.edition" }" />
                                                <a href="javascript:showEditionList();"> <img
                                                        style="vertical-align: middle; cursor: pointer;"
                                                        src="${request.contextPath}/images/lookup.png" /></a>
                                                <input type="button" onclick="productListFormSubmit();" value="搜索" />
                                            </div>
                                            <div>
                                                <input type="button" onclick="selectBack()" value="选择带回" />
                                            </div>

                                            <div style="margin-top: 10px;" id="productListDiv"></div>
                                        </div>

                                        <div id="productCancelDialog" class="adv" title="已选择产品" style="display: none;">
                                            <div>
                                                <input type="button" onclick="productCancel()" value="取消关联产品" />
                                            </div>

                                            <div style="margin-top: 10px;" id="productCancelDiv"></div>
                                        </div>
                                        
                               
                                    </div>
                                </div>
                            </div>
                        </div>
                        <script type="text/javascript">
                            changeDivC(93);
                            var curStatus = 'edit'
                            $('.looking').hide()
                            $('.editing').show()
                            curStatus = '${obj.canEdit}'
                            switchSattus()
                            // 监听.basicSelect下li的点击事件
                            $('.basicSelect li').click(function () {
                                // 检查当前点击的li是否没有active类
                                if (!$(this).hasClass('basicSelectActive')) {
                                    // 移除相邻li的active类
                                    $(this).siblings().removeClass('basicSelectActive');
                                    // 为当前点击的li添加active类
                                    $(this).addClass('basicSelectActive');
                                }
                                    const basicSelectActiveElements = document.getElementsByClassName('basicSelectActive');
                                    var obj = {}
                                    var idArr 
                                    for (let i = 0; i < basicSelectActiveElements.length; i++) {
                                        console.log(basicSelectActiveElements[i].id);
                                        idArr = basicSelectActiveElements[i].id?.split('_')
                                        obj[idArr[0]+'.id'] = idArr[1]
                                    }
                                    console.log(obj);
                                    getWxdj(obj)

                            });
                            function getWxdj(obj){
                                let orginObj ={
                                    'accessVector.id' :obj['accessVector.id'],                         //攻击途径
                                    'accessComplexity.id':obj['accessComplexity.id'],                 //攻击复杂度
                                    'availabilityImpact.id':obj['availabilityImpact.id'],           //可用性
                                    'authentication.id':obj['authentication.id'],               //认证
                                    'confidentialityImpact.id':obj['confidentialityImpact.id'],       //机密性
                                    'integrityImpact.id':obj['integrityImpact.id'],                 //完整性
                                }
                                    var  hasData = true 
                                    for (let key in orginObj) {
                                        if (!orginObj[key]) {
                                            hasData = false;
                                        }
                                    }
                                    if (!hasData) {
                                        return
                                    }
                                    $.ajax({
                                        type: "post",
                                        url: "${request.contextPath}/flaw/calScore",
                                        data: obj,
                                        dataType: "json",
                                        success: function (data) {
                                            if(data?.data){
                                                $('#serverity-id').val(data.data)
                                            }
                                        }
                                    });
                            }
                            function switchSattus() {
                                if (curStatus == 'look') {
                                    curStatus = "edit"
                                    $('.looking').show()
                                    $('.editing').hide()
                                } else {
                                    curStatus = "look"
                                    $('.looking').hide()
                                    $('.editing').show()
                                }

                            }
                            $("#serverityId").change(function () {
                                $.post("${request.contextPath}/TUser/serverityName", { serverityId: $(this).val() }, function (data) {
                                    $("#serverityName").html("").append(data);
                                });
                            })
                            $("html, body").animate(
                                {
                                    scrollTop: $('#' + '${obj.area}').offset().top // 滚动到目标位置
                                },
                                800 // 动画时间（毫秒）
                            );
                            $('.tab-li').click(function () {
                                // 为 tab-li 元素添加点击事件
                                // 检查当前点击的元素是否已有 curSelect 类
                                if (!$(this).hasClass("curSelect")) {
                                    // 移除所有 tab-li 元素的 curSelect 类
                                    $(".tab-li").removeClass("curSelect");
                                    // 为当前点击的元素添加 curSelect 类
                                    $(this).addClass("curSelect");
                                }
                                var target = "#" + $(this).attr("target"); // 获取目标锚点（如 #section1）

                                $("html, body").animate(
                                    {
                                        scrollTop: $(target).offset().top // 滚动到目标位置
                                    },
                                    800 // 动画时间（毫秒）
                                );
                            })
                            $("#subForm").click(function () {
                                $("#addAssetForm").submit();
                            });
                            $("#selectManuSpan").click(function () {
                                showManuList();
                            });
                            $(function () {
                                /*   $("#productName").value="
                                ${productName}"*/
                                document.getElementById("productName").value = "${productName}"

                                $("#addAssetForm").validate({
                                    errorPlacement: function (error, element) {
                                        $("#" + element.attr("id") + "_error").html('');
                                        error.appendTo($("#" + element.attr("id") + "_error"));
                                    },
                                    rules: {
                                        manuName: {
                                            required: true
                                        },
                                        productCategoryName: {
                                            required: true
                                        },
                                        edition: {
                                            required: true
                                        }
                                    },
                                    messages: {
                                        manuName: {
                                            required: "请输入厂商名"
                                        },
                                        productCategoryName: {
                                            required: "请输入产品名"
                                        },
                                        edition: {
                                            required: "请输入版本"
                                        }
                                    }
                                });
                                if ("${flash.message}") {
                                    alert("${flash.message}");
                                }
                                $("#productListDialog").dialog({
                                    autoOpen: false,
                                    height: 460,
                                    width: 750,
                                    modal: true,
                                    buttons: {
                                        "关闭": function () {
                                            $(this).dialog("close");
                                        }
                                    }
                                });

                                $("#productCancelDialog").dialog({
                                    autoOpen: false,
                                    height: 460,
                                    width: 750,
                                    modal: true,
                                    buttons: {
                                        "关闭": function () {
                                            $(this).dialog("close");
                                        }
                                    }
                                });
                                $("#manuListDialog").dialog({
                                    autoOpen: false,
                                    height: 460,
                                    width: 750,
                                    modal: true,
                                    buttons: {
                                        "关闭": function () {
                                            $("#mName").val('');
                                            $(this).dialog("close");
                                        }
                                    }
                                });
                                $("#productCategoryListDialog").dialog({
                                    autoOpen: false,
                                    height: 460,
                                    width: 750,
                                    modal: true,
                                    buttons: {
                                        "关闭": function () {
                                            $("#pCategoryName").val('');
                                            $(this).dialog("close");
                                        }
                                    }
                                });
                                $("#editionListDialog").dialog({
                                    autoOpen: false,
                                    height: 460,
                                    width: 750,
                                    modal: true,
                                    buttons: {
                                        "关闭": function () {
                                            $(this).dialog("close");
                                        }
                                    }
                                });
                            });

                            function showProductList() {
                                $('body').showLoading();
                                $.ajax({
                                    type: "post",
                                    url: "${request.contextPath}/user/addProduct?flawId=" + ${ flaw?.id },
                                    dataType: "html",
                                    success: function (data) {
                                        if (data == "error") {
                                            if (confirm("用户已失效,请重新登录")) {
                                                window.location.href = "${request.contextPath}/user/login"
                                            }
                                        } else if (data == "err") {
                                            alert("操作失败");
                                        } else {
                                            $("#productListDiv").html("").append(data);
                                            $('body').hideLoading();
                                            $("#productListDialog").dialog("open");
                                        }
                                    }
                            })
                        }

                            function delProductList() {
                                $('body').showLoading();
                                $.ajax({
                                    type: "post",
                                    url: "${request.contextPath}/user/viewProduct?productIds=" + $("#productIds").val(),
                                    dataType: "html",
                                    success: function (data) {
                                        if (data == "error") {
                                            if (confirm("用户已失效,请重新登录")) {
                                                window.location.href = "${request.contextPath}/user/login"
                                            }
                                        } else if (data == "err") {
                                            alert("操作失败");
                                        } else {
                                            $("#productCancelDiv").html("").append(data);
                                            $('body').hideLoading();
                                            $("#productCancelDialog").dialog("open");
                                        }
                                    }
                                });
                            }

                            function selectBack() {
                                var editionNameStr = "";
                                $("input[name=productIds]:checked").each(function () {
                                    editionNameStr += $(this).val() + ","

                                });
                                var productIdValue = "";
                                if (editionNameStr != null && editionNameStr != "") {
                                    if ($("#productIds").val() != null) {
                                        productIdValue = editionNameStr.substring(0, editionNameStr.length - 1);
                                    } else {
                                        var productS = $("#productIds").val().split(",");
                                        var strs = editionNameStr.split(",");
                                        for (var i = 0; i < productS.length; i++) {
                                            var bool = false;
                                            for (var j = 0; j < strs.length; j++) {
                                                if (strs[j] != null && strs[j] != "" && productS[i] != strs[j]) {
                                                    bool = true;
                                                }
                                            }
                                            if (bool) {
                                                if (productIdValue == "") {
                                                    productIdValue = productS[i];
                                                } else {
                                                    productIdValue = productIdValue + "," + productS[i];
                                                }
                                            }

                                        }
                                    }
                                }
                                //设置隐藏域产品id的值
                                if ($("#productIds").val() != null && $("#productIds").val() != "") {
                                    $("#productIds").val($("#productIds").val() + "," + productIdValue);
                                } else {
                                    $("#productIds").val(productIdValue);
                                }
                                $.ajax({
                                    type: "post",
                                    url: "${request.contextPath}/user/views",
                                    data: "edition=" + $("#productIds").val(),
                                    async: false,
                                    success: function (data) {
                                        if (data != null) {
                                            var json = $.parseJSON(data);
                                            //设置产品名称的值
                                            $("#productValue").val(json.productName);
                                        }
                                    }
                                });
                                $("#productListDialog").dialog("close");
                            }

                            function productCancel() {
                                var editionNameStr = "";
                                $("input[name=productId]:checked").each(function () {
                                    editionNameStr += $(this).val() + ","

                                });
                                var productIdValue = "";
                                if ($("#productIds").val() != null && editionNameStr != null && editionNameStr != "") {
                                    var productS = $("#productIds").val().split(",");
                                    var strs = editionNameStr.split(",");
                                    for (var i = 0; i < productS.length; i++) {
                                        var bool = true;
                                        for (var j = 0; j < strs.length; j++) {
                                            if (strs[j] != null && strs[j] != "" && productS[i] == strs[j]) {
                                                bool = false;
                                            }
                                        }
                                        if (bool) {
                                            if (productIdValue == "") {
                                                productIdValue = productS[i];
                                            } else {
                                                productIdValue = productIdValue + "," + productS[i];
                                            }
                                        }
                                    }
                                }
                                //设置隐藏域产品id的值
                                $("#productIds").val(productIdValue);
                                if ($("#productIds").val() == "") {
                                    $("#productValue").val("");
                                } else {
                                    $.ajax({
                                        type: "post",
                                        url: "${request.contextPath}/user/views",
                                        data: "edition=" + $("#productIds").val(),
                                        async: false,
                                        success: function (data) {
                                            if (data != null) {
                                                var json = $.parseJSON(data);
                                                //设置产品名称的值
                                                $("#productValue").val(json.productName);
                                            }
                                        }
                                    });
                                }
                                $("#productCancelDialog").dialog("close");
                            }

                        </script>
                    </body>

                    </html>