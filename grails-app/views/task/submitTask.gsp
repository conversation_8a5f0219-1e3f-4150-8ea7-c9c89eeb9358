<%@ page import="com.cnvd.flawInfo.Exploit" %>
<%@ page import="com.cnvd.utils.*" %>
<%@ page import="com.cnvd.flawInfo.ExploitAttachment" %>
<%@ page import="com.cnvd.patchInfo.PatchInfoAttachment" %>
<%@ page import="com.cnvd.patchInfo.PatchInfoAttachmentPdf" %>
<%@ page import="com.cnvd.flawInfo.ExploitAttachmentPdf" %>
<%@ page import="com.cnvd.utils.EncryptUtils" %>
<html>
    <head>
	<script type="text/javascript" src="${request.getContextPath()}/js/jquery-1.8.2.min.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/echarts.min.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/AES.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/rsa/jsbn.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/rsa/jsbn2.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/rsa/prng4.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/rsa/rng.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/rsa/rsa.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/rsa/rsa2.js"></script>
	<link href="${request.getContextPath()}/css/common.css" rel="stylesheet" media="screen" type="text/css" />
	<link href="${request.getContextPath()}/favicon.ico" rel="shortcut icon">
	<link href="${request.getContextPath()}/js/jquery-mega-drop-down-menu/css/skins/white.css" rel="stylesheet" type="text/css" />
	<link href="${request.getContextPath()}/js/jquery-mega-drop-down-menu/css/dcmegamenu.css" rel="stylesheet" type="text/css" />
	<script type='text/javascript' src='${request.getContextPath()}/js/jquery-mega-drop-down-menu/js/jquery.hoverIntent.minified.js'></script>
	<script type='text/javascript' src='${request.getContextPath()}/js/jquery-mega-drop-down-menu/js/jquery.dcmegamenu.1.3.3.js'></script>
	<link href="${request.getContextPath()}/css/pixie.css" rel="stylesheet" media="screen" type="text/css" />
	<link href="${request.getContextPath()}/css/uc.css" rel="stylesheet" media="screen" type="text/css" />
	<link href="${request.getContextPath()}/css/t_tab.css" rel="stylesheet" media="screen" type="text/css" />
	<link href="${request.getContextPath()}/css/jquery-ui-custom.css" rel="stylesheet" media="screen" type="text/css" />
	<link rel="stylesheet" href="${request.contextPath}/css/uploader.css" />
	<link rel="stylesheet" href="${request.contextPath}/css/imgareaselect-default.css" />
	<link href="${request.getContextPath()}/js/showLoading/css/showLoading.css" rel="stylesheet" media="screen" />
	<link rel="stylesheet" type="text/css" href="${request.getContextPath()}/verifyMaster/css/verify.css">
    <script type="text/javascript" src="${request.getContextPath()}/js/showLoading/js/jquery.showLoading.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/jquery.fancybox-1.3.4/ett/jquery.fancybox-1.3.4.pack.js"></script>
	<script type="text/javascript"	src="${resource(dir:'layui', file: 'layui.js')}"></script>
	<script type="text/javascript"	src="${resource(dir:'statics/layui/lay/modules', file: 'laypage.js')}"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/CryptoJS v3.1.2/rollups/aes.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/CryptoJS v3.1.2/components/mode-ecb.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/verifyMaster/js/verify.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/jquery-ui.min-nopacker.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/jquery.validate.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/jquery.metadata.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/jquery.bgiframe-2.1.2.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/jquery.ui.widget.js"></script>
	<script type="text/javascript" src="${request.getContextPath()}/js/jquery.ui.rcarousel.js"></script>
	<script type="text/javascript" src="${request.contextPath}/js/jquery.imgareaselect.min.js"></script>
	<script type="text/javascript" src="${request.contextPath}/js/t_tab.js"></script>
	<link href="${request.getContextPath()}/layui2.6.8/css/layui.css" rel="stylesheet" media="screen" type="text/css">
    </head>
    <body>
		<style>
						.flist .ipt {
							width: 100%;
							height: 40px;
							border-radius: 5px 5px 5px 5px;
							border: 1px solid #E5E8EF;
							padding-left: 20px;
							box-sizing: border-box;
						}
						.flist >p{
							padding-bottom: 3px;
						}
						.flist .required{
							background-position: 100% 14px;
							background-repeat: no-repeat;
						}
						.curLabel {
							font-family: Arial, Arial;
							font-weight: 400;
							font-size: 18px;
							color: #666666;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}
						.flist .txtarea {
							height: 70px;
							max-width: 1000px;
						}
		</style>
		<div id="command_center" class="clf main p_t u_report">

	<div class="i_report">
		<div>
			<div class=" m_h list_wrap">
				<g:form action="saveExploitTask" controller="task" method="post" name="exploitForm" enctype="multipart/form-data">
					<g:hiddenField name="id" value="${taskInstance?.id }"/>
					<g:hiddenField name="isDeleteAtt" />
					<%
						def attIdValue = ""
						def attList = null
						def attachmentPdfList = null
						if(taskInstance.type==Constants.FLAW_EXPOIT){
							attList = ExploitAttachment.executeQuery('select ea.attachment from ExploitAttachment ea where ea.exploit = ?',[taskInstance?.exploit])
							attList.each{
								attIdValue += it.id+";"
							}
							attachmentPdfList = ExploitAttachmentPdf.executeQuery('select ea.attachmentPdf from ExploitAttachmentPdf ea where ea.exploit=?',[taskInstance.exploit])
						}
						if(taskInstance.type==Constants.FLAW_PATCH){
							attList = PatchInfoAttachment.executeQuery('select pia.attachment from DisposalInfoAttachment pia where pia.disposalInfo = ?',[taskInstance?.disposalInfo])
							attList.each{
								attIdValue += it.id+";"
							}
							attachmentPdfList = PatchInfoAttachmentPdf.executeQuery('select pia.attachmentPdf from DisposalInfoAttachmentPdf pia where pia.disposalInfo=?',[taskInstance.disposalInfo])
						}
					 %>
					<g:hiddenField name="attIds" value="${attIdValue}" id="attIdField"/>
					<g:hiddenField name="offset" value="${offset}" />
	              	<div class="flist" style="padding: 10px 20px;">
	              		<g:if test="${taskInstance.type==Constants.FLAW_EXPOIT }">
	              		<p>
							<p class="curLabel">Validation Name<span class="min-cn">验证名称</span></p>
	                  		<g:textField name="exploitName" id="exploitName" class="required ipt" value="${taskInstance?.exploit?.exploitName }"/>
	                  		<p><span id="exploitName_error"></span></p>
	                  	</p>
	              		<p>
	                  		<p class="curLabel">Validation Time<span class="min-cn">验证日期</span></p>
	                  		<g:textField name="exploitTimes" id="exploitTime" readonly="readonly" value="${DateTimeUtil.shortFmt(taskInstance?.exploit?.exploitTime) }" class="required ipt"/>
	                  		<p><span id="exploitTime_error"></span></p>
	                  	</p>

	                   	<p>
							<p class="curLabel">Validation Principle<span class="min-cn">验证原理</span></p>
	                   		<g:textArea name="concept" style="height: 70px;" class="txtarea ipt" value="${taskInstance?.exploit?.concept }"></g:textArea>
			            </p>
	                  	<p>
							<p class="curLabel">Validation POC<span class="min-cn">验证POC</span></p>
	                  		<g:textArea name="poc" style="height: 70px;" class="txtarea ipt" value="${taskInstance?.exploit?.poc }"></g:textArea>
	                  	</p>
	                  	<p>
							<p class="curLabel">Verification Suggestion<span class="min-cn">验证建议</span></p>
	                  		<g:textArea name="suggestion" style="height: 70px;" class="txtarea ipt" value="${taskInstance?.exploit?.suggestion }"></g:textArea>
	                  	</p>
						<p class="curLabel">Verification Attachment<span class="min-cn">验证附件</span></p>
	                  	<div style="float:left;">
	                  		<div id="attSpan">
						  		 <g:if test="${attList}">
									 <%
										 def exploitattid =new String();
										 def exploitattPdfid=new String();
									 %>
						  		 	<g:each in="${attList}" var="attachment">
										<%
											exploitattid = EncryptUtils.aesEncrypt(attachment?.id+"").replaceAll("\\+","%2b")
										%>
						  		 		<span id="attSpan${attachment.id}">
						  		 			<a href="${request.contextPath}/common/download?id=${exploitattid}&cd=${taskInstance?.exploit?.downCode}&type=${taskInstance?.type}" title="附件下载" style="color:blue;height:20px;">
						  		 				${attachment.fileName }
						  		 			</a>&nbsp;&nbsp;

										<g:if test="${attachmentPdfList}">
											<g:each in="${attachmentPdfList}" var="attachmentPdf">
												<%
													exploitattPdfid= EncryptUtils.aesEncrypt(attachmentPdf?.id+"").replaceAll("\\+","%2b")
												%>
												<g:if test="${attachmentPdf?.attachmentId==attachment?.id}">
													<a href="${request.contextPath}/common/downloadPdf?id=${exploitattPdfid}&cd=${taskInstance.exploit.downCode}" title="附件下载" style="color:blue;height:20px;">
														${attachmentPdf?.fileName}
													</a>
												</g:if>
											</g:each>
										</g:if>&nbsp;&nbsp;

						  		 			<a href="javascript:delAttachment('${attachment.id }');">删除</a>
						  		 			<br/>
						  		 		</span>
						  		 	</g:each>
						  		 </g:if>
	                  		</div>
							%{--<input type="file" name="attachmentFile" id="exploitAtt"/>
						   	<div id="queue"></div>--}%
							<div class="layui-upload">
								<button type="button" class="layui-btn layui-btn-normal" id="testList">select file / 选择文件</button>
								<span id="errSpan" style="color: red"></span>
								<div class="layui-upload-list">
									<table class="layui-table">
										<thead>
										<tr><th>文件名</th>
											<th>大小</th>
											<th>状态</th>
											<th>操作</th>
										</tr></thead>
										<tbody id="demoList"></tbody>
									</table>
								</div>
								<button type="button" class="layui-btn" id="testListAction">start upload / 开始上传</button>
							</div>
	                  	</div>
	                  	<div style="clear:both;"></div>
	                  		<%--<input type="file" name="attachmentFile"/>
	                  		<g:if test="${taskInstance.exploit?.attachment!=null }">
								<span id="attachmentDiv">
									<g:link title="下载附件" style="color:blue;" controller="task" action="download" params="[t:'exploit',cd:taskInstance?.downCode]">
										${taskInstance?.exploit?.attachment?.fileName }
									</g:link>
									<a href="javascript:delAtt();">删除</a>
								</span>
							</g:if>
	                	--%>
	                	<script type="text/javascript">
                            //changeDivC(4);
							function closeParentLayer(){
								var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
								setTimeout(function() {
									parent.layer.close(index);
								}, 500);
								parent.layer.close(index);
							}
							$(function() {
								layui.use("upload",function () {
									var $ = layui.jquery
											,upload = layui.upload;
									//多文件列表示例
									var demoListView = $('#demoList')
											,uploadListIns = upload.render({
										elem: '#testList'
										,url: '${createLink(controller: "task",action: "uploadAtt")}' //上传接口
										,accept: 'file'
										,multiple: true
										,auto: false
										,data: {
											'type':'${taskInstance.type}'
										}
										,bindAction: '#testListAction'
										,choose: function(obj){
											var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
											//读取本地文件
											obj.preview(function(index, file, result){
												var tr = $(['<tr id="upload-'+ index +'">'
													,'<td>'+ file.name +'</td>'
													,'<td>'+ (file.size/1024).toFixed(1) +'kb</td>'
													,'<td>等待上传</td>'
													,'<td>'
													,'<button class="layui-btn layui-btn-xs demo-reload layui-hide">重传</button>'
													,'<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>'
													,'</td>'
													,'</tr>'].join(''));

												//单个重传
												tr.find('.demo-reload').on('click', function(){
													obj.upload(index, file);
												});

												//删除
												tr.find('.demo-delete').on('click', function(){
													delete files[index]; //删除对应的文件
													tr.remove();
													$('#errSpan').text("")
													uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
												});

												demoListView.append(tr);
											});
										},
										before: function(obj){ //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
											layer.load(); //上传loading
										}

										,done: function(res, index, upload){
											if(res){ //上传成功
												console.log(res)
												var tr = demoListView.find('tr#upload-'+ index)
														,tds = tr.children();
												tds.eq(2).html('<span style="color: #5FB878;">上传成功</span>');
												tds.eq(3).html(''); //清空操作
												$("#attIdField").val($("#attIdField").val()+""+res.attachmentId+";");
												layer.closeAll('loading'); //关闭loading
												return delete this.files[index]; //删除文件队列已经上传成功的文件
											}
											this.error(index, upload);
										}
										,error: function(index, upload){
											var tr = demoListView.find('tr#upload-'+ index)
													,tds = tr.children();
											tds.eq(2).html('<span style="color: #FF5722;">上传失败</span>');
											tds.eq(3).find('.demo-reload').removeClass('layui-hide'); //显示重传
											$('#errSpan').text("格式不支持或文件内容为空")
											layer.closeAll('loading'); //关闭loading
										}
									});

								})

								 $.datepicker.regional['zh-CN'] =
							     {
									     dayNamesMin: ['日','一','二','三','四','五','六'],
									     monthNames: ['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'],
							          	 monthNamesShort: ['一','二','三','四','五','六','七','八','九','十','十一','十二'],
							            nextText: '', //intentionally blank
							            prevText: '', //intentionally blank
							            numberOfMonths: 1,
							            stepMonths:1,
							            showButtonPanel: false,
							            closeText: '清除已选日期',
							            dateFormat: 'yy-mm-dd',
							            currentText: '今天'
							    };
								$.datepicker.setDefaults($.datepicker.regional['zh-CN']);
								$("#exploitTime").datepicker({
									changeMonth : true,
									changeYear : true
								});
								$("#subForm").click(function(){
									if ($("#exploitForm").valid()) {  // 如果验证通过
											$.ajax({
												url: $("#exploitForm").attr("action"),
												type: "POST",
												data: new FormData($("#exploitForm")[0]),
												processData: false,
												contentType: false,
												success: function(response) {
														parent.layer.msg("提交成功", { icon: 1 });  // 提示成功
														closeParentLayer();  // 关闭弹窗
												},
												error: function(xhr, status, error) {
													parent.layer.msg("请求失败: " + error, { icon: 2 });
												}
											});
										}
						       	});
								$("#exploitForm").validate({
									  errorPlacement: function(error, element) {
										     error.appendTo($("#"+element.attr("id")+"_error"));
										},
										rules: {
											exploitTime:{
												required:true
								 			},
								 			exploitName:{
								 				required:true,
								 				maxlength:255
									  		}
										 },
								 		messages:{
								 			exploitTime:{
								         		required:"请输入验证日期"
								         	},
								         	exploitName:{
								         		required:"请输入验证名称",
								         		maxlength:jQuery.validator.format("验证名称长度最多是 {0} 的字符串")
								         	}
								 		}
								});
							});

						</script>
	                  </g:if>
	                  <g:if test="${taskInstance.type==Constants.FLAW_PATCH }">
	                 	<p>
								<p class="curLabel">Disposal Name<span class="min-cn">处置名称</span></p>
	                  		<g:textField name="patchName" id="patchName" class="required ipt" value="${taskInstance?.disposalInfo?.patchName }"/>
	                  		<p><span id="patchName_error"></span></p>
	                  	</p>
	                   	<p>								
							<p class="curLabel">Disposal Contact<span class="min-cn">处置联系方式</span></p>
	                   		<g:textField name="tel" id="tel" class="required ipt" value="${taskInstance?.disposalInfo?.tel }"/>

	                   		<span id="patchUrl_error"></span>
			            </p>
	                  	<p>
							<p class="curLabel">Other<span class="min-cn">其他</span></p>
	                  		<g:textArea name="remark" class="txtarea ipt" value="${taskInstance?.disposalInfo?.remark }"></g:textArea>
	                  	</p>
	                  	<p>
							<p class="curLabel">Disposal Description<span class="min-cn">处置描述</span></p>
	                  		<g:textArea name="patchDescription" class="txtarea ipt" value="${taskInstance?.disposalInfo?.patchDescription }"></g:textArea>
	                  	</p>
							<p class="curLabel">Disposal Attachment<span class="min-cn">处置附件</span></p>
	                  	<div style="float:left;">
	                  		<div id="attSpan">
						  		 <g:if test="${attList}">

									 <%
										 def patchInfid =new String();
										 def patchInfoPdfid=new String();
									 %>
						  		 	<g:each in="${attList}" var="attachment">
										<%
											patchInfid = EncryptUtils.aesEncrypt(attachment?.id+"").replaceAll("\\+","%2b")
										%>
						  		 		<span id="attSpan${attachment.id}">
						  		 			<a href="${request.contextPath}/common/download?id=${patchInfid}&cd=${taskInstance.disposalInfo.downCode}&type=${taskInstance.type}" title="附件下载" style="color:blue;height:20px;">
						  		 				${attachment.fileName }
						  		 			</a>&nbsp;&nbsp;

										<g:if test="${attachmentPdfList}">
											<g:each in="${attachmentPdfList}" var="attachmentPdf">
												<%
													patchInfoPdfid= EncryptUtils.aesEncrypt(attachmentPdf?.id+"").replaceAll("\\+","%2b")
												%>
												<g:if test="${attachmentPdf?.attachmentId==attachment?.id}">
													<a href="${request.contextPath}/common/downloadPdf?id=${patchInfoPdfid}&cd=${taskInstance.disposalInfo.downCode}&type=${taskInstance.type}" title="附件下载" style="color:blue;height:20px;">
														${attachmentPdf?.fileName}
													</a>
													%{--<g:link title="下载附件" style="color:blue;" controller="common" action="downloadPdf" params="[id:attachmentPdf?.id]">
														${attachmentPdf?.fileName}
													</g:link>--}%
												</g:if>
											</g:each>
										</g:if>&nbsp;&nbsp;
						  		 			<a href="javascript:delAttachment('${attachment.id }');">删除</a>
						  		 			<br/>
						  		 		</span>
						  		 	</g:each>
						  		 </g:if>
	                  		</div>
							<div class="layui-upload">
								<button type="button" class="layui-btn layui-btn-normal" id="testList">Select File / 选择文件</button>
								<span id="errSpan" style="color: red"></span>
								<div class="layui-upload-list">
									<table class="layui-table">
										<thead>
										<tr><th>文件名</th>
											<th>大小</th>
											<th>状态</th>
											<th>操作</th>
										</tr></thead>
										<tbody id="demoList"></tbody>
									</table>
								</div>
								<button type="button" class="layui-btn" id="testListAction">Start Upload / 开始上传</button>
							</div>
							%{--<input type="file" name="attachmentFile" id="patchInfoAtt"/>
						   	<div id="queue"></div>--}%
	                  	</div>
	                  	<div style="clear:both;"></div>

	                  	<%--
	                  	<p>
	                  		<label for="" style="vertical-align: top;" >补丁附件：</label>
	                  		<input type="file" name="attachmentFile"/>
	                  		<g:if test="${taskInstance.patchInfo?.attachment!=null }">
								<span id="attachmentDiv">
									<g:link title="下载附件" style="color:blue;" controller="task" action="download" params="[t:'patchInfo',cd:taskInstance?.downCode]">
										${taskInstance?.patchInfo?.attachment?.fileName }
									</g:link>
									<a href="javascript:delAtt();">删除</a>
								</span>
							</g:if>
	                	</p>
	                  	--%>
	                  	<script type="text/javascript">
							function closeParentLayer(){
								var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
								setTimeout(function() {
									parent.layer.close(index);
								}, 500);
							}
							$(function(){
								layui.use("upload",function () {
									var $ = layui.jquery
											,upload = layui.upload;
									//多文件列表示例
									var demoListView = $('#demoList'),
										uploadListIns = upload.render({
										elem: '#testList'
										,url: '${createLink(controller: "task",action: "uploadAtt")}' //上传接口
										,accept: 'file'
										,multiple: true
										,auto: false
										,data: {
											'type':'${taskInstance.type}'
										}
										,bindAction: '#testListAction'
										,choose: function(obj){
											var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
											//读取本地文件
											obj.preview(function(index, file, result){
												var tr = $(['<tr id="upload-'+ index +'">'
													,'<td>'+ file.name +'</td>'
													,'<td>'+ (file.size/1024).toFixed(1) +'kb</td>'
													,'<td>等待上传</td>'
													,'<td>'
													,'<button class="layui-btn layui-btn-xs demo-reload layui-hide">重传</button>'
													,'<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>'
													,'</td>'
													,'</tr>'].join(''));

												//单个重传
												tr.find('.demo-reload').on('click', function(){
													obj.upload(index, file);
												});

												//删除
												tr.find('.demo-delete').on('click', function(){
													delete files[index]; //删除对应的文件
													tr.remove();
													$('#errSpan').text("")
													uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
												});

												demoListView.append(tr);
											});
										},
										before: function(obj){ //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
											layer.load(); //上传loading
										}
										,done: function(res, index, upload){
											if(res){ //上传成功
												console.log(res)
												var tr = demoListView.find('tr#upload-'+ index)
														,tds = tr.children();
												tds.eq(2).html('<span style="color: #5FB878;">上传成功</span>');
												tds.eq(3).html(''); //清空操作
												$("#attIdField").val($("#attIdField").val()+""+res.attachmentId+";");
												layer.closeAll('loading'); //关闭loading
												//console.log($("#attIdField").val($("#attIdField").val()+""+res.attachmentId+";").val())
												return delete this.files[index]; //删除文件队列已经上传成功的文件
											}
											this.error(index, upload);
										}
										,error: function(index, upload){
											var tr = demoListView.find('tr#upload-'+ index)
													,tds = tr.children();
											tds.eq(2).html('<span style="color: #FF5722;">上传失败</span>');
											tds.eq(3).find('.demo-reload').removeClass('layui-hide'); //显示重传
											layer.closeAll('loading'); //关闭loading
											$('#errSpan').text("格式不支持或文件内容为空")
										}
									});

								})
								$("#subForm").click(function(){
										if ($("#exploitForm").valid()) {  // 如果验证通过
											$.ajax({
												url: $("#exploitForm").attr("action"),
												type: "POST",
												data: new FormData($("#exploitForm")[0]),
												processData: false,
												contentType: false,
												success: function(response) {
														parent.layer.msg("提交成功", { icon: 1 });  // 提示成功
														closeParentLayer();  // 关闭弹窗
												},
												error: function(xhr, status, error) {
													parent.layer.msg("请求失败: " + error, { icon: 2 });
												}
											});
										}
									
						       	});
								$("#exploitForm").validate({
									  errorPlacement: function(error, element) {
										     error.appendTo($("#"+element.attr("id")+"_error"));
										},
										rules: {
											patchName:{
												required:true,
								 				maxlength:255
								 			},
								 			patchUrl:{
								 				required:true,
								 				maxlength:255
									  		}
										 },
								 		messages:{
								 			patchName:{
								         		required:"请输入处置名称",
								         		maxlength:jQuery.validator.format("处置名称 长度最多是 {0} 的字符串")
								         	},
								         	patchUrl:{
								         		required:"请输入处置联系方式",
								         		maxlength:jQuery.validator.format("处置联系方式长度最多是 {0} 的字符串")
								         	}
								 		}
									});
								})
							</script>
	                  	</g:if>
	                  	<g:if test="${taskInstance?.status == Constants.TASK_BACK && taskInstance?.backStr()}">
	                		<p>
		                  		<label for="" style="vertical-align: top;" >驳回原因：</label>
		                  		<font color="red">${taskInstance?.backStr()}</font>
		                  	</p>
	                	</g:if>
					  	<p style="text-align: right;">
							<a href="javascript:void(0);"  onclick="closeParentLayer();" class="layui-btn layui-btn-primary"><span>Cancel / 取消</span></a>
							<a href="javascript:void(0);"  id="subForm" class="layui-btn"><span>Submit / 提交</span></a>
						</p>
				  	</div>

				</g:form>
			</div>
		</div>
	</div>
</div>
<!-- 主体内容结束 -->

	<script type="text/javascript">
		if("${flash.attErr}"){
			alert("${flash.attErr}");
		}
		function delAttachment(attId){
			var reg = new RegExp(attId+";","g");
			$("#attIdField").val($("#attIdField").val().replace(reg,""));
			$("#attSpan"+attId).remove();
		}
		function delAtt(){
			$("#isDeleteAtt").val(1)
			$("#attachmentDiv").hide();
		}
	</script>
    </body>
</html>
