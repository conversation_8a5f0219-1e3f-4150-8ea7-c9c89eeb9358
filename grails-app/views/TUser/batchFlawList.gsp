<%@ page import="com.cnvd.utils.*" %>
	<%@ page contentType="text/html;charset=UTF-8" %>
		<%@ page defaultCodec="html" %>
			<%@ page import="com.cnvd.info.News" %>
				<html>

				<head>
					<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
					<meta name="layout" content="newmain2" />
				</head>

				<body>
					<style type="text/css">
						.layui-layer-shade {
							position: fixed;
							left: 0;
							top: 0;
							width: 100%;
							height: 100%;
							background-color: rgba(0, 0, 0, .3) !important;
							z-index: 19891014;
						}

						.top-nav {
							display: flex;
							margin-top: 15px;
						}

						.mynews-tab {
							padding: 35px 45px;
							display: flex;
							justify-content: space-between;
							align-items: center;
							font-size: 28px;
							color: #333;
						}

						.mynews-tab em {
							font-style: normal;
							font-weight: bold;
							font-size: 24px;
						}

						.table-header th {
							font-weight: bold;
							font-size: 16px;
						}

						.mr20 {
							margin-right: 20px;
						}

						.w200 {
							width: 200px;
						}

						.imgClass {
							width: 50px;
							height: 50px;
						}

						.col-img {
							width: 20px;
						}

						.ipt {
							width: 93%;
							height: 40px;
							border-radius: 5px 5px 5px 5px;
							border: 1px solid #E5E8EF;
							padding-left: 20px;
							box-sizing: border-box;
						}

						.curLabel {
							font-family: Arial, Arial;
							font-weight: 400;
							font-size: 18px;
							color: #666666;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}

						.flist .required {
							background-position: 100% 13px;
							background-repeat: no-repeat;
						}

						.curSelect {
							background-color: #375ab4;
							color: #fff;
							border: 2px solid #375ab4;
						}

						.curSelect a {
							color: #fff !important;
						}

						.layui-tab-title .tab-li {
							display: flex;
							margin-right: 4px;
							border: 1px solid #375ab4;
						}

						.layui-tab .layui-tab-title li a {
							width: 100%;
							display: flex;
						}

						.tab-li a:hover {
							color: #375ab4
						}
					</style>
					<div id="Batch-Add" style="display: none;">
						<div style="padding:16px;">
							<g:form controller="batchFlaw" action="save" method="post" name="batchFlawForm"
								enctype="multipart/form-data">
								<div class=" flist">
									<p style="font-size: 16px;">Vulnerability Zip File <label style="width: 68px;"
											class="min-cn">漏洞zip文件</label></p>
									<input type="file" id="patchFlawFile" name="patchFlawFile" />
									<span>(Select ZIP File / 请选择zip文件)</span>
									<g:if test="${flash.fileLimit_error }">
										<p>
											<font color="red">${flash.fileLimit_error}</font>
										</p>
									</g:if>
									<div
										style="padding-top: 20px;padding-left:20px;font-size: 14px;line-height: 1.5em;margin-bottom: 10px;">
										<p>Please strictly follow the vulnerability reporting Excel template and use the
											CSV template for batch submission of vulnerability information. After
											completing the form, compress the CSV file into a .zip format for upload. A
											single CSV file may contain multiple vulnerability entries. For any
											inquiries, <NAME_EMAIL>.</p>
										<p>请严格参照漏洞报送参考excel模板，并使用漏洞报送csv模板批量提交漏洞信息。填写表后将csv文件压缩为.zip格式上传即可。可使用一个csv文件报送多个漏洞信息，如有疑问，可联系********************邮箱。
										</p>
										<br />
										<br />
										<div style="font-size: 19px;"><a style="color: blue;"
												href="${request.contextPath}/webinfo/downloadBSMB">Submission Template
												Download /报送模板下载</a></div>
										<div style="font-size: 19px;"><a style="color: blue;"
												href="${request.contextPath}/webinfo/downloadCKMB">Reference Template
												Download / 参考模板下载</a></div>
									</div>
									<p style="text-align: end;"><a href="javascript:void(0);" id="subForm"
											class="layui-btn"><span>Submit / 提交</span></a></p>
								</div>
							</g:form>
						</div>
					</div>
					<div id="command_center" class="clf main p_t u_comment"
						style="background-color: rgb(241, 242, 246);">
						<g:render template="/usermenu/usermenutemp2"></g:render>

						<div class="info i_report">
							<div class="layui-panel" style="padding: 15px;border-radius: 20px;">
								<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
									<ul class="layui-tab-title"
										style="border-bottom: 4px solid #375ab4; font-weight: bold;display: flex;padding-bottom: 2px;">
										<li class="tab-li" style="width: 185px; margin-right: 4px;">
											<a href="${request.getContextPath()}/user/reportManage">
												<span style="font-size: 16px;">Obtain CNVD</span>
												<p class="min-cn" style="margin-left:2px">CNVD 编号获取</p>
											</a>
										</li>
										<li class="tab-li curSelect" style="width: 292px;">
											<a href="${request.getContextPath()}/user/batchFlawList">
												<span style="font-size: 16px;">Batch Vulnerability Reporting</span>
												<p class="min-cn" style="margin-left:2px">批量漏洞上报</p>
											</a>
										</li>
									</ul>
								</div>
								<div class="box">
									<div class="box e5 m1015 m_h list_wrap" id="commentDiv"
										style="padding-bottom: 26px;">
										<button type="button" class="layui-btn layui-bg-blue"
											onclick="opendialog('add')">Batch Vulnerability Reporting / 批量漏洞上报</button>
										<table class="tlist layui-table">
											<thead>
												<tr class="table-header">
													<th>NO<span class="min-cn">/序号</span></th>
													<th>CNVD Number<span class="min-cn">/上报者</span></th>
													<th>Reporting Time<span class="min-cn">/上报时间</span></th>
													<th>Vulnerability Title<span class="min-cn">/上报文件</span></th>
													<th>Status<span class="min-cn">/文件状态</span></th>
												</tr>
											</thead>
											<g:if test="${batchFlawList}">
												<tbody>
													<g:each in="${batchFlawList}" status="i" var="batchFlaw">
														<tr>
															<td>
																${i+1}
															</td>

															<td>
																${batchFlaw?.tuser?.nickName}
															<td>
																<g:formatDate date="${batchFlaw?.dateCreated}"
																	format="yyyy-MM-dd HH:mm:ss" />
															</td>
															<td>
																<g:link controller="batchFlaw" action="download"
																	params="[cd:batchFlaw.downCode]">
																	${batchFlaw?.attachment?.fileName}
																</g:link>
															</td>
															<td>
																<g:if test="${batchFlaw?.status==0}">漏洞未录入</g:if>
																<g:if test="${batchFlaw?.status==1}">漏洞已入库</g:if>
															</td>
														</tr>
													</g:each>
												</tbody>
											</g:if>
										</table>
										<div class="pages" style="height: 20px;">
											<g:paginate total="${counts}" /><span>共&nbsp;${counts}&nbsp;条</span>
										</div>


									</div>
								</div>
								<div id="flawTrackDialog" class="adv" title="漏洞跟踪结果" style="display:none;">

								</div>
							</div>

						</div>
					</div>
					<g:if test="${flash.message}">
						<script type="text/javascript">
							alert("${flash.message}");
							window.location.href = "${request.contextPath}/user/batchFlawList"
						</script>
					</g:if>
					<g:if test="${flash.fileLimit_error}">
						<script type="text/javascript">
							// 设置标志，在layui加载完成后打开弹窗
							window.shouldOpenErrorDialog = true;
						</script>
					</g:if>
					<script src="${request.getContextPath()}/laydate/laydate.js" charset="utf-8"></script>
					<script src="${request.getContextPath()}/layui/layui.js" charset="utf-8"></script>

					<script type="text/javascript">
						changeDivC(6);
						var form

						layui.use(['form', 'util'], function () {
							form = layui.form;
							var util = layui.util;
							// 渲染表单
							form.render();
							laydate.render({
								elem: '#examine-time-id',
								trigger: 'click'
							});
							// 设置默认值（如果需要）
						});
						$("#flawTrackDialog").dialog({
							autoOpen: false,
							height: 360,
							width: 750,
							modal: true,
							buttons: {
								"关闭": function () {
									$(this).dialog("close");
								}
							}
						});
						function validateFileType() {
							var fileInput = document.getElementById('patchFlawFile');
							var file = fileInput.files[0];

							if (!file) {
								alert("Please select a ZIP file / 请选择ZIP文件");
								return false;
							}

							// 双重验证
							var allowedExtensions = /(\.zip)$/i;
							if (!allowedExtensions.exec(file.name) ||
								(file.type && !['application/zip', 'application/x-zip-compressed'].includes(file.type))) {
								alert("Only ZIP files are allowed / 只允许上传ZIP格式文件");
								fileInput.value = '';
								return false;
							}
							return true;
						}
						$("#subForm").click(function () {
							if (!validateFileType()) {
								return false;
							}
							$("#batchFlawForm").submit();
							// location.reload();
						});
						$("#addAssetForm").validate({
						})
						function opendialog(type) {
							if (type == 'add') {
								layer.open({
									type: 1,
									title: 'Batch Vulnerability Reporting / 批量上报漏洞', // 可选
									content: $('#Batch-Add'), // 捕获的元素
									area: ['750px', '450px'],
									offset: '200px',
									end: function () {
										$("#description").val("");
										console.log('弹窗关闭');
									}
								});
							}

						}

						// 检查是否需要打开错误弹窗
						if (window.shouldOpenErrorDialog) {
							opendialog('add');
							window.shouldOpenErrorDialog = false;
						}
					</script>
				</body>

				</html>