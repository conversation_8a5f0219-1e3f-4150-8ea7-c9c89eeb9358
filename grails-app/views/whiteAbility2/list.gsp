<%@ page import="com.cnvd.utils.*"%>
<%@ page defaultCodec="none"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="layout" content="main" />
<link href="${request.getContextPath()}/css/main.css" rel="stylesheet" media="screen" type="text/css" />
<link href="${request.getContextPath()}/css/warning.css" rel="stylesheet" media="screen" type="text/css" />
<script type="text/javascript" src="${request.getContextPath()}/js/jquery.tablegroup.js"></script>
<style>
.imgClass img {
	height: 30;
}
.mygroups td{
	color: red;
}
</style>
</head>
<body>
	<div class="mw path">
		当前位置：<a href="${request.contextPath}/">首页</a> &gt;
		<g:if test="${type == '1'}">热点推荐</g:if>
		<g:elseif test="${type == '2'}">热点新闻</g:elseif>
		<g:elseif test="${type == '3'}">成员动态</g:elseif>
		<g:elseif test="${type == '4'}">周报</g:elseif>
		<g:elseif test="${type == '5'}">月报</g:elseif>
		<g:elseif test="${type == '6'}">知识库</g:elseif>
		<g:elseif test="${type == '7'}">CNVD简介</g:elseif>
		<g:elseif test="${type == '8'}">政府部门</g:elseif>
		<g:elseif test="${type == '10'}">CNVD技术组</g:elseif>
		<g:elseif test="${type == '11'}">CNVD用户组</g:elseif>
		<g:elseif test="${type == '12'}">CNVD秘书处</g:elseif>
		<g:elseif test="${type == '13'}">CNVD合作方</g:elseif>
		<g:elseif test="${type == '14'}">安全公告</g:elseif>
	</div>

	<!-- 主体内容开始 -->
	<div class="mw Main clearfix">
		<div class="blkContainer">
			<div class="blkContainerPblk">
				<table class="tlist">
					<tbody>
						<g:each in="${whiteAbilityList}" status="i" var="whiteAbilityInstance">
							<tr class="${(i % 2) == 0 ? 'current' : ''}">
								<td width="30%" class="imgClass">
									${whiteAbilityInstance.tuser.nickName}
								</td>
								<td  class="imgClass">
									${whiteAbilityInstance.rank}
								</td>
								<td width="30%" >
									<a onclick="show(${whiteAbilityInstance.id})">能力象限展示</a>
								</td>
							</tr>
						</g:each>
					</tbody>
				</table>
				<div class="pages clearfix">
					<g:paginate total="${counts }" params="${params }" />
					<span>共&nbsp;${counts}&nbsp;条
					</span>
				</div>
				<div class="content_line"></div>
			</div>
		</div>
		<div class="sidebar">
			<g:render template="/layouts/rightcontent"></g:render>
		</div>
	</div>
	<g:if test="${type == '11'}">
		<script type="text/javascript">
			$(function(){
				$(".tlist").tableGroup({groupColumn: 3, groupClass: 'mygroups',useNumChars:0})
			});
		</script>
	</g:if>
	<!-- 主体内容结束 -->
	<script type="text/javascript">
		$(function(){

			 $(".closed").click(function(){
			     $(".zz").hide();
				 $(".warning").hide();
			});
			$(".zz").click(function(e){		
						$(this).hide();
					    $(".warning").hide();
			})
			
			$.datepicker.regional['zh-CN'] ={
				dayNamesMin: ['日','一','二','三','四','五','六'],
				monthNames: ['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'],
				monthNamesShort: ['一','二','三','四','五','六','七','八','九','十','十一','十二'],
				nextText: '', //intentionally blank
				prevText: '', //intentionally blank
				numberOfMonths: 1,
				stepMonths:1,
				showButtonPanel: false,
				//closeText: Translations.clear_dates,
				//currentText: Translations.today
				closeText: '清除已选日期',
				dateFormat: 'yy-mm-dd',
				currentText: '今天'
			};
	  		$.datepicker.setDefaults($.datepicker.regional['zh-CN']); 
		  	$('#startDate').datepicker({
		  		changeMonth : true,
		  		changeYear : true
		  	});
		  	$('#endDate').datepicker({
		  		changeMonth : true,
		  		changeYear : true
		  	});
	  	});	
		
		function show(id){
			$.ajax({
				type:"post",
				dataType:'json',
				url:"${request.contextPath}/whiteAbility/show",
				data:"id="+id,
				success:function(data){
					var aa = Math.ceil(data.webPoints*1.2)+10;
					var bb = Math.ceil(data.shebeiPoints*1.2)+10;
					var cc = Math.ceil(data.eventPoints*1.2)+10;
					var dd = Math.ceil(data.nixiangPoints*1.2)+10;
					$(".zz").show();
					$(".warning").show();
					var myChart = echarts.init(document.getElementById('mychart'));
					   var option = {
					    title : {
					        text: '',
					        subtext: ''
					    },
					    tooltip : {
					        trigger: 'item'
					    },
					    legend: {
					        orient : 'vertical',
					        x : 'right',
					        y : 'bottom',
					        data:['能力象限','能力象限']
					    },
					    toolbox: {
					        show : true,
					        feature : {
					            mark : {show: true},
					            dataView : {show: true, readOnly: false},
					            restore : {show: true},
					            saveAsImage : {show: true}
					        }
					    },
					    polar : [
					       {
					           indicator : [
					               { text: 'WEB/CMS漏洞积分', max: aa},
					               { text: '设备漏洞积分', max: bb},
					               { text: '事件型漏洞积分', max: cc},
					               { text: '软硬件逆向分析漏洞积分', max: dd}
					               
					            ]
					        }
					    ],
					    calculable : true,
					    series : [
					        {
					            name: '白帽子能力象限',
					            type: 'radar',
					            data : [
					                {
					                    value : [data.webPoints, data.shebeiPoints, data.eventPoints, data.nixiangPoints],
					                    name : '积分详情'
					                }
					            ]
					        }
					    ]
					};
					myChart.setOption(option);
					//条形图

					if(data.level==0){
                        $(".triangleArea").css("left","221px");
                        $(".triangle").css("border-top","20px solid #f00");
                        $(".triangleArea").find("h3").html("未评级");
					}else if(data.level==4){
						$(".triangleArea").css("left","325px");
                        $(".triangle").css("border-top","20px solid orange");
                        $(".triangleArea").find("h3").html("无");
					}else if(data.level==3){
						$(".triangleArea").css("left","431px");
                        $(".triangle").css("border-top","20px solid yellow");
                        $(".triangleArea").find("h3").html("中");
					}else if(data.level==2){
						$(".triangleArea").css("left","538px");
                        $(".triangle").css("border-top","20px solid #5cc85c");
                        $(".triangleArea").find("h3").html("良");
					}else if(data.level==1){
						$(".triangleArea").css("left","641px");
                        $(".triangle").css("border-top","20px solid green");
                        $(".triangleArea").find("h3").html("优");
					}
					
				}
			});
		}
	  </script>
	 
	<!-- 图层 -->
	<div id="" class="zz"  style="display:none;"></div>
	<div id="warning" class="warning"  style="display:none;height:550px;">
	   <div id="" class="warn-head">
		<h4>能力象限展示</h4>
		<span class="closed">&times;</span>
	   </div>
	   <div id="" class="warn-body" >
		    <div id="mychart" style="width:500px;height:400px;margin:10px auto"></div>
	   </div>
	   <div id="ul1">
	       <div class="triangleArea">
	           <h3>未评级</h3>
	           <div  class="triangle"></div>
	       </div>
	       <span>能力等级:</span>
	       <span></span>
	       <span></span>
	       <span></span>
	       <span></span>
	   </div>
	</div>
</body>

</html>