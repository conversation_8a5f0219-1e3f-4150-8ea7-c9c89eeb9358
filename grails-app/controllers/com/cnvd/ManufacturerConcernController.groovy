package com.cnvd

import com.cnvd.productInfo.Manufacturer

class ManufacturerConcernController {

    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]

    def index = {
        redirect(action: "list", params: params)
    }
	def listByStartWord = {
		println "params.startWord="+params.startWord
		if(session.user && params.startWord){
			def hql = "from Manufacturer m where m.name like ? and not exists(select id from "+
				" ManufacturerConcern where user = ? and manufacturer.id = m.id)"
			def manuList = Manufacturer.executeQuery(hql,[params.startWord+"%",session.user])
			//def manuList = Manufacturer.findAllByNameLike(params.startWord+"%")
			render(view:"/TUser/manuConcernList",model:[manuList:manuList])
		}else{
			render(view:"/error")
		}
	}
	
	/**
	 * 添加关注的厂商
	 */
	def addConcernManu = {
		if(!session.user){
			session.originalRequestParams = [controller:"user",action:"myconcerns"]
			render "fail"
			return
		}
		println "params.manuIdStr="+params.manuIdStr
		String[] manuIdArr = params.manuIdStr.split(";")
		for(String manuId : manuIdArr){
			def manufacturer = Manufacturer.get(Integer.valueOf(manuId))
			def manufacturerConcern = new ManufacturerConcern()
			manufacturerConcern.manufacturer = manufacturer
			manufacturerConcern.user = session.user
			manufacturerConcern.save(flush:true);
		}
		render "success"
	}
	
	/**
	 * 取消关注的厂商
	 */
	def cancelConcernManu = {
		if(!session.user){
			session.originalRequestParams = [controller:"user",action:"myconcerns"]
			render "fail"
			return
		}
		println "params.manuIdStr="+params.manuIdStr
		String[] manuIdArr = params.manuIdStr.split(";")
		for(String manuId : manuIdArr){
			def manufacturer = Manufacturer.get(Integer.valueOf(manuId))
			def manufacturerConcern = ManufacturerConcern.findByUserAndManufacturer(session.user,manufacturer)
			if(manufacturerConcern){
				manufacturerConcern.delete()
			}
		}
		render "success"
	}

	public static void main(String[] args) {
		println "2343"
	}
}
