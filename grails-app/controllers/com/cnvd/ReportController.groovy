package com.cnvd

import java.io.FileOutputStream;
import com.cnvd.utils.ReportUtil;
import java.text.SimpleDateFormat;

class ReportController {

	def grailsApplication
	def dataSource
	def exportHighRiskVulnerabilitiesXML = {
		String filePath = "${grailsApplication.config.filePath.highRiskVulnerabilitiesXMLFilePath}"
		println filePath
		SimpleDateFormat sf=new SimpleDateFormat("yyyy-MM-dd");
		Date startTime=new Date()
		Date endTime=new Date()
		if(params.startDate && params.startDate){
			startTime=sf.parse(params.startDate)
			endTime=sf.parse(params.endDate)
		}else{
			Calendar cal = Calendar.getInstance()
			def dateStr = cal.get(Calendar.YEAR)+"-"+cal.get(Calendar.MONTH)+"-"+cal.get(Calendar.DATE)
			cal.setTime(sf.parse(dateStr))
			startTime = cal.getTime()
			cal.set(Calendar.MONTH,cal.get(Calendar.MONTH)+1);
			endTime = cal.getTime()
		}
		def sql = "select f.id, f.title, f.number, dic.name,di.description,f.reference_link,"+
				"concat('http://www.cnvd.org.cn/flaw/show/',f.number) as 'CNVD_URL' from flaw f "+
				" left join dictionary_info dic on f.serverity_id =dic.id left join detailed_info "+
				"di on f.detailed_info_id=di.id where dic.name like '%高%' and f.storage_time < ? "+
				"and storage_time >= ?"
		def paramList = new ArrayList();
		paramList.add(endTime+1);
		paramList.add(startTime);
		def list = new groovy.sql.Sql(dataSource).rows(sql,paramList)
		def file=ReportUtil.createHighRiskVulnerabilitiesXML(list,dataSource,filePath)
		response.setHeader('Content-disposition', 'attachment;filename=highRiskVulnerabilities.xml')
		//response.setHeader('Content-length', "${file.size()}")

		OutputStream out = new BufferedOutputStream(response.outputStream)
		try {
			out.write(file.bytes)

		} finally {
			out.close()
			return false
		}
	}
}
