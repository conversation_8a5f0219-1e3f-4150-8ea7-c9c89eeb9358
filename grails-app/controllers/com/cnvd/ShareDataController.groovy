package com.cnvd

import cnvd.ShareXml

import java.text.SimpleDateFormat
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.cnvd.common.Attachment;

import com.cnvd.utils.DateTimeUtil
import com.cnvd.Flaw
import java.text.SimpleDateFormat;
import java.util.Date;
import com.cnvd.patchInfo.PatchInfo
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.output.XMLOutputter;
import org.springframework.aop.aspectj.RuntimeTestWalker.ThisInstanceOfResidueTestVisitor;
import com.cnvd.flawInfo.ReferenceInfo
import com.cnvd.flawInfo.ReferenceType
import com.cnvd.flawInfo.DictionaryInfo
import com.cnvd.flawInfo.FlawProduct

class ShareDataController {

	def grailsApplication
    def list = { 
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			redirect(controller:"TUser",action:"login")
			return
		}
		params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0
		def criteria = ShareXml.createCriteria()
		def shareXmlInstanceList = criteria.list{
			and{
				eq("enable",1)
				if(params.name){
					like('name','%'+params.name.trim()+'%')
				}
				if(params.startDate){
					ge('dateCreated', new SimpleDateFormat("yyyy-MM-dd").parse(params.startDate))
				}
				if(params.endDate){
					lt('dateCreated', new SimpleDateFormat("yyyy-MM-dd").parse(params.endDate)+1)
				}
			}
			maxResults(params.max)
			firstResult(params.offset)
			order("dateCreated","desc")
		}
		def countCriteria = ShareXml.createCriteria()
		def shareXmlTotal = countCriteria.get{
			and{
				eq("enable",1)
				if(params.name){
					like('name','%'+params.name.trim()+'%')
				}
				if(params.startDate){
					ge('dateCreated', new SimpleDateFormat("yyyy-MM-dd").parse(params.startDate))
				}
				if(params.endDate){
					lt('dateCreated', new SimpleDateFormat("yyyy-MM-dd").parse(params.endDate)+1)
				}
			}
			projections { rowCount() }
		}
		
		[shareXmlInstanceList:shareXmlInstanceList,shareXmlTotal:shareXmlTotal]
	}
	
	/**
	 * 下载附件
	 */
	def download = {
		def shareXmlInstance = ShareXml.get(params.id)
		if (!shareXmlInstance) {
			render(view:"/error")
			return
		}
		this.downloadAttXML(shareXmlInstance,request,response)
	}
	
	/**
	 * 下载附件
	 * @param att
	 * @param request
	 * @param response
	 * @return
	 */
	def downloadAttXML(ShareXml att,HttpServletRequest request,HttpServletResponse response){
		request.setCharacterEncoding("UTF-8");
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;

		String downLoadPath = att.path;
		long fileLength = new File(downLoadPath).length();
		def browserType = request.getHeader("User-Agent")
		response.setContentType("application/octet-stream");
		byte[] bytes = browserType.contains("MSIE") ? att.name.getBytes() : att.name.getBytes("utf-8");
		response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
		response.setHeader("Content-Length", String.valueOf(fileLength));

		bis = new BufferedInputStream(new FileInputStream(downLoadPath));
		bos = new BufferedOutputStream(response.getOutputStream());
		byte[] buff = new byte[2048];
		int bytesRead;
		while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
			bos.write(buff, 0, bytesRead);
		}
		bis.close();
		bos.close();
	}
	
	/**
	 * 手动运行xml生成
	 */
	def createXml = {
		String path = "${grailsApplication.config.filePath.shareXml}";
		SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
		//初始时间
		String theDateStr = '2017-04-17'
		Date theDate = formatDate.parse(theDateStr)
		
		
		while(theDate.after(formatDate.parse('2015-01-01'))){
			//开始时间
			Calendar cal = Calendar.getInstance();
			cal.setTime(theDate);
			cal.add(Calendar.DATE, -7);
			theDate = cal.getTime()
			//截止时间
			Calendar calEnd = Calendar.getInstance();
			calEnd.setTime(theDate);
			calEnd.add(Calendar.DATE, 6);
			Date endDate = calEnd.getTime()
			//创建时间
			Calendar calFlag = Calendar.getInstance();
			calFlag.setTime(theDate);
			calFlag.add(Calendar.DATE, 7);
			Date flagDate = calFlag.getTime()
			println "开始时间"+formatDate.format(theDate)+"--->截止时间"+formatDate.format(endDate)+"---任务创建时间"+formatDate.format(flagDate)
			String weekStartDateStr = formatDate.format(theDate)
			String weekEndDateStr = formatDate.format(endDate)
			try {
				String hql = "from Flaw where status =9  and enable = 1 and openTime<= '"+weekEndDateStr+"' and openTime>= '"+weekStartDateStr+"' and isOpen=1 and parentFlaw is null order by dateCreated "
				def flawList =  Flaw.executeQuery(hql)
				Element vulnerabilitys = new Element("vulnerabilitys");
				for(Flaw flawInstance : flawList){
					PatchInfo p = PatchInfo.findByFlawAndEnable(flawInstance,1)
					Element vulnerability = new Element("vulnerability");
					//cnvd编号
					if(flawInstance.number){
						Element number = new Element("number");
						number.setText(flawInstance.number)
						vulnerability.addContent(number)
					}
					//bid
					def bidList = ReferenceInfo.findAllByFlawAndReferenceType(flawInstance,ReferenceType.get(2))
					if(bidList){
						Element bids = new Element("bids");
						for(def bidInstance : bidList){
							Element bid = new Element("bid");
							if(bidInstance.referenceNumber){
								Element bidNumber = new Element("bidNumber");
								bidNumber.setText(bidInstance.referenceNumber)
								bid.addContent(bidNumber)
							}
							if(bidInstance.linkUrl){
								Element bidUrl = new Element("bidUrl");
								bidUrl.setText(bidInstance.linkUrl)
								bid.addContent(bidUrl)
							}
							bids.addContent(bid)
						}
						if(!bids.value.equals("")){
							vulnerability.addContent(bids)
						}
					}
					//cve
					def cveList = ReferenceInfo.findAllByFlawAndReferenceType(flawInstance,ReferenceType.get(1))
					if(cveList){
						Element cves = new Element("cves");
						for(def cveInstance : cveList){
							Element cve = new Element("cve");
							if(cveInstance.referenceNumber){
								Element cveNumber = new Element("cveNumber");
								cveNumber.setText(cveInstance.referenceNumber)
								cve.addContent(cveNumber)
							}
//							Element cveNumber = new Element("cveNumber");
//							cveNumber.setText(cveInstance.referenceNumber)
//							cve.addContent(cveNumber)
							if(cveInstance.linkUrl){
								Element cveUrl = new Element("cveUrl");
								cveUrl.setText(cveInstance.linkUrl)
								cve.addContent(cveUrl)
							}
							cves.addContent(cve)
						}
						if(!cves.value.equals("")){
							vulnerability.addContent(cves)
						}
					}
	
					//标题
					if(flawInstance.title){
						Element title = new Element("title");
						title.setText(flawInstance.title)
						vulnerability.addContent(title)
					}
					//危害等级
					if(flawInstance?.serverityId){
						def severityDi=DictionaryInfo.get(flawInstance?.serverityId);
						Element serverity = new Element("serverity");
						serverity.setText(severityDi.name)
						vulnerability.addContent(serverity)
					}
					//影响产品
					def flawProductList = FlawProduct.findAllByFlaw(flawInstance)
					if(flawProductList){
						Element products = new Element("products");
						for(def flawPrductInstance : flawProductList){
							Element product = new Element("product");
							product.setText(flawPrductInstance.product.name)
							products.addContent(product)
						}
						if(!products.value.equals("")){
							vulnerability.addContent(products)
						}
					}
	
					//是否为事件型漏洞
					if(flawInstance.isEvent == 0){
						Element isEvent = new Element("isEvent");
						isEvent.setText("通用软硬件漏洞")
						vulnerability.addContent(isEvent)
					}else if(flawInstance.isEvent == 1){
						Element isEvent = new Element("isEvent");
						isEvent.setText("事件型漏洞")
						vulnerability.addContent(isEvent)
					}
					//报送时间
					if(flawInstance.submitTime){
						Element submitTime = new Element("submitTime");
						submitTime.setText(formatDate.format(flawInstance.submitTime))
						vulnerability.addContent(submitTime)
					}
					//公开时间时间
					if(flawInstance.openTime){
						Element openTime = new Element("openTime");
						openTime.setText(formatDate.format(flawInstance.openTime))
						vulnerability.addContent(openTime)
					}
					//漏洞发现者
					if(flawInstance.discovererName){
						Element discovererName = new Element("discovererName");
						discovererName.setText(flawInstance.discovererName)
						vulnerability.addContent(discovererName)
					}
					//参考链接
					if(flawInstance.referenceLink){
						Element referenceLink = new Element("referenceLink");
						referenceLink.setText(flawInstance.referenceLink)
						vulnerability.addContent(referenceLink)
					}
					//解决方案
					if(flawInstance?.detailedInfo?.formalWay){
						Element formalWay = new Element("formalWay");
						formalWay.setText(flawInstance?.detailedInfo?.formalWay)
						vulnerability.addContent(formalWay)
					}
					//漏洞描述
					if(flawInstance?.detailedInfo?.description){
						Element description = new Element("description");
						description.setText(flawInstance?.detailedInfo?.description)
						vulnerability.addContent(description)
					}
					//补丁名称
					if(p&&p.patchName){
						Element patchName = new Element("patchName");
						patchName.setText(p.patchName)
						vulnerability.addContent(patchName)
					}
					//补丁描述
					if(p&&p.patchDescription){
						Element patchDescription = new Element("patchDescription");
						String pd1 = p.patchDescription.replaceAll("CNVD确认并复现所述情况，", "")
						String pd2 = pd1.replaceAll("CNVD确认并复现所述情况,", "")
						String pd3 = pd2.replaceAll("CNVD未直接复现所述情况，", "")
						String pd4 = pd3.replaceAll("CNVD未直接复现所述情况,", "")
						patchDescription.setText(pd4)
						vulnerability.addContent(patchDescription)
					}
	
					vulnerabilitys.addContent(vulnerability)
				}
				//输出xml
				Document document = new Document(vulnerabilitys);
				XMLOutputter out = new  XMLOutputter();
	
				String subFilePath = path;
				File file = new File(subFilePath);
				if(!file.exists()){
					file.mkdirs();
				}
				//写到指定的文件
				FileOutputStream fos = new FileOutputStream(path+"/"+weekStartDateStr+"_"+weekEndDateStr+".xml");
				out.output(document, fos);
				fos.close();
				//创建共享数据类
				String names = weekStartDateStr+"_"+weekEndDateStr+".xml"
				def shareXml = ShareXml.findByNameAndEnable(names,1)
				if(!shareXml){
					shareXml = new ShareXml()
				}
				shareXml.dateCreated = flagDate
				shareXml.lastUpdated = flagDate
				shareXml.name = weekStartDateStr+"_"+weekEndDateStr+".xml"
				shareXml.path = path+"/"+weekStartDateStr+"_"+weekEndDateStr+".xml"
				shareXml.save(flush:true)
			} catch (Exception e) {
				e.printStackTrace()
			}finally {
	//			isOver = true
			}
			
		}
		return
		
	}
}
