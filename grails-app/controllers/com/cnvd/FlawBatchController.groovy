package com.cnvd

import com.cnvd.utils.POIExcelUtil

import java.text.SimpleDateFormat

class FlawBatchController {
    def attachmentService
    def track = {
        if (!session.user) {
            render "error"
        }
        def flawBatchInstance = FlawBatch.get(params.int('flawBatchId'))
        if (flawBatchInstance && flawBatchInstance?.user?.id == session?.user?.id) {
            def flawAuditing = FlawAuditing.findAllByFlawBatch(flawBatchInstance)
            def auditlist = new ArrayList<FlawAuditing>()
            auditlist.addAll(flawAuditing)
            [flawAuditingList: auditlist]
        } else {
            render "err"
        }
    }

    // 根据id进行查询回显，对应回显页面
    def edit = {
        /**
         * 查询出当前登录用户所上报的漏洞列表
         */
        if (!session.user) {
            // 去登录
            session.originalRequestParams = [controller: controllerName, action: actionName]
            return redirect(controller: "TUser", action: "login")
        }
        def flawBatch = FlawBatch.get(params.getLong("id"))
        [flawBatch: flawBatch]
    }
    // 回显之后进行修改,更新操作,然后重定向到列表页
    def update = {
        withForm {
            //printf (params.toString())

            if (!session.user) {
                session.originalRequestParams = [controller: controllerName, action: actionName]
                redirect(controller: "TUser", action: "login")
                return
            }

            def flawBatch = FlawBatch.get(params.id)
            if (!flawBatch) {
                render(view: "/error")
                return
            }
            flawBatch.user = session.user

            if(!params.myCode){
                def myCode_error = "请输入验证码"
                render(view:"edit",model:[myCode_error:myCode_error])
                return
            }
            if (params.myCode.toLowerCase()!=session.validateCode){
                def myCode_error = '验证码不正确'
                render(view:"edit",model:[myCode_error:myCode_error])
                return
            }
            if (!params.title) {
                def title_error = "漏洞标题不能为空"
                render(view: "edit", model: [title_error: title_error, flawBatch: flawBatch])
                return
            }
            if (!params.flawTypes) {
                def flawTypes_error = "漏洞类型不能为空"
                render(view: "edit", model: [flawTypes_error: flawTypes_error, flawBatch: flawBatch])
                return
            }
            if (!params.flawTypes) {
                def flawTypes_error = "漏洞类型不能为空"
                render(view: "edit", model: [flawTypes_error: flawTypes_error, flawBatch: flawBatch])
                return
            }

            if (!params.discovererName) {
                def discovererName_error = "发现者姓名不能为空"
                render(view: "edit", model: [discovererName_error: discovererName_error, flawBatch: flawBatch])
                return
            }

            if (!params.foundTime) {
                def foundTime_error = "发现日期不能为空"
                render(view: "edit", model: [foundTime_error: foundTime_error, flawBatch: flawBatch])
                return
            }
            //上传漏洞附件并设置flawBatchInstance的attachment属性
            def excelFile = request.getFile("excelFile")
            if (!excelFile) {
                def excel_fileLimit_error = "请上传Excel附件,类型为.xlsx或.xls"
                render(view: 'edit', model: [flawBatch: flawBatch, excel_fileLimit_error: excel_fileLimit_error])
                return
            } else {
                //判断上传的附件的格式及大小
                def flag = attachmentService.checkExcelFile(excelFile)
                if (flag) {
                    //附件符合格式及大小限制
                    String filePath = "${grailsApplication.config.filePath.flawBatchFilePath}"
                    String realName = new Date().getTime() //文件的真实文件名
                    def attachment = attachmentService.uploadFile(excelFile, filePath, realName)
                    flawBatch.attachment = attachment

                    String fileName = excelFile.getOriginalFilename()
                    fileName = fileName.substring(fileName.lastIndexOf("."));
                    String fileUrlStr = filePath + realName + fileName
                    def poi = new POIExcelUtil(fileUrlStr)
                    def num = poi.getExcelFirstSheetRows()//得到excel总行数:包括表头
                    flawBatch.num = num - 1//flawBatch.num值：去掉表头的总行数
                } else {
                    //附件不符合格式或大小限制
                    def excel_fileLimit_error = "附件类型不是.xlsx/.xls或大小超出10MB"
                    render(view: 'edit', model: [flawBatch: flawBatch, excel_fileLimit_error: excel_fileLimit_error])
                    return
                }
            }
            def pocFile = request.getFile("pocFile")
            if (!pocFile) {
                def poc_fileLimit_error = "请上传POC附件,类型为.zip"
                render(view: 'edit', model: [flawBatch: flawBatch, poc_fileLimit_error: poc_fileLimit_error])
                return
            } else {
                //判断上传的附件的格式及大小
                def flag = attachmentService.checkZipFile(pocFile)
                if (flag) {
                    //附件符合格式及大小限制
                    String filePath = "${grailsApplication.config.filePath.flawBatchFilePath}"
                    String realName = new Date().getTime() //文件的真实文件名
                    def attachment = attachmentService.uploadFile(pocFile, filePath, realName)
                    flawBatch.pocAttachment = attachment
                } else {
                    //附件不符合格式或大小限制
                    def poc_fileLimit_error = "附件类型不是.zip或大小超出10MB"
                    render(view: 'edit', model: [flawBatch: flawBatch, poc_fileLimit_error: poc_fileLimit_error])
                    return
                }
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            FlawTypes flawTypes = FlawTypes.get(params.flawTypes)
            flawBatch.title = "多个"+params.title+"存在"+flawTypes.getName()+"漏洞"
            flawBatch.oldTitle = params.title
            flawBatch.flawTypes = flawTypes
            flawBatch.productEdition = params.productEdition
            flawBatch.discovererName = params.discovererName
            flawBatch.unitName = params.unitName
            flawBatch.foundTime = sdf.parse(params.foundTime)
            flawBatch.status = 3
            flawBatch.cveId = params.cveId
            flawBatch.cnvdId = params.cnvdId
            if (flawBatch.save(flash: true)) {
                //render(view: '/TUser/flawBatchShow')
                return redirect(controller: "TUser", action: "flawBatchShow")
            } else {
                flawBatch.errors.allErrors.each {
                    println(it)
                }
                render(view: "edit", model: [flawBatch: flawBatch])
            }
        }.invalidToken {
            render(view:"/error")
        }
    }

    def create = {
        if(!session.user){
            session.originalRequestParams = [controller:controllerName,action:actionName]
            redirect(controller:"TUser",action:"login")
            return
        }
        render(view:"/flawBatch/create")
        return
    }

    def save = {
        withForm {
            //printf (params.toString())
            if (!session.user) {
                session.originalRequestParams = [controller: controllerName, action: actionName]
                redirect(controller: "TUser", action: "login")
                return
            }

            def flawBatch = new FlawBatch()
            if (!flawBatch) {
                render(view: "/error")
                return
            }
            flawBatch.user = session.user
            if(!params.myCode){
                def myCode_error = "请输入验证码"
                render(view:"create",model:[myCode_error:myCode_error])
                return
            }
            if (params.myCode.toLowerCase()!=session.validateCode){
                def myCode_error = '验证码不正确'
                render(view:"create",model:[myCode_error:myCode_error])
                return
            }
            if (!params.title) {
                def title_error = "漏洞标题不能为空"
                render(view: "create", model: [title_error: title_error, flawBatch: flawBatch])
                return
            }
            if (!params.flawTypes) {
                def flawTypes_error = "漏洞类型不能为空"
                render(view: "create", model: [flawTypes_error: flawTypes_error, flawBatch: flawBatch])
                return
            }

            if (!params.discovererName) {
                def discovererName_error = "发现者姓名不能为空"
                render(view: "create", model: [discovererName_error: discovererName_error, flawBatch: flawBatch])
                return
            }

            if (!params.foundTime) {
                def foundTime_error = "发现日期不能为空"
                render(view: "create", model: [foundTime_error: foundTime_error, flawBatch: flawBatch])
                return
            }


            //上传漏洞附件并设置flawBatchInstance的attachment属性
            def excelFile = request.getFile("excelFile")
            if (!excelFile) {
                def excel_fileLimit_error = "请上传Excel附件,类型为.xlsx或.xls"
                render(view: 'create', model: [flawBatch: flawBatch, excel_fileLimit_error: excel_fileLimit_error])
                return
            } else {
                //判断上传的附件的格式及大小
                def flag = attachmentService.checkExcelFile(excelFile)
                if (flag) {
                    //附件符合格式及大小限制
                    String filePath = "${grailsApplication.config.filePath.flawBatchFilePath}"
                    String realName = new Date().getTime() //文件的真实文件名
                    def attachment = attachmentService.uploadFile(excelFile, filePath, realName)
                    flawBatch.attachment = attachment

                    String fileName = excelFile.getOriginalFilename()
                    fileName = fileName.substring(fileName.lastIndexOf("."));
                    String fileUrlStr = filePath + realName + fileName
                    def poi = new POIExcelUtil(fileUrlStr)
                    def num = poi.getExcelFirstSheetRows()//得到excel总行数:包括表头
                    flawBatch.num = num - 1//flawBatch.num值：去掉表头的总行数
                } else {
                    //附件不符合格式或大小限制
                    def excel_fileLimit_error = "附件类型不是.xlsx/.xls或大小超出10MB"
                    render(view: 'create', model: [flawBatch: flawBatch, excel_fileLimit_error: excel_fileLimit_error])
                    return
                }
            }
            def pocFile = request.getFile("pocFile")
            if (!pocFile) {
                def poc_fileLimit_error = "请上传POC附件,类型为.zip"
                render(view: 'create', model: [flawBatch: flawBatch, poc_fileLimit_error: poc_fileLimit_error])
                return
            } else {
                //判断上传的附件的格式及大小
                def flag = attachmentService.checkZipFile(pocFile)
                if (flag) {
                    //附件符合格式及大小限制
                    String filePath = "${grailsApplication.config.filePath.flawBatchFilePath}"
                    String realName = new Date().getTime() //文件的真实文件名
                    def attachment = attachmentService.uploadFile(pocFile, filePath, realName)
                    flawBatch.pocAttachment = attachment
                } else {
                    //附件不符合格式或大小限制
                    def poc_fileLimit_error = "附件类型不是.zip或大小超出10MB"
                    render(view: 'create', model: [flawBatch: flawBatch, poc_fileLimit_error: poc_fileLimit_error])
                    return
                }
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            FlawTypes flawTypes = FlawTypes.get(params.flawTypes)
            flawBatch.title = "多个"+params.title+"存在"+flawTypes.getName()+"漏洞"
            flawBatch.oldTitle = params.title
            flawBatch.flawTypes = flawTypes
            flawBatch.productEdition = params.productEdition
            flawBatch.discovererName = params.discovererName
            flawBatch.unitName = params.unitName
            flawBatch.foundTime = sdf.parse(params.foundTime)
            flawBatch.status = 0
            flawBatch.cveId = params.cveId
            flawBatch.cnvdId = params.cnvdId

            Date today = new Date()
            //flawBatch.auditingTime=today
            flawBatch.submitTime = today
            if (flawBatch.save(flash: true)) {
                //render(view: '/TUser/flawBatchShow')
                return redirect(controller: "TUser", action: "flawBatchShow")
            } else {
                flawBatch.errors.allErrors.each {
                    println(it)
                }
                render(view: "create", model: [flawBatch: flawBatch])
            }
        }.invalidToken {
            render(view:"/error")
        }
    }

    def downloadSendTemplate = {

        try {
            //获取项目全路径
            def dirPath =  request.getSession().getServletContext().getRealPath("/");
            def file = new File(dirPath+"flawImport.xlsx")
            def name = file.getName()
            if(file.length()>0){
                response.setHeader("Content-disposition", "attachment; filename="+name)
                response.contentType = "application/mswordd"
                response.setCharacterEncoding("utf-8")
                def out = response.getOutputStream()
                InputStream is = new FileInputStream(file)
                byte[] buffer = new byte[1024]
                int i = -1
                while ((i = is.read(buffer)) != -1){
                    out.write(buffer, 0, i)
                }
                out.flush()
                out.close()
                is.close()
            }
        }catch(Exception ex) {
            ex.printStackTrace()
        }
    }

    def downloadReferTemplate = {

        try {
            //获取项目全路径
            def dirPath =  request.getSession().getServletContext().getRealPath("/");
            def file = new File(dirPath+"flawImport.xlsx")
            def name = file.getName()
            if(file.length()>0){
                response.setHeader("Content-disposition", "attachment; filename="+name)
                response.contentType = "application/mswordd"
                response.setCharacterEncoding("utf-8")
                def out = response.getOutputStream()
                InputStream is = new FileInputStream(file)
                byte[] buffer = new byte[1024]
                int i = -1
                while ((i = is.read(buffer)) != -1){
                    out.write(buffer, 0, i)
                }
                out.flush()
                out.close()
                is.close()
            }
        }catch(Exception ex) {
            ex.printStackTrace()
        }
    }
    def downloadFlawBatchExcel = {
        if (!session.user) {
            render(view: "/error")
            return
        }
        TUser user = session.user;
        def flawBatch = FlawBatch.get(params.cd)
        if(flawBatch == null){
            render(view: "/error")
            return
        }
        if(user.getId() == flawBatch.getUser().getId()){
            attachmentService.downloadAttFile(flawBatch.attachment, request, response)
        }else{
            render(view: "/error")
            return
        }
    }
}