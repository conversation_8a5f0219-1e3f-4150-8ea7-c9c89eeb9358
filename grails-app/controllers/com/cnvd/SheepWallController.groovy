package com.cnvd
import java.util.List;

import com.cnvd.common.Task
import com.cnvd.Flaw
import com.cnvd.productInfo.*

import org.apache.commons.collections.map.HashedMap
import org.apache.commons.io.FileUtils
import com.cnvd.SheepWall
import com.cnvd.utils.SQLUtil

import com.cnvd.utils.FileUtil
class SheepWallController {
	def max=50
	def grailsApplication
	def list = {
		//优先删除数据库中状态为2的数据
		String delsql = "DELETE FROM sheep_wall  WHERE STATUS = 2";
		SQLUtil.execute(delsql);
		// 数据输出 后台是1、状态（完成2和已提交4）、任务类型处置3  创建时间2016-01-01开始
		params.max = Math.min(params.max ? params.int('max') : 40, 100)
		params.offset = params.offset ? params.int('offset') : 0
		def hqlPara = new HashMap()
		//应客户要求，从2016年开始展示
		def queryHqlRs=" from SheepWall t where t.status=:status"
		def queryHqlCo="select count(t.id) from SheepWall t where t.status=:status"
		if(params.enterprise){
			queryHqlCo+=" and t.title like :title "
			queryHqlRs+=" and t.title like :title "
			hqlPara.put("title","%"+params.enterprise+"%")
		}
		queryHqlRs+=" order by t.submitDate desc "
		hqlPara.put("status",1)
		List<Map<String, Object>> maps = new ArrayList<Map<String, Object>>();
		def sheepwallList = SheepWall.executeQuery(queryHqlRs,hqlPara,[max:params.max,offset:params.offset])
		List<SheepWall> sheepList1
		List<SheepWall> sheepList2
		if(sheepwallList.size()>20){
			sheepList1 = sheepwallList.subList(0, 20)
			sheepList2 = sheepwallList.subList(20, sheepwallList.size())
		}else{
			sheepList1 = sheepwallList
		}
		def count =SheepWall.executeQuery(queryHqlCo,hqlPara)[0]
		render(view:"list",model:[taskInstanceList : sheepList1,taskInstanceList2 : sheepList2,taskInstanceTotal : count,params:params])
		//render(view:"list",model:[taskInstanceList : sheepwallList,taskInstanceTotal : count,params:params])
	}

	def map={
		def view="map"
		String taskMapPath="${grailsApplication.config.filePath.taskMap}"
		String filePath=taskMapPath+"1.csv"
		File f = new File(filePath)
		if(!f.exists()){
			filePath=taskMapPath+"no.csv"
			f = new File(filePath)
		}
		String fileEncoding=FileUtil.codeString(filePath)
		List<String> proLines = FileUtils.readLines(f, fileEncoding)
		max=50
		def mapProvinceStr=listToJson(proLines)
		render (view:view,model:[allData:mapProvinceStr,max:max])
	}
	def all={
		def view="all"
		String taskMapPath="${grailsApplication.config.filePath.taskMap}"
		String filePath=taskMapPath+"all.csv"
		File f = new File(filePath)
		if(!f.exists()){
			filePath=taskMapPath+"no.csv"
			f = new File(filePath)
		}
		String fileEncoding=FileUtil.codeString(filePath)
		List<String> proLines = FileUtils.readLines(f, fileEncoding)
		max=100
		def mapProvinceStr=listToJson(proLines)
		render (view:view,model:[allDataAll:mapProvinceStr,maxAll:max])
	}

	public String listToJson(List<String> lines) {
		Map<String, String> proMap=new HashMap<String,String>()
		//把所有省的值放在Map中
		for(String line : lines){
			if(line.indexOf(",")>-1){
				String[] keys=line.split(",");
				if(keys.length>0){
					String key=keys[0]
					String value=keys[1]
					try{
						int val=Integer.parseInt(value);
						if(max<val){
							max=val;
						}
						proMap.put(key,value)
					}catch(Exception e){
						continue;
					}
				}
			}
		}
		StringBuffer mapStr = new StringBuffer("[");
		for(String key : proMap.keySet()){
			mapStr.append("{");
			String value=proMap.get(key)
			mapStr.append("name:'"+key+"'");
			mapStr.append(",value:'"+value+"'");
			mapStr.append("},");
		}
		if(mapStr.length()>1){
			mapStr = new StringBuffer(mapStr.substring(0, mapStr.length() - 1));
		}
		mapStr.append("]");
		return mapStr.toString();
	}
}
