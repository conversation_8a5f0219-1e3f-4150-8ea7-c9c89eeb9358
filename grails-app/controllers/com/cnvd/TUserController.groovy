package com.cnvd

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cnvd.cer.CA
import com.cnvd.common.*
import com.cnvd.flawInfo.BatchFlaw
import com.cnvd.flawInfo.Certificate
import com.cnvd.flawInfo.DictionaryInfo
import com.cnvd.flawInfo.Exploit
import com.cnvd.industryLibrary.CorporationProduct
import com.cnvd.industryLibrary.FlawIndustryProduct
import com.cnvd.industryLibrary.IndustryFlaw
import com.cnvd.info.TUserNews
import com.cnvd.patchInfo.PatchInfo
import com.cnvd.productInfo.Manufacturer
import com.cnvd.productInfo.ProductCategory
import com.cnvd.productInfo.ProductInfo
import com.cnvd.productInfo.ProductInfoLog
import com.cnvd.utils.*
import com.cnvd.yqm.FlawYqm
import com.cnvd.yqm.Yqm
import com.sun.star.io.IOException
import com.sun.star.uno.Exception
import grails.converters.JSON
import org.springframework.web.util.HtmlUtils

import javax.servlet.http.Cookie
import javax.servlet.http.HttpServletRequest
import java.security.KeyPair
import java.security.cert.X509Certificate
import java.security.interfaces.RSAPrivateKey
import java.text.SimpleDateFormat

class TUserController {
    def userService
    def dataSource
    def jcaptchaService
    def attachmentService
    def toJsonObjectService
    static final Integer SESSION_TIMEOUT = 86400
    //session timeout, this time is set in seconds. 86400 = 24 hour
    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]
    /**
     * 密钥
     */
    def KEY = "0c4e0acf07ac97ba7edd7fb2a1936fb9";

    def index = {
        redirect(action: "reportManage", params: params)
    }
    def regist = {
        if (session.user != null) {
            //如果用户已经登录 ，跳转至用户中心
            redirect(action: "userManager")
        }
        render(view: "regist", model: [userType: '100200'])
    }

    def doRegist = {
        Yqm yqm
        FlawYqm flawYqm
        List<FlawYqm> flawYqmlist
        def TUserInstance


        String userType = params.userType
        try {
            if (!jcaptchaService.validateResponse("imageCaptcha", session.id, params.myCode)) {
                //def captcha_error = '验证码不正确'
                def captcha_error = 'The verification code is incorrect'
                render(view: "regist", model: [TUserInstance: TUserInstance, captcha_error: captcha_error, userType: userType])
                return
            }
        } catch (Exception e) {
            // def captcha_error = '验证码不正确'
            def captcha_error = 'The verification code is incorrect'
            render(view: "regist", model: [TUserInstance: TUserInstance, captcha_error: captcha_error, userType: userType])
            return
        }
        if (!params.nickName) {
            //请输入用户名称
            def nickName_error = "Please enter the username"
            render(view: "regist", model: [TUserInstance: TUserInstance, nickName_error: nickName_error, userType: userType])
            return
        }
        if (!params.email) {
            //注册邮箱必须填写
            def email_error = "Registration email is required"
            render(view: "regist", model: [TUserInstance: TUserInstance, email_error: email_error, userType: userType])
            return
        } else {
            //TUserInstance = TUser.executeQuery(' from TUser where email=? and status != ?',[params.email,Constants.userStatusMap['TEMPORARY']])
            def tempUser = TUser.findByEmailAndStatusNotEqual(params.email, Constants.userStatusMap['TEMPORARY'])
            if (tempUser) {
                //此邮箱已被注册
                def email_error = "The email address is already registered"
                render(view: "regist", model: [TUserInstance: TUserInstance, email_error: email_error, userType: userType])
                return
            } else {
                tempUser = TUser.findByEmailAndStatus(params.email, Constants.userStatusMap['TEMPORARY'])
                if (tempUser) {
                    TUserInstance = tempUser
                }
            }
        }
        if (!params.passwordHidden) {
            //请输入密码
            def password_error = "Please enter your password"
            render(view: "regist", model: [TUserInstance: TUserInstance, password_error: password_error, userType: userType])
            return
        }
        if (!params.nickName) {
            //请填写用户名
            def nickName_error = "Please enter your username"
            render(view: "regist", model: [TUserInstance: TUserInstance, email_error: email_error, userType: userType])
            return
        } else {
            def tuser = TUser.findByNickName(params.nickName)
            if (tuser) {
                //此用户名已被占用
                def nickName_error = "This username is already taken"
                render(view: "regist", model: [TUserInstance: TUserInstance, nickName_error: nickName_error, userType: userType])
                return
            }
        }

        if (params.userType == Constants.userTypeMap['ENTERPRISEUSER'].toString()) {
            if (params.url) {

                String email = params.email
                String[] emailDomin = email.split("@")
                def name = URLUtils.getDomainName(params.url)

                //如果一级域不符合条件
                def emailDominSb = new StringBuilder(emailDomin[1])
                def nameSb = new StringBuilder(name)
                nameSb = nameSb.reverse()
                emailDominSb = emailDominSb.reverse()
                def nameSbStr = nameSb.toString();
                def emailDominSbStr = emailDominSb.toString()

                if (emailDominSbStr.size() >= nameSbStr.size()) {
                    def emailDominSbStrsub = emailDominSbStr.substring(0, nameSbStr.size())
                    if (!emailDominSbStrsub.equals(nameSbStr)) {
                        //企业用官方网站地址和邮箱不相符
                        def url_error = "The official website address and email domain of the company do not match"
                        render(view: "regist", model: [TUserInstance: TUserInstance, url_error: url_error, userType: userType])
                        return
                    }
                } else {
                    //企业用官方网站地址和邮箱不相符
                    def url_error = "The official website address and email domain of the company do not match"
                    render(view: "regist", model: [TUserInstance: TUserInstance, url_error: url_error, userType: userType])
                    return
                }

                /*if (!name.equals(emailDomin[1])) {
                    //判断二级域名
                    def url2=URLUtils.getTopDomain2(params.url);
                    //如果二级域名不等于
                    if (!url2.equals(emailDomin[1])) {
                        //判断三级域名
                        def url3=URLUtils.getTopDomain3(params.url);
                        //如果三级域名不等于
                        if (!url3.equals(emailDomin[1])) {
                            def url_error = "企业用官方网站地址和邮箱不相符"
                            render(view: "regist", model: [TUserInstance: TUserInstance, url_error: url_error, userType: userType])
                            return
                        }
                    }
                }

                def url3=URLUtils.getTopDomain3(params.url);
                //判断邮箱跟企业网址是否符合
                if (!url3.equals(emailDomin[1])) {
                    def url_error = "企业用官方网站地址和邮箱不相符"
                    render(view: "regist", model: [TUserInstance: TUserInstance, url_error: url_error, userType: userType])
                    return
                }*/
            } else {
                //请填写企业官方网站地址
                def url_error = "Please enter the official website address of your company"
                render(view: "regist", model: [TUserInstance: TUserInstance, url_error: url_error, userType: userType])
                return
            }
        }

        if (!params.serviceTerms) {
            //请勾选服务条款
            def serviceTerms_error = "Please check the box to agree to the Terms of Service"
            render(view: "regist", model: [TUserInstance: TUserInstance, serviceTerms_error: serviceTerms_error, userType: userType])
            return
        }


        if (params.yqm) {
            try {
                def yqmList = Yqm.createCriteria().list {
                    eq("status", 0)
                    eq("code", params.int('yqm'))
                }
                if (!yqmList) {
                    //邀请码不存在或已经被使用
                    def captcha_error = 'The invitation code does not exist or has already been used'
                    render(view: "regist", model: [TUserInstance: TUserInstance, captcha_error: captcha_error, userType: userType])
                    return
                } else {
                    yqm = yqmList[0];
                    if (yqm.tUser) {
                        TUserInstance = yqm.tUser
                    }
                    /*if (yqm.manufacturer){
						TUserInstance.manufacturer =yqm.manufacturer
					}*/
                    flawYqmlist = FlawYqm.findAllByYqm(yqm)
                }
            } catch (Exception e) {
                e.printStackTrace()
                //邀请码不正确
                def captcha_error = 'The invitation code is incorrect'
                render(view: "regist", model: [TUserInstance: TUserInstance, captcha_error: captcha_error, userType: userType])
                return
            }
            try {
                flawYqmlist.each {
                    def flaw = it.flaw
                    def taskType = it.taskType
                    if (taskType == 1) {
                        if (flaw && flaw.isv == Constants.ISP_NOT) {
                            def task = new Task()
                            task.type = taskType
                            task.targetType = 2 // 任务来源于[后台] 邮件派发
                            task.targetTUser = TUserInstance //当前创建的用户
                            task.title = flaw.title
                            task.info = "补丁任务来源：邮件派发"
                            task.flaw = flaw
                            task.creater = it.creater
                            task.dateCreated = new Date()
                            task.completeDate = new Date()
                            //更新漏洞状态
                            if (task.save(flush: true)) {
                                flaw.isv = Constants.ISP_ING
                                flaw.save(flush: true)
                                yqm.status = 1
                                yqm.save(flush: true)
                            }
                        }
                    } else if (taskType == 3) {
                        if (flaw && flaw.ivp == Constants.ISP_NOT) {
                            def task = new Task()
                            task.type = taskType
                            task.targetType = 2 // 任务来源于[后台] 邮件派发
                            task.targetTUser = TUserInstance //当前创建的用户
                            task.title = flaw.title
                            task.info = "补丁任务来源：邮件派发"
                            task.flaw = flaw
                            task.creater = it.creater
                            task.dateCreated = new Date()
                            task.completeDate = new Date()
                            //更新漏洞状态
                            if (task.save(flush: true)) {
                                flaw.isv = Constants.ISP_ING
                                flaw.ivp = Constants.ISP_ING
                                flaw.save(flush: true)
                                yqm.status = 1
                                yqm.save(flush: true)
                            }
                        }
                    }

                }

            } catch (Exception e) {
                e.printStackTrace();
                //创建关联补丁任务失败
                def captcha_error = 'Failed to create the associated patch task'
                render(view: "regist", model: [TUserInstance: TUserInstance, captcha_error: captcha_error])
                return
            }
        } else {
            TUserInstance = new TUser(params)
        }

        KeyPair keyPair = RSAUtils.genRSAKeyPair();
        //获取私钥和modu
        //模hex
        String modulus = (String) session.getAttribute("modu");
        //私钥指数hex
        String private_exponent = (String) session.getAttribute("m");
        RSAPrivateKey priKey = RSAUtils.getPrivateKey(modulus, private_exponent);
        //私钥解密后的明文
        String key = RSAUtils.decryptByPrivateKey(HexUtil.hexStringToBytes(params.aesKey), priKey)
        def password = AESCrypt.Decrypt(params.passwordHidden, key);
        System.out.println("注册的密码===" + password);

        def encryptedPwd = MD5.getEncryptedPwd(password);
        TUserInstance.email = params.email
        TUserInstance.password = encryptedPwd
        TUserInstance.nickName = params.nickName
        TUserInstance.registTime = new Date()
        TUserInstance.userType = params.int("userType")

        if (params.url) {
            println params.url
            TUserInstance.url = params.url
        }
        if (params.userType == Constants.userTypeMap['PERSONAL'].toString()) {
            TUserInstance.status = Constants.userStatusMap['UNACTIVE']
            def randMD5 = MD5.getMD5Str(Constants.getRandomNumber(6).toString())
            def timeMD5 = MD5.getMD5Str(String.valueOf(System.currentTimeMillis()));
            TUserInstance.activeCode = MD5.getMD5Str(randMD5 + timeMD5);
        } else {
            TUserInstance.isTimeoutEditor = 1
            TUserInstance.status = Constants.userStatusMap['UNACTIVE']
            def randMD5 = MD5.getMD5Str(Constants.getRandomNumber(6).toString())
            def timeMD5 = MD5.getMD5Str(String.valueOf(System.currentTimeMillis()))
            TUserInstance.activeCode = MD5.getMD5Str(randMD5 + timeMD5)
        }

        /*TUserInstance.status = Constants.userStatusMap['UNACTIVE']
        def randMD5 = MD5.getMD5Str(Constants.getRandomNumber(6).toString())
        def timeMD5 = MD5.getMD5Str(String.valueOf(System.currentTimeMillis()));
        TUserInstance.activeCode = MD5.getMD5Str(randMD5+timeMD5);*/

        if (TUserInstance.save(flush: true)) {
            try {
                if (params.yqm) {
                    yqm.status = 1
                    yqm.save(flush: true)
                }
                println "发送激活邮件到邮箱-->" + TUserInstance.email
                //恭喜您注册成功
                new Mail(fromEmail: null, toEmail: TUserInstance.email, title: "Congratulations! Your registration is successful", content: "type#:regist#,userId#:${TUserInstance.id}", isMust: 1, isHandled: 0, createDate: new Date()).save()
            } catch (Exception e) {
                e.printStackTrace();
            }
            render(view: "regsucc", model: [TUserInstance: TUserInstance])
            return
        } else {
            render(view: "regist", model: [TUserInstance: TUserInstance])
            return
        }
    }

    //激活邮箱账户
    def activate = {
        def activeCode = params.activeCode;
        if (activeCode) {
            def TUserInstance = TUser.findByActiveCode(activeCode)
            if (TUserInstance) {
                if (TUserInstance.status == Constants.userStatusMap['UNACTIVE']) {
                    if (TUserInstance.userType == 100203) {
                        TUserInstance.status = Constants.userStatusMap['PENDING']
                    } else {
                        TUserInstance.status = Constants.userStatusMap['ACTIVE']
                    }
                    TUserInstance.setActiveCode("")
                    TUserInstance.save(flush: true)
                    //恭喜您，您的账户已成功激活
                    params.activeMsg = 'Congratulations! Your account has been successfully activated'
                    params.msg = 'success'
                } else {
                    //您的账户已通过验证，无需再次提交。请直接登录
                    params.activeMsg = 'Your account has been successfully verified—no further action is required. Please proceed to log in directly'
                    params.msg = 'error'
                }
            } else {
                //用户已激活或激活码已失效
                params.activeMsg = 'The user is already activated, or the activation code has expired'
                params.msg = 'error'
            }
            /*
            由于这里会发生再次点击激活码，然后免密登录漏洞，所以屏蔽掉
            if(TUserInstance.userType == 100203){
                return [params: params]
            }else{
                session.user = TUserInstance
                return [TUserInstance: TUserInstance, params: params]
            }*/
        } else {
            render(view: "error")
            return
        }
    }

    /**
     * 登出并删除session信息
     */
    def logout = {
        session.user = null
        session.invalidate()
        CookieUtils cu = new CookieUtils();
        //移除cookies中的数据
        cu.removeCookiesValue(request, response);
        //退出时、清空cookie中token信息
        Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {
            if ("token".equals(cookie.getName())) {
                cookie.setValue(null);
                cookie.setPath("/");
                cookie.setMaxAge(0);
                response.addCookie(cookie);
                break;
            }
        }
        return redirect(uri: "/")
    }
    /**
     * 显示登入页面
     */
    def login = {
        if (session.user) {
            return redirect(action: "reportManage")
        }
    }
    def getLoginInfo = {
        def Map<String, String> res = new HashMap<String, String>();
        if (session.user) {
            res.put("code", "200")
            res.put("userName", session.user.nickName)
        } else {
            res.put("userName", "")
            res.put("code", "500")
        }
        return render(res as JSON)
    }
    /**
     * 登录并设置session信息
     */
    def doLogin = {
        String password = "";
        if (!params.email) {
            //请输入邮箱
            flash.email_error = "Please enter your email address"
            render(view: "login", model: [params: params])
            return
        }
        if (!params.passwordHidden) {
            //请输入密码
            flash.password_error = "Please enter your password"
            render(view: "login", model: [params: params])
            return
        } else {
            KeyPair keyPair = RSAUtils.genRSAKeyPair();
            //获取私钥和modu
            //模hex
            String modulus = (String) session.getAttribute("modu");
            //私钥指数hex
            String private_exponent = (String) session.getAttribute("m");
            RSAPrivateKey priKey = RSAUtils.getPrivateKey(modulus, private_exponent);
            //私钥解密后的明文
            String key = RSAUtils.decryptByPrivateKey(HexUtil.hexStringToBytes(params.aesKey), priKey)
            password = AESCrypt.Decrypt(params.passwordHidden, key);

            if (password.length() < 6) {
                //密码长度必须大于6位
                flash.password_error = "The password must be longer than 6 characters"
                render(view: "login", model: [params: params])
                return
            } else if (password.length() > 40) {
                //密码长度必须小于40位
                flash.password_error = "The password must be less than 40 characters"
                render(view: "login", model: [params: params])
                return
            }

        }
        String code = session.getAttribute(CommonController.VALIDATE_CODE);
        long startTime = (long) session.getAttribute(params.myCode.toLowerCase());
        long endTime = System.currentTimeMillis();
        if (startTime == null) {
            request.getSession().setAttribute(params.myCode.toLowerCase(), System.currentTimeMillis());
            startTime = (long) session.getAttribute(params.myCode.toLowerCase());
        }
        int allSecond = (int) (endTime - startTime) / 1000;
        if (params.myCode.toLowerCase() != session.getAttribute(CommonController.VALIDATE_CODE) || allSecond > 30) {
            //验证码不正确
            flash.captcha_error = 'The verification code is incorrect'
            render(view: "login", model: [params: params])
            return
        }
        //验证邮箱和密码是否正确
        if (params.email && params.passwordHidden) {
            def user = TUser.findByEmailAndStatusNotEqual(params.email, Constants.userStatusMap['TEMPORARY'])
            if (user && MD5.validPassword(password, user.password)) {
                if (user.status == Constants.userStatusMap['UNACTIVE'] || user.status == Constants.userStatusMap['PENDING'] || user.status == Constants.userStatusMap['DISABLED'] || user.status == Constants.userStatusMap['REJECTED']) {
                    render(view: 'unactive', model: [TUserInstance: user])
                    return;
                }
                //将登录的用户存放到session
                session.user = user
                //设置cookie  --与众测平台进行单点登录
                CookieUtils cu = new CookieUtils();
                cu.setCookieValue(request, response, user, params.passwordHidden);
                //设置cookie  end

                //设置session timeout
                session.setMaxInactiveInterval(SESSION_TIMEOUT)
                if (session.originalRequestParams) {
                    def target = session.originalRequestParams.clone()
                    session.originalRequestParams = null
                    //redirect(controller: "flaw",action: "show")
                    redirect(target)
                    return
                } else {
                    redirect(action: "reportManage")
                    return
                }
            } else {
                //邮箱或密码输入错误
                flash.email_error = "The email or password you entered is incorrect"
                render(view: "login", model: [params: params])
                return
            }
        } else {
            render(view: "login", model: [params: params])
            return
        }
    }
    /**
     * 登录认证
     */
    def loginAuthentication = {
        def result = new JSONObject()
        try {
            def reJson = getRequestJson(request)
            JSONObject jsonObject = JSONObject.parseObject(reJson);
            def email = jsonObject.getString("email")
            def password = jsonObject.getString("password")
            def sumTtotal = 0
            def honorPoints = 0
            if (!email) {
                result.put("status", "0")
                result.put("data", "")
                //登录名不能为空
                result.put("msg", "Login name cannot be empty")
                render result as JSON
                return
            }
            if (!password) {
                result.put("status", "0")
                result.put("data", "")
                //密码不能为空
                result.put("msg", "Password cannot be empty")
                render result as JSON
                return
            }
            def tuser = TUser.findByEmailAndStatusNotEqual(email, Constants.userStatusMap['TEMPORARY'])
            if (tuser && MD5.validPassword(EncryptUtils.aesDecrypt(password, KEY), tuser.password)) {
                String sql = "SELECT TRUNCATE(sum(points.total),2) as total ,TRUNCATE(sum(points.honor_points),2) AS points FROM flaw,tuser,points WHERE tuser.id = ? and  flaw.user_id = tuser.id AND flaw.points_id = points.id AND tuser.id <> 1 AND flaw. STATUS = 9 AND flaw.ENABLE = 1 AND flaw.parent_flaw_id IS NULL GROUP BY flaw.user_id  "
                def rankLists = SQLUtil.getResult(sql, [tuser.id]);
                if (rankLists) {
                    sumTtotal = rankLists[0].total ? rankLists[0].total : 0
                    honorPoints = rankLists[0].points ? rankLists[0].points : 0
                }
                String aesStr = tuser.id + "#@#" + tuser.email + "#@#" + tuser.nickName + "#@#" + tuser.userType + "#@#" + tuser.password + "#@#" + tuser.honorValue
                String aes = AESCrypt.Encrypt(aesStr);
                result.put("data", aes)
            } else {
                result.put("status", "0")
                result.put("data", "")
                //用户名或密码输入错误
                result.put("msg", "Incorrect username or password")
                render result as JSON
                return
            }
            result.put("status", "1")
            //登录成功
            result.put("msg", "Login successfully")
            render result as JSON
            return
        } catch (Exception e) {
            e.printStackTrace()
            result.put("status", "0")
            result.put("data", "")
            //程序错误，请联系管理员
            result.put("msg", "System error Please contact the administrator")
            render result as JSON
            return
        }

    }

    /**
     * 加密接口
     */
    def aesEncrypt = {
        def result = new JSONObject()
        if (params.password != null && params.password != '') {
            def password = params.password
            def encPassword = EncryptUtils.aesEncrypt(password)
            result.put("code", "200")
            result.put("data", encPassword)
            //操作成功
            result.put("msg", "Operation succeeded")
            render result as JSON
        } else {
            result.put("code", "201")
            result.put("data", '')
            //操作失败
            result.put("msg", "Operation failed")
            render result as JSON
        }
    }

    public String getRequestJson(HttpServletRequest request) throws Exception {
        InputStream is = null;
        BufferedInputStream bis = null;
        StringBuilder sb = new StringBuilder();
        try {
            is = request.getInputStream();
            bis = new BufferedInputStream(is);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = bis.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
            // sb.append(baos.toString("UTF-8"));
            sb.append(baos.toString("UTF-8"));

            baos.close();
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            try {
                bis.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return sb.toString();
    }


    /**
     * 编辑个人信息
     **/
    def edit = {
        if (!session.user) {
            return redirect(action: "login");
        }
        def TUserInstance = TUser.get(session.user?.id);
        if (!TUserInstance) {
            render(view: "/error")
        } else {
            return [TUserInstance: TUserInstance, params: params]
        }
    }

    def update = {
        //token
        withForm {
            if (!session.user) {
                return redirect(action: "login");
            }
            def TUserInstance = TUser.get(session.user?.id);
            def email = TUserInstance.email
            if (TUserInstance) {
                TUserInstance.properties = params
                TUserInstance.email = session.user?.email
                TUserInstance.nickName = session.user?.nickName
                if (!TUserInstance.hasErrors() && TUserInstance.save(flush: true)) {
                    session.user.nickName = TUserInstance.nickName
                    session.user.phoneNumber = TUserInstance.phoneNumber
                    session.user.address = TUserInstance.address
                    session.user.work = TUserInstance.work
                    session.user.email = TUserInstance.email
                    session.user.description = TUserInstance.description

                    //保存成功
                    flash.message = "Saved successfully"
                    redirect(action: "edit", id: params.id, params: [editsucess: "1", p: params.gorhname])
                    return
                } else {
                    if (!session.user.email) {
                        TUserInstance.email = null;
                    }
                    render(view: "edit", model: [TUserInstance: TUserInstance])
                }
            } else {
                render(view: "/error")
            }
        }.invalidToken { render(view: "/error") }
    }

    /**
     * 编辑厂商信息
     **/
    def editManufacturer = {
        if (!session.user) {
            return redirect(action: "login");
        }
        def TUserInstance = TUser.get(session.user?.id);

        return [manufacturer: TUserInstance?.manufacturer, params: params]

    }


    /**
     * 编辑厂商信息历史记录
     **/
    def editManufacturerHis = {
        if (!session.user) {
            return redirect(action: "login");
        }
        def tUserInstance = TUser.get(session.user?.id);
        def manufacturerExamine = ManufacturerExamine.findByManufacturerId(tUserInstance.manufacturer.id, [sort: 'id', order: 'desc', max: 1, offset: 0])

        return [manufacturer: manufacturerExamine, params: params]

    }


    /*   def updateManufacturer = {
           //token
           withForm {
               SendMail sendMail = SendMail.getInstance();
               def updateManufacturerEmail = "${grailsApplication.config.grails.mail.updateManufacturerEmail}"
               def TUserInstance = TUser.get(session.user?.id);

               def toMail = updateManufacturerEmail.split(";");
               def title = TUserInstance.nickName + "用户提交的修改单位信息申请";
               String content = "<p>您好，</p>" +
                       "<p>用户#{用户名}申请进行修改单位信息，提交的单位信息如下：</p>" +
                       "<p>单位名称：#{单位名称}</p>" +
                       "<p>单位注册邮箱：#{单位注册邮箱}</p>" +
                       "<p>单位详细信息：#{单位详细信息}</p>" +
                       "<p>单位联系电话：#{单位联系电话}</p>" +
                       "<p>单位地址：#{单位地址}</p>" +
                       "<p>单位法人：#{单位法人}</p>" +
                       "<p>单位ICP备案号：#{单位ICP备案号}</p>" +
                       "<p>单位关键词：#{单位关键词}</p>" +
                       "<p>单位行业：#{单位行业}</p>" +
                       "<p>祝好！</p>";
               content = content.replace("#{用户名}", TUserInstance.nickName);
               content = content.replace("#{单位名称}", params.name);
               content = content.replace("#{单位注册邮箱}", params.email);
               content = content.replace("#{单位详细信息}", params.description);
               content = content.replace("#{单位联系电话}", params.phoneNum);
               content = content.replace("#{单位地址}", params.address);
               content = content.replace("#{单位法人}", params.corporation);
               content = content.replace("#{单位ICP备案号}", params.icp);
               content = content.replace("#{单位关键词}", params.Keyword);
               content = content.replace("#{单位行业}", params.Unit);
               String subject = title;
               String succ = null;
               //附件
               String fileName = "" // 附件地址
               def file = request.getFile("uploadFile")
               if (file != null && !file.empty) {
                   String DownloadPath = "${grailsApplication.config.filePath.flawAttFilePath}"
                   fileName = DownloadPath + file.getOriginalFilename()
                   File fileNew = new File(fileName)
                   file.transferTo(fileNew)
               } else {
                   flash.message = "附件不能为空"
                   redirect(controller: "TUser", action: "editManufacturer", id: params.id, params: [editsucess: "1", p: params.gorhname, params: params])
                   return
               }

               try {
                   succ = sendMail.send(toMail, subject, content, fileName)
                   if (succ.equals("true")) {
                       flash.message = "发送邮箱成功"
                       redirect(controller: "TUser", action: "editManufacturer", id: params.id, params: [editsucess: "1", p: params.gorhname, params: params])
                       return
                   } else {
                       flash.message = "发送邮箱失败"
                       redirect(controller: "TUser", action: "editManufacturer", id: params.id, params: [editsucess: "1", p: params.gorhname, params: params])
                       return
                   }

               } catch (Exception e) {
                   e.printStackTrace()
                   flash.message = "发送邮箱失败"
                   redirect(controller: "TUser", action: "editManufacturer", id: params.id, params: [editsucess: "1", p: params.gorhname, params: params])
                   return
               }


           }.invalidToken { render(view: "/error") }
       }*/


    def updateManufacturer = {
        //token
        withForm {

            ManufacturerExamine manufacturerExamine = new ManufacturerExamine()
            manufacturerExamine.setStatus(1)
            manufacturerExamine.setName(params.name == null ? "" : String.valueOf(params.name))
            manufacturerExamine.setContacts(params.contacts == null ? "" : String.valueOf(params.contacts))
            manufacturerExamine.setEmail(params.email == null ? "" : String.valueOf(params.email))
            manufacturerExamine.setPhoneNum(params.phoneNum == null ? "" : String.valueOf(params.phoneNum))
            manufacturerExamine.setAddress(params.address == null ? "" : String.valueOf(params.address))
            manufacturerExamine.setCorporation(params.corporation == null ? "" : String.valueOf(params.corporation))
            manufacturerExamine.setIcp(params.icp == null ? "" : String.valueOf(params.icp))
            manufacturerExamine.setKeyword(params.Keyword == null ? "" : String.valueOf(params.Keyword))
            manufacturerExamine.setUnit(params.Unit == null ? "" : String.valueOf(params.Unit))
            manufacturerExamine.setManufacturerId(Long.valueOf(params.id))
            manufacturerExamine.setDescription(params.description == null ? "" : String.valueOf(params.description))
            manufacturerExamine.setUser(session.user)
            if (manufacturerExamine.save(flush: true)) {
                //提交成功，等待审核
                flash.message = "Submitted successfully, pending review"
                redirect(controller: "TUser", action: "editManufacturer", id: params.id, params: [editsucess: "1", p: params.gorhname, params: params])
                return
            } else {

                //提交失败了
                flash.message = "Submission failed"
                redirect(controller: "TUser", action: "editManufacturer", id: params.id, params: [editsucess: "1", p: params.gorhname, params: params])
                return
            }

        }.invalidToken { render(view: "/error") }
    }


    /**
     * 修改密码
     */
    def changepassword = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            return redirect(controller: "TUser", action: "login")
        }
    }

    def changePwd = {
        //token
        withForm {
            if (!session.user) {
                return redirect(controller: "TUser", action: "login")
            }
            //判断原密码 新密码 确认密码是否为空
            if (!params.oldPsw) {
                //原密码为空
                //原密码不能为空
                flash.oldPswErr = "Current password cannot be empty"
                redirect(action: "changepassword", params: [p: params.p])
            }
            if (!params.newPsw) {
                //新密码位空
                //新密码不能为空
                flash.newPswErr = "New password cannot be empty"
                redirect(action: "changepassword", params: [p: params.p])
            }
            if (!params.confirmPsw) {
                //确认密码为空
                //确认密码不能为空
                flash.confirmPswErr = "Confirm password cannot be empty"
                redirect(action: "changepassword", params: [p: params.p])
            }
            if (!params.confirmPsw.equals(params.newPsw)) {
                //确认密码为空
                //两次密码不一致，请重新填写
                flash.confirmPswErr = "Passwords do not match Please re-ente"
                redirect(action: "changepassword", params: [p: params.p])
            }

            KeyPair keyPair = RSAUtils.genRSAKeyPair();

            //获取私钥和modu
            //模hex
            String modulus = (String) session.getAttribute("modu");

            //私钥指数hex
            String private_exponent = (String) session.getAttribute("m");

            RSAPrivateKey priKey = RSAUtils.getPrivateKey(modulus, private_exponent);
            //私钥解密后的明文
            String key = RSAUtils.decryptByPrivateKey(HexUtil.hexStringToBytes(params.aesKey), priKey)

            String old = AESCrypt.Decrypt(params.oldPsw, key);
            String newPassword = AESCrypt.Decrypt(params.confirmPsw, key);
            params.oldPsw = old;
            params.confirmPsw = newPassword;
            def user = TUser.get(session.user.id)
            if (MD5.validPassword(params.oldPsw, user.password)) {
                user.password = MD5.getEncryptedPwd(params.confirmPsw);
                if (user.save(flush: true)) {
                    session.user.password = user.password;
                    //修改成功
                    flash.message = "Changed successfully"
                } else {
                    user.errors.allErrors.each { println it }
                    //修改失败
                    flash.message = "Change failed"
                }

            } else {
                //原密码输入有误
                flash.oldPswErr = "Current password is incorrect"
            }
            redirect(action: "changepassword", params: [p: params.p])
        }.invalidToken { render(view: "/error") }

    }

    /**
     * 忘记密码
     */
    def forgetPwd = {}

    /**
     * 找回密码
     */
    def doForgetPwd = {
        //token
        withForm {
            long startTime = (long) session.getAttribute(params.myCode.toLowerCase());
            long endTime = System.currentTimeMillis();
            if (startTime == null) {
                request.getSession().setAttribute(params.myCode.toLowerCase(), System.currentTimeMillis());
                startTime = (long) session.getAttribute(params.myCode.toLowerCase());
            }
            int allSecond = (int) (endTime - startTime) / 1000;
            if (params.myCode.toLowerCase() != session.getAttribute(CommonController.VALIDATE_CODE) || allSecond > 30) {
                //验证码不正确
                flash.captcha_error = 'Verification code is invalid'
                render(view: "forgetPwd", model: [params: params])
                return
            }
            def user
            if (params.backPwdType == '1') {
                user = TUser.findByEmail(params.email)
            } else if (params.backPwdType == '2') {
                user = TUser.findByPhoneNumber(params.phoneNumber)
            }
            //当user.status == 100302 不能找回密码
            if (null == user) {
                render(view: "forgetPwdSuccess", model: [params: params])
                return;
                /*if (params.backPwdType == '1') {
                    flash.forgetPwdMessage = "邮箱填写错误"
                } else if (params.backPwdType == '2') {
                    //flash.forgetPwdMessage = "${message(code: 'airizu.TUserController.phoneError')}"
                }*/
            } else if (user.status == 100302 || user.status == 100300 || user.status == 100301) {
                //您的用户不能找回密码，请联系客服人员
                flash.forgetPwdMessage = "Your account is not eligible for self-service password recovery Please contact customer support"
                flash.email = params.email
            } else {
                if (params.backPwdType == '1') {//同时发送邮件
                    def passwordReset
                    //判断两次发送时间之间的大小
                    def emailMD5 = MD5.getMD5Str(params.email)

                    def passwordResetList = PasswordReset.findAllByEmailMD5AndEnable(emailMD5, 0, [sort: 'lastUpdated', order: 'desc', max: 1, offset: 0])
                    if (passwordResetList) {
                        passwordReset = passwordResetList.get(0)
                        //判断两次发送时间之间的大小
                        SimpleDateFormat dfs = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                        def begin = passwordReset.lastUpdated.getTime()
                        def end = new Date().getTime()
                        if ((end - begin) <= passwordReset.unEffectiveTime) {
                            //在有效时间内,两次间隔在5分钟以内
                            passwordReset.lastUpdated = new Date()
                            passwordReset.user = user
                            passwordReset.save(flush: true)
                        } else {
                            //不在有效时间内,两次间隔超过5分钟
                            passwordReset = new PasswordReset()
                            passwordReset.emailMD5 = MD5.getMD5Str(params.email)
                            def timeMD5 = MD5.getMD5Str(String.valueOf(System.currentTimeMillis()));
                            def toMd5 = MD5.getMD5Str(emailMD5 + timeMD5);
                            passwordReset.unEffectiveTime = 300000
                            passwordReset.user = user
                            passwordReset.realMD5 = toMd5
                            passwordReset.save(flush: true)
                            //new Mail(fromEmail: null, toEmail: params.email, title: "重置cnvd密码", content: "type#:resetPwd#,userId#:${user.id}#,userNickName#:${user.nickName}#,t#:${toMd5}").save()
                            //重置cnvd密码
                            new Mail(fromEmail: null, toEmail: params.email, title: "Reset CNVD password", content: "type#:resetPwd#,userId#:${user.id}#,userNickName#:${user.nickName}#,t#:${toMd5}", isMust: 1, isHandled: 0, createDate: new Date()).save()
                        }
                    } else {
                        passwordReset = new PasswordReset()
                        passwordReset.emailMD5 = MD5.getMD5Str(params.email)
                        def timeMD5 = MD5.getMD5Str(String.valueOf(System.currentTimeMillis()));
                        def toMd5 = MD5.getMD5Str(emailMD5 + timeMD5);
                        passwordReset.unEffectiveTime = 300000
                        passwordReset.user = user
                        passwordReset.realMD5 = toMd5
                        passwordReset.save(flush: true)
                        //重置cnvd密码
                        new Mail(fromEmail: null, toEmail: params.email, title: "Reset CNVD password", content: "type#:resetPwd#,userId#:${user.id}#,userNickName#:${user.nickName}#,t#:${toMd5}", isMust: 1, isHandled: 0, createDate: new Date()).save()
                    }
                } else if (params.backPwdType == '2') {//发送短信获取密码
                    //messageService.sendSms("${user.phoneNumber}", "${I18nUtil.getMessageByCodeWithArgs('airizu.TUserController.sms.forgetPwdBody', [passwordNOMD5].toArray(),user)}")
                }

                render(view: "forgetPwdSuccess", model: [params: params])
                return;
            }
            redirect(action: "forgetPwd")

        }.invalidToken { render(view: "/error") }
    }

    /**
     * 上传头像
     */
    def uploadFile = {
        if (!session.user) {
            redirect(action: "login");
            return
        }
        def uploadFile = params.qqfile
        println "uploadFile=" + uploadFile

        try {
            //上传头像
            def filePath = "${grailsApplication.config.filePath.userImage}/${session.user?.id}/"
            def tempFilePath = "/cnvd/temp/"
            def f = new File(filePath)
            if (!f.exists()) {
                f.mkdirs()
            }
            def fileNameWithoutPath
            def filename
            def realFileName

            //删除指定目录下的所有文件 除1.JPEG之外
            def set = new HashSet<String>();
            set.add "1.JPEG"
            ImageUtil.rmFiles(filePath, set)

            //ie 文件的路径 (浏览器上传返回的路径的不同)
            if (params.qqfile instanceof org.springframework.web.multipart.commons.CommonsMultipartFile) {
                def fileAbsolutePath = params.qqfile.fileItem.name
                if (fileAbsolutePath.indexOf('/') != -1) {
                    fileNameWithoutPath = fileAbsolutePath.substring(fileAbsolutePath.lastIndexOf("/") + 1, fileAbsolutePath.length())
                } else {
                    fileNameWithoutPath = fileAbsolutePath.substring(fileAbsolutePath.lastIndexOf("\\") + 1, fileAbsolutePath.length())
                }
                filename = tempFilePath + fileNameWithoutPath
                realFileName = filePath + fileNameWithoutPath
                def file = new File("${filename}")
                params.qqfile.transferTo(file)

                if (attachmentService.checkAvatar(file)) {
                    def realFile = new File(realFileName)
                    params.qqfile.transferTo(realFile)
                    file.delete()
                } else {
                    render(text: "{'success': 'false'}")
                    return
                }
            } else {
                fileNameWithoutPath = params.qqfile
                filename = tempFilePath + fileNameWithoutPath
                realFileName = filePath + fileNameWithoutPath
                def file = new File(filename)
                InputStream inputStream = request.getInputStream();

                byte[] buf = new byte[1024]
                int len
                OutputStream out = new FileOutputStream(file.getAbsolutePath())
                while ((len = inputStream.read(buf)) > 0) {
                    out.write(buf, 0, len)
                }
                out.close()

                //文件类型检查
                if (attachmentService.checkAvatar(file)) {
                    def realFile = new File(realFileName)
                    InputStream is = new FileInputStream(new File(filename))
                    OutputStream os = new FileOutputStream(realFile.getAbsolutePath())
                    while ((len = is.read(buf)) > 0) {
                        os.write(buf, 0, len)
                    }
                    is.close();
                    os.close()

                } else {
                    render(text: "{'success': 'false'}")
                    return
                }
                inputStream.close()
                file.delete();
            }

            def suffix = fileNameWithoutPath.substring(0, fileNameWithoutPath.lastIndexOf("."))
            println "suffix=" + suffix
            def scaleImagePath = "${filePath}scale-${suffix}.jpg"
            //判断图片是否需要剪切
            def iscut = false
            if (ImageUtil.scale(realFileName, scaleImagePath, 200, 200, true)) {
                //删掉原图
                new File(realFileName).delete()
                iscut = true
                render(text: "{'success': 'true', 'url': 'scale-${suffix}.jpg', 'isCut': ${iscut}}")
            } else {
                render(text: "{'success': 'true', 'url': '${fileNameWithoutPath}', 'isCut': ${iscut}}")
            }
        } catch (Exception e) {
            e.printStackTrace()
        }
    }

    /**
     * 我的上报漏洞
     */
    def reportManage = {
        Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {
            if (cookie.getName().equals("loginInfo")) {
                String loginInfo = cookie.getValue();
                String username = loginInfo.split(",")[0];
                String password = loginInfo.split(",")[1];
                request.setAttribute("username", username);
                request.setAttribute("password", password);
            }
        }

        /**
         * 查询出当前登录用户所上报的漏洞列表
         */
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            return redirect(controller: "TUser", action: "login")
        }
        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0

//        //防止DDOS攻击
        if (params.max > 2000) {
            params.max = 2000;
        }


        // 获取查询参数
        def title = params.title
        def status = params.status
        def dateCreated = params.dateCreated

        // 构建基础HQL
        def baseHql = "from Flaw f where f.user = :user and f.enable = 1"
        def queryParams = [user: session.user]

        // 添加标题模糊查询条件
        if (title) {
            baseHql += " and f.title like :title"
            queryParams.title = "%${title}%"
        }

        // 添加状态查询条件
        if (status) {
            def statusValue = status.toInteger()
            if (statusValue == 99) {
                // 未归档状态：不等于-2、-1、6、7、9
                baseHql += " and f.status not in (-2, -1, 6, 7, 9)"
            } else {
                // 其他具体状态值
                baseHql += " and f.status = :status"
                queryParams.status = statusValue
            }
        }

        // 添加创建日期条件
        if (dateCreated) {
            baseHql += " and f.dateCreated between :dateCreated and :tomorrow"
            queryParams.dateCreated = DateUtil.parse4yMd(dateCreated)
            queryParams.tomorrow = DateUtil.parse4yMd(DateUtil.getTomorrowStr(dateCreated))
        }

        // 执行查询
        def flawInstanceList = Flaw.executeQuery(
                "select f $baseHql order by f.dateCreated desc",
                queryParams,
                [max: params.max, offset: params.offset]
        )

        // 获取总数
        def counts = Flaw.executeQuery(
                "select count(*) $baseHql",
                queryParams
        )[0]

        // 统计维度数据 - 使用HQL查询确保准确性
        def archivedCount = Flaw.executeQuery("""
            select count(*) from Flaw f 
            where f.user = :user 
            and f.enable = 1 
            and f.status = 9
        """, [user: session.user])[0] // 已归档

        def invalidCount = Flaw.executeQuery("""
            select count(*) from Flaw f 
            where f.user = :user 
            and f.enable = 1 
            and f.status = -2
        """, [user: session.user])[0] // 已作废

        def unarchivedCount = Flaw.executeQuery("""
            select count(*) from Flaw f 
            where f.user = :user 
            and f.enable = 1 
            and f.status not in (-2, -1, 6, 7, 9)
        """, [user: session.user])[0] // 未归档

        // 返回结果

        def res = [flawInstanceList: flawInstanceList, counts: counts, count1: archivedCount, count2: invalidCount, count3: unarchivedCount, obj: params]
        if (params.returnType) {
            render(res as JSON)
        } else {
            [flawInstanceList: flawInstanceList, counts: counts, count1: archivedCount, count2: invalidCount, count3: unarchivedCount, obj: params]
        }
    }

    /**
     * 显示我上报的漏洞信息ylx
     */
    def myreport = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        def flawInstance = Flaw.get(params.id)
        def flawTypesParamMiddle = FlawTypesParamMiddle.findAllByFlaw(flawInstance)
        //漏洞-漏洞类型-漏洞类型参数中间表的数据
        if (!flawInstance || flawInstance?.user?.id != session?.user?.id) {
            render(view: "/error")
        } else if (flawInstance.status == 9 && flawInstance.isOpen == 1
                && flawInstance.enable == 1 && new Date().compareTo(flawInstance.openTime) >= 0
                && !flawInstance.parentFlaw) {
            redirect(controller: "flaw", action: "show", params: [id: params.id])
            return
        } else {
            render(view: 'notStorageFlawShow', model: [flawInstance: flawInstance, flawTypesParamMiddle: flawTypesParamMiddle])
        }
    }

    def myPoc = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        def flawPocInstance = FlawPoc.get(params.long('id'))
        if (!flawPocInstance || flawPocInstance?.user?.id != session?.user?.id) {
            render(view: "/error")
        } else {
            redirect(controller: "flawPoc", action: "show", params: [id: params.long('id')])
        }
    }

    /**
     * 我的关注
     */
    def myconcerns = {
        /**
         * 查询出当前登录用户关注的漏洞列表
         */
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        def flawConcernList
        def counts
        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0

        //flawConcernList = FlawConcern.findAllByUser(session.user,[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
        //counts = FlawConcern.countByUser(session.user)//关注的漏洞数量
        def manufacturerConcernList = ManufacturerConcern.findAllByUser(session.user)//关注的厂商集合
        def countSql = "select count(*) as cnt from ( " +
                "select f1.id as flaw_id from flaw f1,(select manufacturer_id,date_created from manufacturer_concern where user_id = ?) t " +
                "where f1.manufacturer_id = t.manufacturer_id " +
                "and f1.status=? and f1.enable=? and f1.open_time<? and f1.is_open=? and f1.parent_flaw_id is null " +
                "UNION " +
                "select flaw_id from flaw_concern where user_id = ? " +
                ")temp"
        def paramList = new ArrayList()
        paramList.add(session?.user?.id)
        paramList.add(9)
        paramList.add(1)
        paramList.add(new Date())
        paramList.add(1)
        paramList.add(session?.user?.id)
        def countRes = new groovy.sql.Sql(dataSource).rows(countSql, paramList)
        def count = countRes[0].cnt
        println "count=" + count

        def sql = "select f.id,f.number,f.title,f.status,temp.type,temp.concernTime from flaw f,( " +
                "select f1.id as flaw_id,2 as type,t.date_created as concernTime from flaw f1,(select manufacturer_id,date_created from manufacturer_concern where user_id = ?) t " +
                "where f1.manufacturer_id = t.manufacturer_id " +
                "and f1.status=? and f1.enable=? and f1.open_time<? and f1.is_open=? and f1.parent_flaw_id is null " +
                "UNION " +
                "select flaw_id,1 as type,date_created as concernTime from flaw_concern where user_id = ? " +
                ") temp where f.id = temp.flaw_id " +
                "order by temp.concernTime desc " +
                "limit ?,?"
        println "sql|" + sql
        paramList.add(params.offset)
        paramList.add(params.max)
        def flawRes = new groovy.sql.Sql(dataSource).rows(sql, paramList)
        //println "flawRes="+flawRes

        //[flawConcernList:flawConcernList,counts:counts,manufacturerConcernList:manufacturerConcernList]
        [flawRes: flawRes, counts: count, manufacturerConcernList: manufacturerConcernList]
    }

    /**
     * 评论管理
     */
    def comments = {
        /**
         * 包括“我评论的”和“评论我的”两部分
         */
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        println "session.user的值为：" + session.user
        def flawCommentList //评论的集合
        def counts //评论的数量
        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0
        if (params.commentType == 'byYou' || params.commentType == null || params.commentType == '') {
            /**
             * “我评论的”处理
             */
            flawCommentList = FlawComments.findAllByUserAndStatus(session.user, 0, [max: params.max, sort: "dateCreated", order: "desc", offset: params.offset])
            counts = FlawComments.countByUserAndStatus(session.user, 0)
            params.commentType = 'byYou'

        } else if (params.commentType == "toYou" || params.commentType == null || params.commentType == '') {
            //“评论我的”处理
            StringBuffer sqlBuff = new StringBuffer()
            sqlBuff.append("select rs.*, tu.nick_name from ( ")
                    .append("select fa.id,fa.number, fc.content, fc.date_created, fc.user_id from flaw_comments fc, flaw fa where fa.user_id=? and fc.status = ? and fc.flaw_id = fa.id")
                    .append(" )rs, tuser tu where rs.user_id = tu.id order by rs.date_created desc limit ?,?")
            println "sqlBuff的值为:" + sqlBuff

            StringBuffer sqlBuffCount = new StringBuffer()
            sqlBuffCount.append("select count(*) as con from ( ")
                    .append("select fa.number, fc.content, fc.date_created, fc.user_id from flaw_comments fc, flaw fa where fa.user_id=? and fc.status = ? and fc.flaw_id = fa.id")
                    .append(" )rs, tuser tu where rs.user_id = tu.id")
            println "sqlBuffCount的值为:" + sqlBuffCount
            //String sql = "select rs.*, tu.nick_name from ( select fa.id,fa.number, fc.content, fc.date_created, fc.user_id from flaw_comments fc, flaw fa where fa.user_id=? and fc.status = ? and fc.flaw_id = fa.id )rs, tuser tu where rs.user_id = tu.id order by rs.date_created desc limit ?,?"
            flawCommentList = SQLUtil.getResult(sqlBuff.toString(), [
                    session.user.id,
                    0,
                    params.offset,
                    params.offset + params.max
            ]);
            //String countSql = "select count(*) as con from ( select fa.number, fc.content, fc.date_created, fc.user_id from flaw_comments fc, flaw fa where fa.user_id=? and fc.status = ? and fc.flaw_id = fa.id )rs, tuser tu where rs.user_id = tu.id"
            counts = SQLUtil.getResult(sqlBuffCount.toString(), [session.user.id, 0]).con[0];

        }
        println "flawCommentList的值为:" + flawCommentList
        [flawCommentList: flawCommentList, obj: params, counts: counts]

    }

    /**
     * 我的积分明细
     */
    def myintegralInfo = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        def integral = TUser.get(session.user.id).integValue
        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0
        /**
         * 查询当前登录用户的所有积分明细列表
         */
        def integralInfoList = IntegralInfo.findAllByUser(session.user, [max: params.max, sort: "dateCreated", order: "desc", offset: params.offset])
        def integralInfoCount = IntegralInfo.countByUser(session.user)
        [integralInfoList: integralInfoList, integralInfoCount: integralInfoCount, integral: integral]
    }

    /**
     * 任务列表-验证任务
     */
    def myexploits = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0

        // 查询参数
        def taskName = params.taskName?.trim()
        def status = params.status ? params.int('status') : null
        def dateCreated = params.dateCreated

        // 构建查询条件和参数
        def queryParams = [targetTUser: session.user, type: Constants.FLAW_EXPOIT]
        def countParams = [targetTUser: session.user, type: Constants.FLAW_EXPOIT]

        // 基础HQL
        def baseHql = "from Task as b where b.targetTUser=:targetTUser and b.type=:type and b.enable=1"
        def countHql = "select count(*) from Task as b where b.targetTUser=:targetTUser and b.type=:type and b.enable=1"

        // 添加任务名称条件
        if (taskName) {
            baseHql += " and b.title like :taskName"
            countHql += " and b.title like :taskName"
            queryParams.taskName = "%${taskName}%"
            countParams.taskName = "%${taskName}%"
        }

        // 添加状态条件
        if (status != null) {
            baseHql += " and b.status = :status"
            countHql += " and b.status = :status"
            queryParams.status = status
            countParams.status = status
        }

        // 添加创建日期条件
        if (dateCreated) {
            baseHql += " and b.dateCreated between :dateCreated and :tomorrow"
            countHql += " and b.dateCreated between :dateCreated and :tomorrow"
            queryParams.dateCreated = dateCreated
            queryParams.tomorrow = DateUtil.getTomorrowStr(dateCreated)
            countParams.dateCreated = dateCreated
            countParams.tomorrow = DateUtil.getTomorrowStr(dateCreated)
        }

        // 添加排序
        baseHql += " order by b.dateCreated desc"

        // 执行查询
        def taskList = Task.executeQuery(baseHql, queryParams, [max: params.max, offset: params.offset])
        def taskInstanceTotal = Task.executeQuery(countHql, countParams)[0]

        // 统计三种状态的任务数量
        def statusParams = [targetTUser: session.user, type: Constants.FLAW_EXPOIT]

        // 进行中(status=1)的任务数量
        def inProgressCount = Task.executeQuery(
                "select count(*) from Task where targetTUser=:targetTUser and type=:type and enable=1 and status=1",
                statusParams
        )[0] ?: 0

        // 完成(status=2)的任务数量
        def completedCount = Task.executeQuery(
                "select count(*) from Task where targetTUser=:targetTUser and type=:type and enable=1 and status=2",
                statusParams
        )[0] ?: 0

        // 取消(status=3)的任务数量
        def canceledCount = Task.executeQuery(
                "select count(*) from Task where targetTUser=:targetTUser and type=:type and enable=1 and status=3",
                statusParams
        )[0] ?: 0

        TUser user = session.user
        def timeoutEditor = user?.isTimeoutEditor ? user?.isTimeoutEditor : 0

        [taskList: taskList, taskInstanceTotal: taskInstanceTotal, timeoutEditor: timeoutEditor,
         count1  : inProgressCount, count2: completedCount, count3: canceledCount, obj: params]

        def res = [taskList: taskList, taskInstanceTotal: taskInstanceTotal, timeoutEditor: timeoutEditor, count1: inProgressCount, count2: completedCount, count3: canceledCount, obj: params]
        if (params.returnType) {
            render(res as JSON)
        } else {
            [taskList: taskList, taskInstanceTotal: taskInstanceTotal, timeoutEditor: timeoutEditor, count1: inProgressCount, count2: completedCount, count3: canceledCount, obj: params]
        }
    }


    /**
     * 任务列表 - 统一展示验证任务、处置任务、补丁任务
     */
    def newTaskList = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        // 分页参数
        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0

        // 查询参数
        def taskName = params.taskName?.trim()
        def taskType = params.taskType ? params.int('taskType') : null
        def dateCreated = params.dateCreated

        // 统一的任务列表
        def allTaskList = []

        // 统计各类型任务总数（用于查询条件过滤）
        def exploitTotal = 0
        def disposalTotal = 0
        def patchTotal = 0

        // 1. 查询验证任务 (type=1)
        if (!taskType || taskType == 1) {
            def exploitConditions = ["b.targetTUser=:targetTUser", "b.type=:exploitType", "b.enable=1"]
            def exploitParams = [targetTUser: session.user, exploitType: Constants.FLAW_EXPOIT]

            if (taskName) {
                exploitConditions.add("b.title like :taskName")
                exploitParams.taskName = "%${taskName}%"
            }
            if (dateCreated) {
                exploitConditions.add("date(b.dateCreated) = :dateCreated")
                exploitParams.dateCreated = new SimpleDateFormat("yyyy-MM-dd").parse(dateCreated)
            }

            def exploitQuery = "from Task as b where " + exploitConditions.join(" and ") + " order by b.dateCreated desc"
            def exploitTasks = Task.executeQuery(exploitQuery, exploitParams)


            // 转换为统一格式
            exploitTasks.each { task ->
                allTaskList.add([
                        id          : task.id,
                        taskName    : task.title,
                        taskType    : 1,
                        taskTypeStr : "验证任务",
                        dateCreated : task.dateCreated,
                        status      : task.status,
                        statusStr   : task.statusStr(),
                        originalTask: task
                ])
            }
        }

        // 2. 查询处置任务 (type=3)
        if (!taskType || taskType == 2) {
            def disposalConditions = ["b.targetTUser=:targetTUser", "b.type=:disposalType", "b.enable=1", "b.isExpoitPatch is null"]
            def disposalParams = [targetTUser: session.user, disposalType: Constants.FLAW_PATCH]

            if (taskName) {
                disposalConditions.add("b.title like :taskName")
                disposalParams.taskName = "%${taskName}%"
            }
            if (dateCreated) {
                disposalConditions.add("date(b.dateCreated) = :dateCreated")
                disposalParams.dateCreated = new SimpleDateFormat("yyyy-MM-dd").parse(dateCreated)
            }

            def disposalQuery = "from Task as b where " + disposalConditions.join(" and ") + " order by b.dateCreated desc"
            def disposalTasks = Task.executeQuery(disposalQuery, disposalParams)


            // 转换为统一格式
            disposalTasks.each { task ->
                allTaskList.add([
                        id          : task.id,
                        taskName    : task.title,
                        taskType    : 2,
                        taskTypeStr : "处置任务",
                        dateCreated : task.dateCreated,
                        status      : task.status,
                        statusStr   : task.statusStr(),
                        originalTask: task
                ])
            }
        }

        // 3. 查询补丁任务
        if (!taskType || taskType == 3) {
            def patchConditions = ["b.tuser=:tuser", "b.isDisposalTaskPatch=0"]
            def patchParams = [tuser: session.user]

            if (taskName) {
                patchConditions.add("b.patchName like :taskName")
                patchParams.taskName = "%${taskName}%"
            }
            if (dateCreated) {
                patchConditions.add("date(b.dateCreated) = :dateCreated")
                patchParams.dateCreated = new SimpleDateFormat("yyyy-MM-dd").parse(dateCreated)
            }

            def patchQuery = "from PatchInfo as b where " + patchConditions.join(" and ") + " order by b.dateCreated desc"
            def patchTasks = PatchInfo.executeQuery(patchQuery, patchParams)


            // 转换为统一格式
            patchTasks.each { patch ->
                // 补丁任务状态转换
                def statusStr = ""
                switch (patch.status) {
                    case "0": statusStr = "未提交"; break
                    case "1": statusStr = "已提交"; break
                    case "2": statusStr = "审核未通过"; break
                    case "3": statusStr = "审核通过"; break
                    default: statusStr = "未知"; break
                }

                allTaskList.add([
                        id          : patch.id,
                        taskName    : patch.patchName ?: "(未验证)",
                        taskType    : 3,
                        taskTypeStr : "补丁任务",
                        dateCreated : patch.dateCreated,
                        status      : patch.status,
                        statusStr   : statusStr,
                        originalTask: patch
                ])
            }
        }

        // 按创建时间排序
        allTaskList.sort { a, b -> b.dateCreated <=> a.dateCreated }

        // 分页处理
        def totalCount = allTaskList.size()
        def startIndex = params.offset
        def endIndex = Math.min(startIndex + params.max, totalCount)
        def pagedTaskList = totalCount > 0 ? allTaskList[startIndex..<endIndex] : []

        // 如果没有指定任务类型，则统计所有类型的总数
        exploitTotal = Task.executeQuery("select count(*) from Task as b where b.targetTUser=:targetTUser and b.type=:type and b.enable=1",
                [targetTUser: session.user, type: Constants.FLAW_EXPOIT])[0]
        disposalTotal = Task.executeQuery("select count(*) from Task as b where b.targetTUser=:targetTUser and b.type=:type and b.enable=1 and b.isExpoitPatch is null",
                [targetTUser: session.user, type: Constants.FLAW_PATCH])[0]
        patchTotal = PatchInfo.executeQuery("select count(*) from PatchInfo as b where b.tuser=:tuser and b.isDisposalTaskPatch=0",
                [tuser: session.user])[0]

        [
                taskList         : pagedTaskList,
                taskInstanceTotal: totalCount,
                count1           : exploitTotal,
                count2           : disposalTotal,
                count3           : patchTotal,
                obj              : params
        ]

        def res = [taskList: pagedTaskList, taskInstanceTotal: totalCount, count1: exploitTotal, count2: disposalTotal, count3: patchTotal, obj: params]
        if (params.returnType) {
            render(res as JSON)
        } else {
            [taskList: pagedTaskList, taskInstanceTotal: totalCount, count1: exploitTotal, count2: disposalTotal, count3: patchTotal, obj: params]
        }
    }


    def exportExploits = {
        // 导出我的验证到csv文件
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        def taskList = Task.executeQuery("from Task as b where b.targetTUser=:targetTUser and b.type=:type and b.enable=1 order by dateCreated desc", [targetTUser: session.user, type: Constants.FLAW_EXPOIT], [sort: "dateCreated", order: "desc"])
        def sb = new StringBuffer()
        def formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        sb.append("验证名称,下发时间,验证任务状态\n")
        for (Task t : taskList) {
            sb.append(t.title).append(",").append(formatter.format(t.dateCreated)).append(",").append(t.statusStr())
            sb.append("\n")
        }
        response.setHeader('Content-disposition', 'attachment;filename=myexploits.csv')
        OutputStream out = new BufferedOutputStream(response.outputStream)
        try {
            out.write(sb.toString().getBytes())
        } finally {
            out.close()
            return false
        }
    }

    /**
     * 任务列表-补丁任务
     */
    def mypatchInfo = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0

        // 查询参数
        def taskName = params.taskName?.trim()
        def status = params.status?.trim()
        def dateCreated = params.dateCreated

        // 构建查询条件
        def criteria = PatchInfo.createCriteria()

        // 主查询
        def patchInfoInstanceList = criteria.list(max: params.max, offset: params.offset, sort: "dateCreated", order: "desc") {
            eq("tuser", session.user)
            eq("isDisposalTaskPatch", 0)

            if (taskName) {
                ilike("patchName", "%${taskName}%")
            }
            if (status) {
                eq("status", status)
            }
            if (dateCreated) {
                between("dateCreated", dateCreated, DateUtil.getTomorrowStr(dateCreated))
            }
        }

        // 总数查询
        def patchInfoInstanceTotal = PatchInfo.createCriteria().get {
            eq("tuser", session.user)
            eq("isDisposalTaskPatch", 0)

            if (taskName) {
                ilike("patchName", "%${taskName}%")
            }
            if (status != null) {
                eq("status", status)
            }
            if (dateCreated) {
                between("dateCreated", dateCreated, DateUtil.getTomorrowStr(dateCreated))
            }
            projections {
                count("id")
            }
        }

        // 统计三种状态的任务数量
        def pendingCount = PatchInfo.createCriteria().count {
            eq("tuser", session.user)
            eq("isDisposalTaskPatch", 0)
            eq("status", "1")
        }

        def rejectedCount = PatchInfo.createCriteria().count {
            eq("tuser", session.user)
            eq("isDisposalTaskPatch", 0)
            eq("status", "2")
        }

        def approvedCount = PatchInfo.createCriteria().count {
            eq("tuser", session.user)
            eq("isDisposalTaskPatch", 0)
            eq("status", "3")
        }


        def res = [patchInfoInstanceList: patchInfoInstanceList, patchInfoInstanceTotal: patchInfoInstanceTotal, count1: pendingCount, count2: rejectedCount, count3: approvedCount, obj: params]
        if (params.returnType) {
            render(res as JSON)
        } else {
            [patchInfoInstanceList: patchInfoInstanceList, patchInfoInstanceTotal: patchInfoInstanceTotal, count1: pendingCount, count2: rejectedCount, count3: approvedCount, obj: params]
        }
    }


    /**
     * 任务列表-处置任务
     */
    def mydisposition = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0

        // 查询参数
        def taskName = params.taskName?.trim()
        def status = params.status ? params.int('status') : null
        def dateCreated = params.dateCreated

        // 构建基础查询条件和参数
        def queryParams = [targetTUser: session.user, type: Constants.FLAW_PATCH]
        def countParams = [targetTUser: session.user, type: Constants.FLAW_PATCH]

        // 基础HQL
        def baseHql = "from Task as b where b.targetTUser=:targetTUser and b.type=:type and b.enable=1 and b.isExpoitPatch is null"
        def countHql = "select count(*) from Task as b where b.targetTUser=:targetTUser and b.type=:type and b.isExpoitPatch is null and b.enable=1"

        // 添加任务名称条件
        if (taskName) {
            baseHql += " and b.title like :taskName"
            countHql += " and b.title like :taskName"
            queryParams.taskName = "%${taskName}%"
            countParams.taskName = "%${taskName}%"
        }

        // 添加状态条件
        if (status != null) {
            baseHql += " and b.status = :status"
            countHql += " and b.status = :status"
            queryParams.status = status
            countParams.status = status
        }

        // 添加创建日期条件
        if (dateCreated) {
            baseHql += " and b.dateCreated between :dateCreated and :tomorrow"
            countHql += " and b.dateCreated between :dateCreated and :tomorrow"
            queryParams.dateCreated = dateCreated
            queryParams.tomorrow = DateUtil.getTomorrowStr(dateCreated)
            countParams.dateCreated = dateCreated
            countParams.tomorrow = DateUtil.getTomorrowStr(dateCreated)
        }

        // 添加排序
        baseHql += " order by b.dateCreated desc"

        // 执行查询
        def taskList = Task.executeQuery(baseHql, queryParams, [max: params.max, offset: params.offset])
        def taskInstanceTotal = Task.executeQuery(countHql, countParams)[0]

        // 统计三种状态的任务数量
        def statusParams = [targetTUser: session.user, type: Constants.FLAW_PATCH]

        // 进行中(status=1)的任务数量
        def inProgressCount = Task.executeQuery(
                "select count(*) from Task where targetTUser=:targetTUser and type=:type and enable=1 and isExpoitPatch is null and status=1",
                statusParams
        )[0] ?: 0

        // 完成(status=2)的任务数量
        def completedCount = Task.executeQuery(
                "select count(*) from Task where targetTUser=:targetTUser and type=:type and enable=1 and isExpoitPatch is null and status=2",
                statusParams
        )[0] ?: 0

        // 取消(status=3)的任务数量
        def canceledCount = Task.executeQuery(
                "select count(*) from Task where targetTUser=:targetTUser and type=:type and enable=1 and isExpoitPatch is null and status=3",
                statusParams
        )[0] ?: 0


        // 原有分中心相关逻辑
        def flawIdtemp = new ArrayList<Long>()
        def yzMapInstance = new HashMap()
        TUser user = session.user
        def name = user.getNickName()

        if (name.endsWith('分中心')) {
            for (def taskTemp : taskList) {
                flawIdtemp.add(taskTemp.flawId)
            }
            if (!flawIdtemp.isEmpty()) {
                def isyzList = Task.executeQuery("from Task as b where b.flaw.id in (:list) and b.type=:type and b.enable=1 order by dateCreated desc", [list: flawIdtemp, type: Constants.FLAW_EXPOIT])
                for (def temp : isyzList) {
                    yzMapInstance.put(temp.flawId, temp.flawId)
                }
            }
        }


        def res = [taskList: taskList, taskInstanceTotal: taskInstanceTotal, offset: params.offset, yzMapInstance: yzMapInstance, count1: inProgressCount, count2: completedCount, count3: canceledCount, obj: params]

        if (params.returnType) {
            render(res as JSON)
        } else {
            [taskList: taskList, taskInstanceTotal: taskInstanceTotal, offset: params.offset, yzMapInstance: yzMapInstance, count1: inProgressCount, count2: completedCount, count3: canceledCount, obj: params]

        }
    }

    /**
     * 友情链接
     */
    def getAllUnitMember = {
        def unitMemberList = TUser.findAllByUserType(100201)
        render(view: "/common/unitMemberList", model: [unitMemberList: unitMemberList])
    }

    /**
     * "用户中心"中"我的证书"列表
     */
    def mycerlist = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0

        def cerList
        def cerCount
        if ("BS".equals(params.cerType) || params.cerType == null || params.cerType == '') {
            //报送证书 cerType=1
            cerList = Certificate.findAllByTuserAndCerType(session.user, (byte) 1, [max: params.max, offset: params.offset])
            cerCount = Certificate.countByTuserAndCerType(session.user, (byte) 1)
            params.cerType = "BS"
        } else if ("YC".equals(params.cerType)) {
            //原创证书 cerType=2
            cerList = Certificate.findAllByTuserAndCerType(session.user, (byte) 2, [max: params.max, offset: params.offset])
            cerCount = Certificate.countByTuserAndCerType(session.user, (byte) 2)
        } else if ("BMZ".equals(params.cerType)) {
            //原创证书 cerType=2
            cerList = Certificate.findAllByTuserAndCerType(session.user, (byte) 3, [max: params.max, offset: params.offset])
            cerCount = Certificate.countByTuserAndCerType(session.user, (byte) 3)
        } else if ("TC".equals(params.cerType)) {
            //原创证书 cerType=2
            cerList = Certificate.findAllByTuserAndCerType(session.user, (byte) 4, [max: params.max, offset: params.offset])
            cerCount = Certificate.countByTuserAndCerType(session.user, (byte) 4)
        }
        [cerList: cerList, obj: params, cerCount: cerCount]
    }


    /**
     * “用户中心”中“我的验证”中漏洞的查看
     */
    def myExploitFlawShow = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        def flawInstance = Flaw.get(params.id)
        def exploit = Exploit.findByFlaw(flawInstance)
        if (!flawInstance || exploit?.tuser?.id != session?.user?.id) {
            //如果验证的漏洞所分配的验证人不是当前登录人
            render(view: "/error")
        } else if (flawInstance.status == 9 && flawInstance.isOpen == 1 && flawInstance.enable == 1
                && new Date().compareTo(flawInstance.openTime) >= 0
                && !flawInstance?.parentFlaw) {
            redirect(controller: "flaw", action: "show", params: [id: params.id])
        } else {
            render(view: "notStorageFlawShow", model: [flawInstance: flawInstance])
        }
    }

    /**
     * “用户中心”中“我的补丁”中漏洞的查看
     */
    def myPatchFlawShow = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        def flawInstance = Flaw.get(params.id)
        def patchInfoInstance = PatchInfo.findByFlaw(flawInstance)
        if (!flawInstance || patchInfoInstance?.tuser?.id != session?.user?.id) {
            render(view: "/error")
        } else if (flawInstance.status == 9 && flawInstance.isOpen == 1
                && flawInstance.enable == 1 && new Date().compareTo(flawInstance.openTime) >= 0
                && !flawInstance.parentFlaw) {
            redirect(controller: "flaw", action: "show", params: [id: params.id])
        } else {
            render(view: "notStorageFlawShow", model: [flawInstance: flawInstance])
        }
    }

    /**
     * 显示"用户中心"中"我的漏洞"信息列表
     * 100203 企业用户特有的
     */
    def myflaw = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        if (session.user.userType != 100201 && session.user.userType != 100202 && session.user.userType != 100203) {
            render(view: "/error")
        }
        def flag = params.flag
        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0
        if ("".equals(flag) || "reportToMe".equals(flag) || flag == null) {
            //上报给我的漏洞
            //已归档的 user_id为session userId的 enable为1的
            def manufacturer = session?.user?.manufacturer
            def c = Flaw.createCriteria()
            //上报给我的漏洞
            def flawList = c.list {
                and {
                    eq("manufacturer", manufacturer)
                    eq("status", 9)
                    eq("enable", 1)
                    eq("isOpen", 1)
                    isNull("parentFlaw")
                    le("openTime", new Date())
                }
                maxResults(params.max)
                firstResult(params.offset)
                order("dateCreated", "desc")
            }
            def countCriteria = Flaw.createCriteria()
            def flawInstanceCount = countCriteria.get {
                and {
                    eq("manufacturer", manufacturer)
                    eq("status", 9)
                    eq("enable", 1)
                    eq("isOpen", 1)
                    isNull("parentFlaw")
                    le("openTime", new Date())
                }
                projections { rowCount() }
            }
            flag = "reportToMe"
            [flawInstanceList: flawList, flawInstanceCount: flawInstanceCount, flag: flag]
        } else if ("reflectToMe".equals(flag)) {
            //影响我产品的漏洞
            def hql = " from Flaw f where exists(select fp.id from FlawProduct fp where f.id=fp.flaw.id and exists( " +
                    "select pi.id from ProductInfo pi where fp.product.id = pi.id and pi.manufacturer = ? " +
                    ")) order by f.dateCreated desc"
            System.out.println("max=" + params.max + "|offset=" + params.offset);
            def flawList = Flaw.executeQuery(hql, [session?.user?.manufacturer], [max: params.max, offset: params.offset])

            def countHql = " select count(*) as cnt from Flaw f where exists( " +
                    "select fp.id from FlawProduct fp where f.id=fp.flaw.id and exists( " +
                    "select pi.id from ProductInfo pi where fp.product.id = pi.id and manufacturer = ? ))"
            def count = Flaw.executeQuery(countHql, [session?.user?.manufacturer])[0]
            [flawInstanceList: flawList, flawInstanceCount: count, flag: flag]
        }
    }

    /**
     * 显示"用户中心"中"我的漏洞"中的漏洞详细信息
     */
    def myflawShow = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        def flawInstance = Flaw.get(params.id)
        def manufacturer = session?.user?.manufacturer
        def c = Flaw.createCriteria()
        def flawList = c.list {
            and {
                eq("manufacturer", manufacturer)
                eq("status", 9)
                eq("enable", 1)
                eq("isOpen", 1)
                isNull("parentFlaw")
                le("openTime", new Date())
            }
        }

        def hql = " from Flaw f where exists(select fp.id from FlawProduct fp where f.id=fp.flaw.id and exists( " +
                "select pi.id from ProductInfo pi where fp.product.id = pi.id and pi.manufacturer = ? " +
                ")) order by f.dateCreated desc"
        def list = Flaw.executeQuery(hql, [manufacturer])
        flawList.addAll(0, list)
        for (def f : flawList) {
            print f.hashCode() + " "
        }
        println ""
        println flawList
        println flawInstance
        println flawInstance.hashCode()
        println flawList.contains(flawInstance)
        if (!flawInstance || !flawList.contains(flawInstance)) {
            render(view: "/error")
        } else if (flawInstance.status == 9 && flawInstance.isOpen == 1
                && flawInstance.enable == 1 && new Date().compareTo(flawInstance.openTime) >= 0
                && !flawInstance?.parentFlaw) {
            redirect(controller: "flaw", action: "show", params: [id: params.id])
        } else {
            render(view: 'notStorageFlawShow', model: [flawInstance: flawInstance])
        }
    }

    /**
     * 显示我上传的补丁信息
     */
    def mypatchInfoShow = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        def patchInfoInstance = PatchInfo.get(params.id)
        if (!patchInfoInstance) {
            render(view: "/error")
        } else if ("3".equals(patchInfoInstance.status) && patchInfoInstance?.flaw?.status == 9
                && patchInfoInstance?.flaw?.isOpen == 1 && patchInfoInstance?.flaw?.enable == 1
                && new Date().compareTo(patchInfoInstance?.flaw?.openTime) >= 0
                && !patchInfoInstance?.flaw?.parentFlaw) {
            redirect(controller: "patchInfo", action: "show", params: [id: params.id])
        } else {
            def flawInstance = patchInfoInstance?.flaw
            def manufacturer = session?.user?.manufacturer
            def c = Flaw.createCriteria()
            def flawList = c.list {
                and {
                    eq("manufacturer", manufacturer)
                    eq("status", 9)
                    eq("enable", 1)
                    eq("isOpen", 1)
                    isNull("parentFlaw")
                    le("openTime", new Date())
                }
            }

            def hql = " from Flaw f where exists(select fp.id from FlawProduct fp where f.id=fp.flaw.id and exists( " +
                    "select pi.id from ProductInfo pi where fp.product.id = pi.id and pi.manufacturer = ? " +
                    ")) order by f.dateCreated desc"
            def list = Flaw.executeQuery(hql, [manufacturer])
            flawList.addAll(0, list)
            def flawIdList = new ArrayList()
            for (def f : flawList) {
                flawIdList.add(f.id)
            }
            if (!flawIdList.contains(flawInstance?.id)) {
                render(view: "/error")
                return
            }
            [patchInfoInstance: patchInfoInstance]
        }
    }

    /**
     * 显示"我的验证"中的具体验证信息
     */
    def myexploitShow = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        def exploitInstance = Exploit.get(params.id)
        if (!exploitInstance) {
            render(view: "/error")
        } else {
            def flawInstance = exploitInstance?.flaw
            def manufacturer = session?.user?.manufacturer
            def c = Flaw.createCriteria()
            def flawList = c.list {
                and {
                    eq("manufacturer", manufacturer)
                    eq("status", 9)
                    eq("enable", 1)
                    eq("isOpen", 1)
                    isNull("parentFlaw")
                    le("openTime", new Date())
                }
            }
            def hql = " from Flaw f where exists(select fp.id from FlawProduct fp where f.id=fp.flaw.id and exists( " +
                    "select pi.id from ProductInfo pi where fp.product.id = pi.id and pi.manufacturer = ? " +
                    ")) order by f.dateCreated desc"
            def list = Flaw.executeQuery(hql, [manufacturer])
            flawList.addAll(0, list)
            def flawIdList = new ArrayList()
            for (def f : flawList) {
                flawIdList.add(f.id)
            }
            if (!flawIdList.contains(flawInstance?.id)) {
                render(view: "/error")
                return
            }
            println "true"
            render(view: "/exploit/show", model: [exploitInstance: exploitInstance])
        }
    }

    /**
     * 白帽子列表
     */
    def userList = {
        //2018-01-31张某关闭该接口
        redirect(controller: "flaw", action: "index1")
        return

        params.max = params.max ? params.int('max') : 20
        params.offset = params.offset ? params.int('offset') : 0

        def userlist = new ArrayList()
        def count = TUser.executeQuery("select count(*) from TUser where userType = 100200 and status = 100304 and id != 1")[0]

        def paramList = new ArrayList()
        def sql = "select tuser.id userId,tuser.nick_name nickName, ifnull(f.count,0) flawCount from tuser " +
                "left join (select user_id, count(*) count from flaw where status = ? and enable = ? " +
                "group by user_id)f on tuser.id = f.user_id where tuser.user_type=? " +
                "and tuser.status=? and tuser.id<>1 order by flawCount desc,id asc limit ?,?;"
        paramList.add(9)
        paramList.add(1)
        paramList.add(100200)
        paramList.add(100304)
        paramList.add(params.offset)
        paramList.add(params.max)

        def res = new groovy.sql.Sql(dataSource).rows(sql, paramList)
        res.each {
            def user = new TUser()
            user.id = it.userId
            user.nickName = it.nickName
            user.flawCount = it.flawCount
            userlist.add(user)
        }

        [userList: userlist, count: count]
    }

    //仅作为备份，无意义
    def userFlawListBak = {

        println "offset_new=" + params.offset_new

        session.setAttribute("offset", params.offset_new ? params.int('offset_new') : (params.offset ? params.int('offset') : 0))

        params.max = params.max ? params.int('max') : 20
        params.offset = params.offset_new ? params.int('offset_new') : (params.offset ? params.int('offset') : 0)
        def userlist = new ArrayList()

        println "number=" + params.number
        def sqlCount = ""
        def sql = ""
        def countList = new ArrayList()
        def paramList = new ArrayList()
        TUser tuser = TUser.findById(session.user.id)
        countList.add(tuser.getManufacturer().id)
        paramList.add(tuser.getManufacturer().id)
        if (params.number) {
            sqlCount = "SELECT  count(*) as countAs  from  tuser_flaw a LEFT JOIN flaw b  on  a.flaw_id=b.id where a.manufacturer_id =? and b.number like ?";
            sql = "SELECT  b.*,c.`value`,c.`name` as names  from  tuser_flaw a LEFT JOIN flaw b  on  a.flaw_id=b.id LEFT JOIN  dictionary_info c  on   b.serverity_id=c.id where a.manufacturer_id =? and b.number like ? order by b.storage_time desc limit ?,?;"
            countList.add("%" + params.number.trim() + "%")
            paramList.add("%" + params.number.trim() + "%")
        } else {
            sqlCount = "SELECT  count(*) as countAs  from  tuser_flaw a LEFT JOIN flaw b  on  a.flaw_id=b.id where a.manufacturer_id =?";
            sql = "SELECT  b.*,c.`value`,c.`name` as names  from  tuser_flaw a LEFT JOIN flaw b  on  a.flaw_id=b.id LEFT JOIN  dictionary_info c  on   b.serverity_id=c.id where a.manufacturer_id =? order by b.storage_time desc limit ?,?;"
        }
        List<Map<String, Object>> dataResult = SQLUtil.getResult(sqlCount, countList)

        Long flawInstanceTotal = Long.valueOf(0)
        if (dataResult != null && dataResult.size() > 0) {
            flawInstanceTotal = Long.valueOf(dataResult.get(0).get("countAs").toString())
        }

        paramList.add(params.offset)
        paramList.add(params.max)

        def res = new groovy.sql.Sql(dataSource).rows(sql, paramList)

        for (int i = 0; i < res.size(); i++) {
            def sql1 = "SELECT b.name from flaw_product a LEFT JOIN product_info b on a.product_id=b.id where  a.flaw_id=" + res.get(i).get("id")
            def res1 = new groovy.sql.Sql(dataSource).rows(sql1)

            StringBuffer aa = new StringBuffer()
            for (int j = 0; j < res1.size(); j++) {
                aa.append(res1.get(j).get("name") + ";")
            }

            println("res.get(i).get" + res.get(i).get("id"))

            res.get(i).put("productName", aa)
        }


        [flawInstanceList: res, flawInstanceTotal: flawInstanceTotal, number: params.number]
    }

    def userFlawList = {
        session.setAttribute("offset", params.offset_new ? params.int('offset_new') : (params.offset ? params.int('offset') : 0))

        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset_new ? params.int('offset_new') : (params.offset ? params.int('offset') : 0)

        def userlist = new ArrayList()
        println "number=" + params.number

        TUser tuser = TUser.findById(session.user.id)
        def manufacturerId = tuser.getManufacturer().id

        // 1. 统计漏洞总数
        def totalFlawsSql = "SELECT COUNT(*) as count FROM tuser_flaw WHERE manufacturer_id = ?"
        def totalFlaws = new groovy.sql.Sql(dataSource).firstRow(totalFlawsSql, [manufacturerId]).count

        // 2. 统计待审核个数(status=1)
        def pendingReviewSql = """
            SELECT COUNT(DISTINCT a.flaw_id) as count 
            FROM tuser_flaw a 
            INNER JOIN (
                SELECT flaw_id, status 
                FROM flaw_web_use_log 
                WHERE (flaw_id, last_updated) IN (
                    SELECT flaw_id, MAX(last_updated) 
                    FROM flaw_web_use_log 
                    GROUP BY flaw_id
                )
            ) b ON a.flaw_id = b.flaw_id 
            WHERE a.manufacturer_id = ? AND b.status = 1
        """
        def pendingReviewCount = new groovy.sql.Sql(dataSource).firstRow(pendingReviewSql, [manufacturerId]).count

        // 3. 统计审核通过个数(status=2)
        def approvedSql = """
            SELECT COUNT(DISTINCT a.flaw_id) as count 
            FROM tuser_flaw a 
            INNER JOIN (
                SELECT flaw_id, status 
                FROM flaw_web_use_log 
                WHERE (flaw_id, last_updated) IN (
                    SELECT flaw_id, MAX(last_updated) 
                    FROM flaw_web_use_log 
                    GROUP BY flaw_id
                )
            ) b ON a.flaw_id = b.flaw_id 
            WHERE a.manufacturer_id = ? AND b.status = 2
        """
        def approvedCount = new groovy.sql.Sql(dataSource).firstRow(approvedSql, [manufacturerId]).count

        // 4. 统计审核驳回个数(status=3)
        def rejectedSql = """
            SELECT COUNT(DISTINCT a.flaw_id) as count 
            FROM tuser_flaw a 
            INNER JOIN (
                SELECT flaw_id, status 
                FROM flaw_web_use_log 
                WHERE (flaw_id, last_updated) IN (
                    SELECT flaw_id, MAX(last_updated) 
                    FROM flaw_web_use_log 
                    GROUP BY flaw_id
                )
            ) b ON a.flaw_id = b.flaw_id 
            WHERE a.manufacturer_id = ? AND b.status = 3
        """
        def rejectedCount = new groovy.sql.Sql(dataSource).firstRow(rejectedSql, [manufacturerId]).count

        // 基础SQL部分 - 使用子查询获取最新的flaw_web_use_log记录
        def baseSql = """
            FROM (
                SELECT a.flaw_id, a.manufacturer_id, 
                       (SELECT l.status 
                        FROM flaw_web_use_log l 
                        WHERE l.flaw_id = a.flaw_id 
                        ORDER BY l.last_updated DESC 
                        LIMIT 1) as status
                FROM tuser_flaw a 
                WHERE a.manufacturer_id = ?
                GROUP BY a.flaw_id
            ) main
            LEFT JOIN flaw b ON main.flaw_id = b.id 
            LEFT JOIN dictionary_info c ON b.serverity_id = c.id
        """

        def baseCountSql = "SELECT count(*) as countAs " + baseSql

        // 添加产品关联条件（当需要按产品或版本查询时）
        def productJoin = """
            INNER JOIN flaw_product d ON b.id = d.flaw_id 
            INNER JOIN product_info e ON d.product_id = e.id
        """

        // 构建WHERE条件
        def whereClauses = []
        def whereParams = [manufacturerId] // 第一个参数始终是manufacturerId

        // 处理查询参数
        def number = params.number?.trim()
        def name = params.name?.trim()
        def productEdition = params.productEdition?.trim()
        // 新增查询条件
        def status = params.status
        def title = params.title?.trim()
        def storageTime = params.storageTime

        if (number) {
            whereClauses.add("b.number like ?")
            whereParams.add("%" + number + "%")
        }

        // 状态查询
        if (status) {
            whereClauses.add("main.status = ?")
            whereParams.add(status)
        }

        // 漏洞标题查询
        if (title) {
            whereClauses.add("b.title like ?")
            whereParams.add("%" + title + "%")
        }

        // 收录时间查询
        if (storageTime) {
            whereClauses.add("b.storage_time between ? and ?")
            whereParams.add(storageTime)
            whereParams.add(DateUtil.getTomorrowStr(storageTime))
        }

        // 判断是否需要添加产品关联
        def needProductJoin = name || productEdition

        if (needProductJoin) {
            baseSql += productJoin
            baseCountSql += productJoin

            if (name) {
                whereClauses.add("e.name like ?")
                whereParams.add("%" + name + "%")
            }

            if (productEdition) {
                whereClauses.add("e.edition = ?")
                whereParams.add(productEdition)
            }
        }

        // 构建完整SQL
        def whereClause = whereClauses ? " WHERE " + whereClauses.join(" AND ") : ""

        def sqlCount = baseCountSql + whereClause
        def sql = """
            SELECT b.id, b.number, b.title,b.storage_time as storageTime, c.`value`, c.`name` as names, main.status 
            $baseSql 
            $whereClause 
            ORDER BY b.storage_time DESC 
            LIMIT ?, ?
        """

        // 执行计数查询
        def countParams = whereParams.clone()
        List<Map<String, Object>> dataResult = SQLUtil.getResult(sqlCount, countParams)
        Long flawInstanceTotal = dataResult ? Long.valueOf(dataResult[0].get("countAs").toString()) : 0L

        // 执行主查询
        def queryParams = whereParams.clone()
        queryParams.add(params.offset)
        queryParams.add(params.max)

        def res = new groovy.sql.Sql(dataSource).rows(sql, queryParams)

        // 处理产品名称 和 象限值
        res.each { flaw ->
            def sql1 = "SELECT b.name from flaw_product a LEFT JOIN product_info b on a.product_id=b.id where a.flaw_id=?"
            def products = new groovy.sql.Sql(dataSource).rows(sql1, [flaw.get("id")])

            String productNames = products.collect { it.get("name") }.join(";")
            flaw.put("productName", productNames)

            //	BaseMetric basemetric //基本度量评分
            //	TemporalMetric temporalMetric //时间度量评分
            //	EnvironmentalMetric environmentalMetric //环境度量评分
            Flaw flawInstance = Flaw.get(flaw.get("id"))
            flaw.put("basemetric", flawInstance?.basemetric)
            flaw.put("temporalMetric", flawInstance?.temporalMetric)
            flaw.put("environmentalMetric", flawInstance?.environmentalMetric)
        }

        def res1 = [flawInstanceList: res, flawInstanceTotal: flawInstanceTotal, obj: params, count: totalFlaws, count1: pendingReviewCount, count2: approvedCount, count3: rejectedCount]
        if (params.returnType) {
            render(res1 as JSON)
        } else {
            [flawInstanceList: res, flawInstanceTotal: flawInstanceTotal, obj: params, count: totalFlaws, count1: pendingReviewCount, count2: approvedCount, count3: rejectedCount]
        }
    }

    def resetPwd = {
        //判断链接是否在有效时间内
        println "oooo"
        def passwordReset = PasswordReset.findByRealMD5AndEnable(params.t, 0)

        if (passwordReset) {
            SimpleDateFormat dfs = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
            def begin = passwordReset.lastUpdated.getTime()
            def end = new Date().getTime()
            if ((end - begin) <= passwordReset.unEffectiveTime) {
                [userInstance: passwordReset.user, params: params]
            } else {
                //不在有效时间内
                render(view: "resetPwdFail")
                return;
            }
        } else {
            render(view: "resetPwdFail")
            return;
        }
    }

    def updatePwd = {
        if (!jcaptchaService.validateResponse("imageCaptcha", session.id, params.myCode)) {
            //验证码不正确
            flash.message = 'Certificate error'
            redirect(action: "resetPwd", params: [id: params.userId, pwdResetId: params.pwdResetId, email: params.mail])
            return
        }

        //判断邮箱的MD5值是否相同
        //def passwordReset = PasswordReset.get(params.pwdResetId)
        def passwordReset = PasswordReset.findByRealMD5AndEnable(params.t, 0)
        if (passwordReset) {
            //判断是否在有效时间内
            SimpleDateFormat dfs = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
            def begin = passwordReset.lastUpdated.getTime()
            def end = new Date().getTime()
            if ((end - begin) <= passwordReset.unEffectiveTime) {
                //在有效时间内
                def user = passwordReset.user
                user.password = MD5.getEncryptedPwd(params.newPwd);
                passwordReset.newPassword = user.password
                passwordReset.enable = 1
                passwordReset.save(flush: true)
                user.save(flush: true)
                render(view: "resetPwdSuccess")
                return;
            } else {
                //不在有效时间内
                render(view: "resetPwdFail")
                return;
            }
        } else {
            //邮箱输入有误
            flash.message = "Email input error"
            redirect(action: "resetPwd", params: [id: params.userId, pwdResetId: params.pwdResetId])
            return
        }
    }

    def batchFlawList = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        if (session.user.userType != 100201 && session.user.userType != 100202 && session.user.userType != 100203 && session.user.status != 100304) {
            render(view: "error")
            return
        }
        params.max = Math.min(params.max ? params.int('max') : 10, 100)
        params.offset = params.offset ? params.int('offset') : 0
        def batchFlawList = BatchFlaw.findAllByTuser(session.user, [sort: "dateCreated", order: "desc", max: params.max, offset: params.offset])
        def counts = BatchFlaw.countByTuser(session.user)
        [batchFlawList: batchFlawList, counts: counts]
    }

    def flawPocList = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        params.max = Math.min(params.max ? params.int('max') : 10, 100)
        params.offset = params.offset ? params.int('offset') : 0
        def flawPocList = FlawPoc.findAllByUser(session.user, [sort: "createTime", order: "desc", max: params.max, offset: params.offset])
        def counts = FlawPoc.countByUser(session.user)
        [flawPocList: flawPocList, counts: counts]
    }

    def mynews = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        params.max = Math.min(params.max ? params.int('max') : 10, 100)
        params.offset = params.offset ? params.int('offset') : 0
        def newsCriteria = TUserNews.createCriteria()
        def newsInstanceList = newsCriteria.list {
            and {
                eq('tuser', session.user)
                if (params.status) {
                    eq('status', params.int('status'))
                }
                if (params.title) {
                    news {
                        like('title', '%' + params.title + '%')
                    }
                }
                if (params.dateCreated) {
                    eq('dateCreated', new SimpleDateFormat("yyyy-MM-dd").parse(params.dateCreated))
                }
            }
            maxResults(params.max)
            firstResult(params.offset)
            order('dateCreated', 'desc')
        }
        def countCriteria = TUserNews.createCriteria()
        def newsInstanceTotal = countCriteria.get {
            and {
                eq('tuser', session.user)
                if (params.status) {
                    eq('status', params.int('status'))
                }
                if (params.title) {
                    news {
                        like('title', '%' + params.title + '%')
                    }
                }
                if (params.dateCreated) {
                    eq('dateCreated', new SimpleDateFormat("yyyy-MM-dd").parse(params.dateCreated))
                }
            }
            projections { rowCount() }
        }
        def countUnread = TUserNews.countByTuserAndStatus(session.user, 0)
        def countRead = TUserNews.countByTuserAndStatus(session.user, 1)
        [tusernewsList: newsInstanceList, tusernewsCount: newsInstanceTotal, countUnread: countUnread, countRead: countRead]
    }

    /**
     * 系统消息
     */
    def mynewsNew = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        params.max = Math.min(params.max ? params.int('max') : 10, 100)
        params.offset = params.offset ? params.int('offset') : 0
        def newsCriteria = TUserNews.createCriteria()
        def newsInstanceList = newsCriteria.list {
            and {
                eq('tuser', session.user)
                if (params.status) {
                    eq('status', params.int('status'))
                }
                if (params.title) {
                    news {
                        like('title', '%' + params.title + '%')
                    }
                }
                if (params.dateCreated) {
                    def date = new SimpleDateFormat("yyyy-MM-dd").parse(params.dateCreated)
                    between('dateCreated', date, date + 1)
                }
            }
            maxResults(params.max)
            firstResult(params.offset)
            order('dateCreated', 'desc')
            // 确保关联对象被正确加载
            join('news')
        }
        def countCriteria = TUserNews.createCriteria()
        def newsInstanceTotal = countCriteria.get {
            and {
                eq('tuser', session.user)
                if (params.status) {
                    eq('status', params.int('status'))
                }
                if (params.title) {
                    news {
                        like('title', '%' + params.title + '%')
                    }
                }
                if (params.dateCreated) {
                    def date = new SimpleDateFormat("yyyy-MM-dd").parse(params.dateCreated)
                    between('dateCreated', date, date + 1)
                }
            }
            projections { rowCount() }
        }
        def countUnread = TUserNews.countByTuserAndStatus(session.user, 0)
        def countRead = TUserNews.countByTuserAndStatus(session.user, 1)

       if (params.returnType) {
           // 为了确保JSON序列化正确，手动构建数据结构
           def tusernewsListForJson = []
           newsInstanceList.each { tuserNews ->
               def newsData = [:]
               newsData.id = tuserNews.id
               newsData.status = tuserNews.status
               newsData.dateCreated = DateUtil.get4yMdHm(tuserNews.dateCreated)
               newsData.readTime = tuserNews.readTime

               // 确保news对象数据完整
               if (tuserNews.news) {
                   newsData.news = [:]
                   newsData.news.id = tuserNews.news.id
                   newsData.news.title = tuserNews.news.title
                   newsData.news.content = tuserNews.news.content
                   newsData.news.type = tuserNews.news.type
                   newsData.news.dateCreated = DateUtil.get4yMdHm(tuserNews.news.dateCreated)
               }

               tusernewsListForJson.add(newsData)
           }

           def res = [tusernewsList: tusernewsListForJson, tusernewsCount: newsInstanceTotal, countUnread: countUnread, countRead: countRead, obj: params]

           render(res as JSON)
        } else {
            [tusernewsList: newsInstanceList, tusernewsCount: newsInstanceTotal, countUnread: countUnread, countRead: countRead, obj: params]
        }
    }


    def caTest = {
        println params

        response.setContentType("text/plain;charset=UTF-8")
        response.setCharacterEncoding("UTF-8")
        X509Certificate[] certs = (X509Certificate[]) request.getAttribute("javax.servlet.request.X509Certificate");
        PrintWriter out = response.getWriter();
        if (certs != null) {
            int count = certs.length;
            out.println("共检测到[" + count + "]个客户端证书");
            for (int i = 0; i < count; i++) {
                out.println("客户端证书 [" + (++i) + "]： ");
                out.println("校验结果：" + verifyCertificate(certs[--i]));
                out.println("证书详细：\r" + certs[i].toString());
                out.println("发行者：" + certs[i].getIssuerDN().getName())
                String issuerName = certs[i].getIssuerDN().getName()
                String[] array = issuerName.split(",")
                String[] array2 = array[0].split("=")
                System.out.println("CN=" + array2[0]);
                System.out.println(array2[1]);
                out.println("sadfsadfdsaf")
                out.println("公钥：\r" + certs[i].getPublicKey())
                out.println("公钥：\r")
            }
        } else {
            if ("https".equalsIgnoreCase(request.getScheme())) {
                out.println("这是一个HTTPS请求，但是没有可用的客户端证书");
            } else {
                out.println("这不是一个HTTPS请求，因此无法获得客户端证书列表 ");
            }
        }
        out.close();
    }

    /**
     * 校验证书是否过期
     * @param certificate
     * @return
     */
    public static boolean verifyCertificate(X509Certificate certificate) {
        boolean valid = true;
        try {
            certificate.checkValidity();
        } catch (Exception e) {
            e.printStackTrace();
            valid = false;
        }
        return valid;
    }

    /**
     * 我的资产
     */
    def myasset = {
        //666
        //RedisUtil.setString("name1","1231")
        String name = RedisUtil.getString("name1")
        println "name|" + name
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        params.max = Math.min(params.max ? params.int('max') : 10, 100)
        params.offset = params.offset ? params.int('offset') : 0
        def userAssetList = UserAsset.findAllByUser(session.user, [sort: "dateCreated", order: "desc", max: params.max, offset: params.offset])
        def count = UserAsset.countByUser(session.user)

        [userAssetList: userAssetList, userAssetTotal: count]
    }


    /**我的产品和服务
     * 关键字
     */
    def keyWordListHis = {
        //666
        //RedisUtil.setString("name1","1231")
        String name = RedisUtil.getString("name1")
        println "name|" + name
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        params.max = Math.min(params.max ? params.int('max') : 10, 100)
        params.offset = params.offset ? params.int('offset') : 0
        TUser tuser = TUser.findById(session.user.id)
        def userAssetList = TUserKeyWordLog.findAllByManufacturerId(tuser.getManufacturer().id, [sort: "dateCreated", order: "desc", max: params.max, offset: params.offset])
        def count = TUserKeyWordLog.countByManufacturerId(tuser.getManufacturer().id)

        [keyWordLists: userAssetList, userAssetTotal: count]
    }

    /**
     * 关键字
     */
    def keyWordList = {
        String name = RedisUtil.getString("name1")
        println "name|" + name
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        params.max = Math.min(params.max ? params.int('max') : 10, 100)
        params.offset = params.offset ? params.int('offset') : 0
        int offset1 = params.offset1 ? params.int('offset1') : 0
        TUser tuser = TUser.findById(session.user.id)


        StringBuilder sql = new StringBuilder() //查询sql
        StringBuilder countSql = new StringBuilder() //count sql
        List<Object> paramList = new ArrayList() //参数集合
        List<Object> countParamList = new ArrayList() //统计参数集合
        sql.append("select * from (SELECT null as id, name, examine_date examineDate, status, manufacturer_id manufacturerId FROM tuser_key_word_log WHERE parent_id IS NULL AND status IN (1, 3) UNION ALL SELECT l.parent_id id, l.name, l.examine_date examineDate, l.status, l.manufacturer_id manufacturerId FROM tuser_key_word_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM tuser_key_word_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id left join tuser_key_word k on l.parent_id = k.id WHERE l.status IN (1, 3) and k.is_del = 1 UNION ALL SELECT k.id, k.name, k.examine_date examineDate, 2 AS status, k.manufacturer_id manufacturerId FROM tuser_key_word k WHERE k.is_del=1 and k.id NOT IN (SELECT l.parent_id FROM tuser_key_word_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM tuser_key_word_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id left join tuser_key_word k on l.parent_id = k.id WHERE l.status IN (1, 3) and k.is_del = 1)) t1 where 1=1 ")
        countSql.append("select count(1) as count from (SELECT id, name, examine_date examineDate, status, manufacturer_id manufacturerId FROM tuser_key_word_log WHERE parent_id IS NULL AND status IN (1, 3) UNION ALL SELECT l.parent_id id, l.name, l.examine_date examineDate, l.status, l.manufacturer_id manufacturerId FROM tuser_key_word_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM tuser_key_word_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id left join tuser_key_word k on l.parent_id = k.id WHERE l.status IN (1, 3) and k.is_del = 1 UNION ALL SELECT k.id, k.name, k.examine_date examineDate, 2 AS status, k.manufacturer_id manufacturerId FROM tuser_key_word k WHERE k.is_del=1 and k.id NOT IN (SELECT l.parent_id FROM tuser_key_word_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM tuser_key_word_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id left join tuser_key_word k on l.parent_id = k.id WHERE l.status IN (1, 3) and k.is_del = 1)) t1 where 1=1 ")
        sql.append(" and t1.manufacturerId = ?")
        countSql.append(" and t1.manufacturerId = ?")
        paramList.add(tuser.getManufacturer().id)
        countParamList.add(tuser.getManufacturer().id)
        if (params.name) {
            sql.append(" and t1.name like ?")
            countSql.append(" and t1.name like ?")
            paramList.add("%\\" + params.name + "%")
            countParamList.add("%\\" + params.name + "%")
        }
        if (params.examineDate) {
            sql.append(" and t1.examineDate between ? and ?")
            countSql.append(" and t1.examineDate between ? and ?")
            paramList.add(params.examineDate)
            paramList.add(DateUtil.getTomorrowStr(params.examineDate))
            countParamList.add(params.examineDate)
            countParamList.add(DateUtil.getTomorrowStr(params.examineDate))
        }
        if (params.status) {
            sql.append(" and t1.status = ?")
            countSql.append(" and t1.status = ?")
            paramList.add(params.status)
            countParamList.add(params.status)
        }

        sql.append(" order by t1.examineDate desc limit ?,?")
        paramList.add(params.offset)
        paramList.add(params.max)
        String newSql = sql.toString()
        String countNewSql = countSql.toString()
        List<Map<String, Object>> dataResult = SQLUtil.getResult(newSql, paramList)
        def userAssetTotal = SQLUtil.getResult(countNewSql, countParamList)?.getAt(0)?.count ?: 0

        def count1Sql = "select count(1) as count from (SELECT id, name, examine_date examineDate, status, manufacturer_id manufacturerId FROM tuser_key_word_log WHERE parent_id IS NULL AND status IN (1, 3) UNION ALL SELECT l.id, l.name, l.examine_date examineDate, l.status, l.manufacturer_id manufacturerId FROM tuser_key_word_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM tuser_key_word_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id left join tuser_key_word k on l.parent_id = k.id WHERE l.status IN (1, 3) and k.is_del = 1 UNION ALL SELECT k.id, k.name, k.examine_date examineDate, 2 AS status, k.manufacturer_id manufacturerId FROM tuser_key_word k WHERE k.is_del=1 and k.id NOT IN (SELECT l.parent_id FROM tuser_key_word_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM tuser_key_word_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id left join tuser_key_word k on l.parent_id = k.id WHERE l.status IN (1, 3) and k.is_del = 1)) t1 where 1=1 and t1.status=2 and t1.manufacturerId=?"
        def count2Sql = "select count(1) as count from (SELECT id, name, examine_date examineDate, status, manufacturer_id manufacturerId FROM tuser_key_word_log WHERE parent_id IS NULL AND status IN (1, 3) UNION ALL SELECT l.id, l.name, l.examine_date examineDate, l.status, l.manufacturer_id manufacturerId FROM tuser_key_word_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM tuser_key_word_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id left join tuser_key_word k on l.parent_id = k.id WHERE l.status IN (1, 3) and k.is_del = 1 UNION ALL SELECT k.id, k.name, k.examine_date examineDate, 2 AS status, k.manufacturer_id manufacturerId FROM tuser_key_word k WHERE k.is_del=1 and k.id NOT IN (SELECT l.parent_id FROM tuser_key_word_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM tuser_key_word_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id left join tuser_key_word k on l.parent_id = k.id WHERE l.status IN (1, 3) and k.is_del = 1)) t1 where 1=1 and t1.status=1 and t1.manufacturerId=?"
        def count3Sql = "select count(1) as count from (SELECT id, name, examine_date examineDate, status, manufacturer_id manufacturerId FROM tuser_key_word_log WHERE parent_id IS NULL AND status IN (1, 3) UNION ALL SELECT l.id, l.name, l.examine_date examineDate, l.status, l.manufacturer_id manufacturerId FROM tuser_key_word_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM tuser_key_word_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id left join tuser_key_word k on l.parent_id = k.id WHERE l.status IN (1, 3) and k.is_del = 1 UNION ALL SELECT k.id, k.name, k.examine_date examineDate, 2 AS status, k.manufacturer_id manufacturerId FROM tuser_key_word k WHERE k.is_del=1 and k.id NOT IN (SELECT l.parent_id FROM tuser_key_word_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM tuser_key_word_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id left join tuser_key_word k on l.parent_id = k.id WHERE l.status IN (1, 3) and k.is_del = 1)) t1 where 1=1 and t1.status=3 and t1.manufacturerId=?"
        //审核通过
        def count1 = SQLUtil.getResult(count1Sql, [tuser.getManufacturer().id])?.getAt(0)?.count ?: 0
        //待审核
        def count2 = SQLUtil.getResult(count2Sql, [tuser.getManufacturer().id])?.getAt(0)?.count ?: 0
        //驳回
        def count3 = SQLUtil.getResult(count3Sql, [tuser.getManufacturer().id])?.getAt(0)?.count ?: 0
        //总数
        def count = count1 + count2 + count3

        def res = [keyWordLists: dataResult, userAssetTotal: userAssetTotal, count: count, count1: count1, count2: count2, count3: count3, obj: params]
        if (params.returnType) {
            render(res as JSON)
        } else {
            [keyWordLists: dataResult, userAssetTotal: userAssetTotal, count: count, count1: count1, count2: count2, count3: count3, obj: params]
        }
    }

    /**
     * 批量删除关键字
     * 功能：批量删除用户的关键字，将关键字标记为删除状态
     * 参数：
     *   - ids: 关键字ID列表，多个ID用逗号分隔或数组形式
     */
    def delWord = {
        // 1. 检查用户是否登录
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        // 2. 获取关键字ID数组
        def keywordIds = params.id instanceof String ? params.id.split(',') : params.id

        if (!keywordIds) {
            flash.message = "请选择要删除的关键字"
            redirect(action: "keyWordList")
            return
        }

        // 3. 记录删除结果
        def successCount = 0
        def failCount = 0

        // 4. 遍历处理每个关键字
        keywordIds.each { id ->
            def keyWord = TUserKeyWord.get(id)
            // 5. 验证关键字存在且属于当前用户
            if (keyWord && keyWord.tUserId == session.user.id) {
                try {
                    // 6. 标记删除并更新
                    keyWord.setIsDel(0)
                    keyWord.setLastUpdated(new Date())
                    if (keyWord.save(flush: true)) {
                        successCount++
                    } else {
                        failCount++
                        log.error("删除关键字失败，ID: ${id}")
                    }
                } catch (Exception e) {
                    failCount++
                    log.error("删除关键字异常，ID: ${id}", e)
                }
            } else {
                failCount++
                log.warn("尝试删除不属于当前用户的关键字，ID: ${id}")
            }
        }

        // 7. 设置操作结果消息
        if (failCount == 0) {
            flash.message = "已成功删除${successCount}个关键字"
        } else if (successCount == 0) {
            flash.message = "删除关键字失败，请重试"
        } else {
            flash.message = "成功删除${successCount}个关键字，${failCount}个删除失败"
        }

        // 8. 返回关键字列表
        redirect(action: "keyWordList")
    }

    /**
     * 我的产品与服务
     */
    def examineList = {
        //666
        //RedisUtil.setString("name1","1231")
        String name = RedisUtil.getString("name1")
        println "name|" + params
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        params.max = Math.min(params.max ? params.int('max') : 10, 100)
        params.offset = params.offset ? params.int('offset') : 0

        TUser tuser = TUser.findById(session.user.id)

        StringBuilder sql = new StringBuilder() //查询sql
        StringBuilder countSql = new StringBuilder() //count sql
        List<Object> paramList = new ArrayList() //参数集合
        List<Object> countParamList = new ArrayList() //参数集合
        sql.append("select * from (SELECT null as id, name, description, edition, date_created as dateCreated, status, manufacturer_id manufacturerId FROM product_info_log WHERE parent_id IS NULL AND status IN (1, 3) UNION ALL SELECT l.parent_id id, l.name, l.description, l.edition, l.date_created as dateCreated, l.status, l.manufacturer_id manufacturerId FROM product_info_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM product_info_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id WHERE l.status IN (1, 3) UNION ALL SELECT t1.id, t2.name, t2.description, t1.edition, t1.date_created as dateCreated, 2 as status, t1.manufacturer_id FROM product_info t1 left join product_category t2 on t1.product_category_id = t2.id WHERE t1.id NOT IN (SELECT l.parent_id FROM product_info_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM product_info_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id WHERE l.status IN (1, 3))) t1 where 1 = 1 ")
        countSql.append("select count(1) count from (SELECT null as id, name, description, edition, date_created as dateCreated, status, manufacturer_id manufacturerId FROM product_info_log WHERE parent_id IS NULL AND status IN (1, 3) UNION ALL SELECT l.parent_id id, l.name, l.description, l.edition, l.date_created as dateCreated, l.status, l.manufacturer_id manufacturerId FROM product_info_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM product_info_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id WHERE l.status IN (1, 3) UNION ALL SELECT t1.id, t2.name, t2.description, t1.edition, t1.date_created as dateCreated, 2 as status, t1.manufacturer_id FROM product_info t1 left join product_category t2 on t1.product_category_id = t2.id WHERE t1.id NOT IN (SELECT l.parent_id FROM product_info_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM product_info_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id WHERE l.status IN (1, 3))) t1 where 1 = 1 ")
        sql.append(" and t1.manufacturerId = ?")
        countSql.append(" and t1.manufacturerId = ?")
        paramList.add(tuser.getManufacturer().id)
        countParamList.add(tuser.getManufacturer().id)
        if (params.name) {
            sql.append(" and t1.name like ?")
            countSql.append(" and t1.name like ?")
            paramList.add("%\\" + params.name + "%")
            countParamList.add("%\\" + params.name + "%")
        }
        if (params.dateCreated) {
            sql.append(" and t1.dateCreated between ? and ?")
            countSql.append(" and t1.dateCreated between ? and ?")
            paramList.add(params.dateCreated)
            paramList.add(DateUtil.getTomorrowStr(params.dateCreated))
            countParamList.add(params.dateCreated)
            countParamList.add(DateUtil.getTomorrowStr(params.dateCreated))
        }
        if (params.status) {
            sql.append(" and t1.status = ?")
            countSql.append(" and t1.status = ?")
            paramList.add(params.status)
            countParamList.add(params.status)
        }

        sql.append(" order by t1.dateCreated desc limit ?,?")
        paramList.add(params.offset)
        paramList.add(params.max)
        String newSql = sql.toString()
        String countNewSql = countSql.toString()
        List<Map<String, Object>> dataResult = SQLUtil.getResult(newSql, paramList)
        def countResult = SQLUtil.getResult(countNewSql, countParamList)?.getAt(0)?.count ?: 0

        def count1Sql = "select count(1) count from (SELECT null as id, name, description, edition, date_created as dateCreated, status, manufacturer_id manufacturerId FROM product_info_log WHERE parent_id IS NULL AND status IN (1, 3) UNION ALL SELECT l.parent_id id, l.name, l.description, l.edition, l.date_created as dateCreated, l.status, l.manufacturer_id manufacturerId FROM product_info_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM product_info_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id WHERE l.status IN (1, 3) UNION ALL SELECT t1.id, t2.name, t2.description, t1.edition, t1.date_created as dateCreated, 2 as status, t1.manufacturer_id FROM product_info t1 left join product_category t2 on t1.product_category_id = t2.id WHERE t1.id NOT IN (SELECT l.parent_id FROM product_info_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM product_info_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id WHERE l.status IN (1, 3))) t1 where 1 = 1 and t1.status=2 and t1.manufacturerId=?"
        def count2Sql = "select count(1) count from (SELECT null as id, name, description, edition, date_created as dateCreated, status, manufacturer_id manufacturerId FROM product_info_log WHERE parent_id IS NULL AND status IN (1, 3) UNION ALL SELECT l.parent_id id, l.name, l.description, l.edition, l.date_created as dateCreated, l.status, l.manufacturer_id manufacturerId FROM product_info_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM product_info_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id WHERE l.status IN (1, 3) UNION ALL SELECT t1.id, t2.name, t2.description, t1.edition, t1.date_created as dateCreated, 2 as status, t1.manufacturer_id FROM product_info t1 left join product_category t2 on t1.product_category_id = t2.id WHERE t1.id NOT IN (SELECT l.parent_id FROM product_info_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM product_info_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id WHERE l.status IN (1, 3))) t1 where 1 = 1 and t1.status=1 and t1.manufacturerId=?"
        def count3Sql = "select count(1) count from (SELECT null as id, name, description, edition, date_created as dateCreated, status, manufacturer_id manufacturerId FROM product_info_log WHERE parent_id IS NULL AND status IN (1, 3) UNION ALL SELECT l.parent_id id, l.name, l.description, l.edition, l.date_created as dateCreated, l.status, l.manufacturer_id manufacturerId FROM product_info_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM product_info_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id WHERE l.status IN (1, 3) UNION ALL SELECT t1.id, t2.name, t2.description, t1.edition, t1.date_created as dateCreated, 2 as status, t1.manufacturer_id FROM product_info t1 left join product_category t2 on t1.product_category_id = t2.id WHERE t1.id NOT IN (SELECT l.parent_id FROM product_info_log l INNER JOIN (SELECT parent_id, MAX(id) AS max_id FROM product_info_log WHERE parent_id IS NOT NULL GROUP BY parent_id) max_ids ON l.id = max_ids.max_id WHERE l.status IN (1, 3))) t1 where 1 = 1 and t1.status=3 and t1.manufacturerId=?"
        //审核通过
        def count1 = SQLUtil.getResult(count1Sql, [tuser.getManufacturer().id])?.getAt(0)?.count ?: 0
        //待审核
        def count2 = SQLUtil.getResult(count2Sql, [tuser.getManufacturer().id])?.getAt(0)?.count ?: 0
        //驳回
        def count3 = SQLUtil.getResult(count3Sql, [tuser.getManufacturer().id])?.getAt(0)?.count ?: 0
        //总数
        def count = count1 + count2 + count3
        def res = [examineList: dataResult, userAssetTotal: countResult, count: count, count1: count1, count2: count2, count3: count3, obj: params]
        if (params.returnType) {
            render(res as JSON)
        } else {
            [examineList: dataResult, userAssetTotal: countResult, count: count, count1: count1, count2: count2, count3: count3, obj: params]
        }

    }


    /**
     * 我的产品与服务记录
     */
    def examineListHis = {
        //666
        //RedisUtil.setString("name1","1231")
        String name = RedisUtil.getString("name1")
        println "name|" + name
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        params.max = Math.min(params.max ? params.int('max') : 10, 100)
        params.offset = params.offset ? params.int('offset') : 0

        TUser tuser = TUser.findById(session.user.id)

        def count = ProductInfoLog.countByManufacturer(tuser.getManufacturer())
        def userAssetList = ProductInfoLog.findAllByManufacturer(tuser.getManufacturer())

        [examineList: userAssetList, userAssetTotal: count]
    }

    /**
     * 批量删除产品
     */
    def delProduct = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        TUser tUser = TUser.findById(session.user.id);
        // 获取要删除的产品ID数组
        def productIds = params.id instanceof String ? params.id.split(',') : params.id

        if (!productIds) {
            Map<Object, Object> map = new HashMap<Object, Object>();
            map.put("code", "-1");
            map.put("msg", "未选择要删除的产品");
            render JSONArray.toJSONString(map);
            return
        }

        def successIds = []
        def failIds = []

        productIds.each { id ->
            def product = ProductInfo.get(id)
            if (product && product.manufacturer.id == tUser.manufacturer.id) {
                // 检查产品是否关联到漏洞
                String sql = "SELECT count(*) count FROM flaw f left join flaw_product p on p.flaw_id=f.id left join product_info i on i.id=p.product_id " +
                        "where f.enable=1 and i.id=?";
                def ret = SQLUtil.getResult(sql, [product.id]);
                System.out.println("前台删除产品信息|sql=" + sql + "|productId=" + product.id + "|ret=" + ret);
                if (ret != null && ret.size() > 0) {
                    def total = Long.valueOf(ret.get(0).get("count").toString());
                    System.out.println("前台删除产品信息|total=" + total);
                    if (total == 0) {
                        System.out.println("前台删除产品信息|删除|productId=" + product.id);
                        product.delete();
                        successIds << product.id
                    } else {
                        failIds << product.id
                    }
                }
            }
        }

        Map<Object, Object> map = new HashMap<Object, Object>();
        if (failIds.isEmpty()) {
            map.put("code", "0");
            map.put("msg", "所有选中的产品已成功删除");
        } else if (successIds.isEmpty()) {
            map.put("code", "1");
            map.put("msg", "选中的产品都关联了漏洞，无法删除");
        } else {
            map.put("code", "2");
            map.put("msg", "部分产品删除成功");
            map.put("successIds", successIds);
            map.put("failIds", failIds);
        }

        render JSONArray.toJSONString(map);
    }


    /**
     * 查询产品是否重复
     */
    def getProduct = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        System.out.println(params.nameedition);
        Manufacturer manufacturer = TUser.findById(Long.valueOf(session.user.id)).getManufacturer()
        //把单位名称、产品名称、产品版本拼接起来，用于查询是否有重复的
        def productName = manufacturer.name + " " + params.nameedition;
        System.out.println("productName===" + productName);
        //根据此单位查询添加的产品是否有数据，如果有重复的就不能添加。
        String sql = "select count(*) count from product_info where name=?";
        def ret = SQLUtil.getResult(sql, [productName]);
        System.out.println("判断产品信息|sql=" + sql + "|productName=" + productName + "|ret=" + ret);
        if (ret != null && ret.size() > 0) {
            def total = Long.valueOf(ret.get(0).get("count").toString());
            System.out.println("判断产品信息|total=" + total);
            Map<Object, Object> map = new HashMap<Object, Object>();
            if (total == 0) {
                map.put("code", "0");
                render JSONArray.toJSONString(map);
                return
            } else {
                map.put("code", "1");
                render JSONArray.toJSONString(map);
                return
            }
        }
    }

    /**
     * 编辑产品时判断产品是否有关联漏洞，如果有关联不能编辑
     */
    def isUpateProduct = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        //查询产品有没有关联到漏洞
        String sql = "SELECT count(*) count FROM flaw f left join flaw_product p on p.flaw_id=f.id left join product_info i on i.id=p.product_id " +
                "where f.enable=1 and i.id=?";
        def ret = SQLUtil.getResult(sql, [params.id]);
        System.out.println("产品信息是否关联漏洞|sql=" + sql + "|productId=" + params.id + "|ret=" + ret);
        if (ret != null && ret.size() > 0) {
            def total = Long.valueOf(ret.get(0).get("count").toString());
            System.out.println("判断产品信息|total=" + total);
            Map<Object, Object> map = new HashMap<Object, Object>();
            if (total == 0) {
                map.put("code", "0");
                render JSONArray.toJSONString(map);
                return
            } else {
                map.put("code", "1");
                render JSONArray.toJSONString(map);
                return
            }
        }
    }


    /**
     * 漏洞记录
     */
    def examineFlawHis = {
        //666
        //RedisUtil.setString("name1","1231")
        String name = RedisUtil.getString("name1")
        println "name|" + name
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        params.max = Math.min(params.max ? params.int('max') : 10, 100)
        params.offset = params.offset ? params.int('offset') : 0


        def count = FlawWebUseLog.countByFlawId(params.id)
        def userAssetList = FlawWebUseLog.findAllByFlawId(params.id, [sort: "dateCreated", order: "desc", max: params.max, offset: params.offset])

        for (FlawWebUseLog flawWebUseLog : userAssetList) {
            def sql1 = "select product_info_id from flaw_web_use_log where id=" + flawWebUseLog.getId()
            def res1 = new groovy.sql.Sql(dataSource).rows(sql1)
            def productInfoId = "";
            for (int i = 0; i < res1.size(); i++) {
                productInfoId = res1.get(i).get("product_info_id");
            }
            StringBuffer productName = new StringBuffer()
            if (!"".equals(productInfoId)) {
                def sql2 = "select name from product_info where id in (" + productInfoId + ")";
                def res2 = new groovy.sql.Sql(dataSource).rows(sql2)
                for (int i = 0; i < res2.size(); i++) {
                    if (i == 0) {
                        productName.append(res2.get(i).get("name"));
                    } else {
                        productName.append(";" + res2.get(i).get("name"));
                    }
                }
            }
            flawWebUseLog.setProductName(productName.toString());
        }

        [examineList: userAssetList, userAssetTotal: count]
    }


    /**}* 我的产品的服务编辑
     */
    def editProduct = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        return [name: params.name, id: params.id, description: params.description, edition: params.edition]

    }


    /**}* 漏洞编辑
     */
    def editLoophole = {
        session.setAttribute("offset", session.getAttribute("offset"))
        println "1111=" + session.getAttribute("offset")
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        TUser tuser = TUser.findById(session.user.id)
        String manufacturerId = tuser.getManufacturer().id

        Flaw flaw = Flaw.findById(params.id);
        DetailedInfo detailedInfo = DetailedInfo.findById(flaw.detailedInfo.id)

        //判断是否在已有列表中
        def paramList = new ArrayList()
        paramList.add(manufacturerId)
        paramList.add(params.id)
        def sqlList = "SELECT  b.*,c.`value`,c.`name` as names  from  tuser_flaw a LEFT JOIN flaw b  on  a.flaw_id=b.id LEFT JOIN  dictionary_info c  on   b.serverity_id=c.id where a.manufacturer_id =? And b.id = ?  order by b.storage_time;"
        def res = new groovy.sql.Sql(dataSource).rows(sqlList, paramList)
        print("editLoophole resSize:[" + res.size() + "]")

        if (res == null || res.size() == 0) {
            return [flaw: null, productName: productName, productIds: productIds, names: params.names, description: null]
        }

        def sql1 = "SELECT b.name,b.id from flaw_product a LEFT JOIN product_info b on a.product_id=b.id  where  a.flaw_id=" + flaw.getId();
        def res1 = new groovy.sql.Sql(dataSource).rows(sql1)
        StringBuffer productName = new StringBuffer()
        StringBuffer productIds = new StringBuffer()
        for (int j = 0; j < res1.size(); j++) {
            if (j == 0) {
                productName.append(res1.get(j).get("name"));
                productIds.append(res1.get(j).get("id"));
            } else {
                productName.append(";" + res1.get(j).get("name"));
                productIds.append("," + res1.get(j).get("id"));
            }
        }
        def attId = flaw?.attachment?.id

        if (attId) {
            def attachmentPdfInstance = AttachmentPdf.findAllByAttachmentId(flaw?.attachment?.id)
            return [flaw: flaw, attPdfList: attachmentPdfInstance, productName: productName, productIds: productIds, names: params.names, description: detailedInfo.description, area: params.area, canEdit: params.canEdit]
        } else {
            return [flaw: flaw, productName: productName, productIds: productIds, names: params.names, description: detailedInfo.description, curParams: params, area: params.area, canEdit: params.canEdit]
        }
    }


    def views = {
        String productId = params.edition
        String sql = "select * from product_info where id in (" + productId + ")";
        def res = new groovy.sql.Sql(dataSource).rows(sql)
        StringBuffer productName = new StringBuffer()
        for (int i = 0; i < res.size(); i++) {
            if (i == 0) {
                productName.append(res.get(i).get("name"));
            } else {
                productName.append(";" + res.get(i).get("name"));
            }
        }
        Map<Object, Object> map = new HashMap<Object, Object>();
        map.put("productName", productName);
        render JSONArray.toJSONString(map);
        return

    }

    def publicKey = {
        Map map = RSAUtils.getModulus();
        map.put("aesKey", AESCrypt.getKey());
        session.setAttribute("modu", map.get("modu"))
        session.setAttribute("m", map.get("m"))
        map.remove("m")
        render JSONArray.toJSONString(map);
        return

    }

    //查询关联产品
    def viewProduct = {
        params.numPerPage = Math.min(params.numPerPage ? params.int('numPerPage') : 20, 100)
        params.pageNum = params.pageNum ? params.int('pageNum') : 1
        def offset = (params.int('pageNum') - 1) * params.int('numPerPage')

        def hql = "from ProductInfo p where 1=1 and p.id in(" + params.productIds + ")";
        def hqlCount = "select count(p.id) from ProductInfo p where 1=1 and p.id in(" + params.productIds + ")";
        def productInfoInstanceList = ProductInfo.executeQuery(hql, [max: params.numPerPage, offset: offset])
        def productInfoInstanceTotal = ProductInfo.executeQuery(hqlCount)

        render(view: 'viewProduct', model: [productInfoInstanceList: productInfoInstanceList, productInfoInstanceTotal: productInfoInstanceTotal[0]])

    }

    /**
     * 我的产品的服务添加
     */
    def saveProductView = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
    }

    def returnUserFlawList = {

        redirect(action: 'userFlawList', params: [offset_new: session.getAttribute("offset")])
    }

    /**
     * 我的产品的服务添加update
     */
    def editLoopholeUpdate = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        def file = request.getFile("patchFile")
        def attachment = null
        if (file != null && !"".equals(file.getOriginalFilename())) {
            //附件符合格式及大小限制
            String filePath = "${grailsApplication.config.filePath.exploitAttFilePath}" //文件的路径
            String realName = CommentsUtil.getCurrentTime() //文件的真实文件名
            attachment = attachmentService.uploadFile(file, filePath, realName)
        }

        if (params.id) {
            FlawWebUseLog flawWebLog = FlawWebUseLog.findByFlawIdAndStatus(params.id, 1)
            if (flawWebLog == null) {
                flawWebLog = new FlawWebUseLog()
                flawWebLog.title = params.title
                flawWebLog.status = 1
                flawWebLog.number = params.number
                flawWebLog.content = params.conttent
                flawWebLog.referenceLink = params.referenceLink
                flawWebLog.description = params.description
                flawWebLog.dateCreated = new Date()
                flawWebLog.tUserId = session.user.id
                flawWebLog.flawId = java.lang.Long.valueOf(params.id)
                flawWebLog.formalWay = params.formalWay
                flawWebLog.patchName = params.patchName
                flawWebLog.patchDescription = params.patchDescription
                flawWebLog.function = params.function
                flawWebLog.patchUrl = params.patchUrl
                flawWebLog.productInfoId = params.productIds
                if (params.serverityId != null) {
                    flawWebLog.serverityId = Integer.valueOf(params.serverityId)
                } else {
                    flawWebLog.serverityId = 0;
                }

                if (attachment != null) {
                    flawWebLog.attachment = attachment
                }
                if (flawWebLog.save(flush: true)) {
                    redirect(action: 'userFlawList', params: [offset_new: session.getAttribute("offset")])
                } else {
                    flawWebLog.errors.allErrors.each {
                        println "flawWebLog|" + it
                    }
                }

            } else {
                flawWebLog.title = params.title
                flawWebLog.status = 1
                flawWebLog.number = params.number
                flawWebLog.content = params.conttent
                flawWebLog.referenceLink = params.referenceLink
                flawWebLog.description = params.description
                flawWebLog.lastUpdated = new Date()
                flawWebLog.tUserId = session.user.id
                flawWebLog.formalWay = params.formalWay
                flawWebLog.patchName = params.patchName
                flawWebLog.patchDescription = params.patchDescription
                flawWebLog.function = params.function
                flawWebLog.patchUrl = params.patchUrl
                flawWebLog.productInfoId = params.productIds
                if (params.serverityId != null) {
                    flawWebLog.serverityId = Integer.valueOf(params.serverityId)
                } else {
                    flawWebLog.serverityId = 0;
                }
                flawWebLog.flawId = java.lang.Long.valueOf(params.id)
                if (attachment != null) {
                    flawWebLog.attachment = attachment
                }
                if (flawWebLog.save(flush: true)) {
                    redirect(action: 'userFlawList', params: [offset_new: session.getAttribute("offset")])
                } else {
                    flawWebLog.errors.allErrors.each {
                        println "flawWebLog|" + it
                    }
                }
            }
        }

    }


    def saveProduct = {


        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }


        def description = params.description
        Manufacturer manufacturer = TUser.findById(Long.valueOf(session.user.id)).getManufacturer()

        def productInfoInstance = new ProductInfoLog()
        if (params.edition != null) {
            productInfoInstance.edition = params.edition
        }
        productInfoInstance.dateCreated = new Date()
        productInfoInstance.lastUpdated = new Date()
        productInfoInstance.productCategoryName = params.name
        productInfoInstance.manufacturer = manufacturer
        productInfoInstance.status = 1
        productInfoInstance.description = description
        /*    productInfoInstance.name=manufacturer.name+" "+params.name+" "+params.edition*/
        productInfoInstance.name = params.name
        productInfoInstance.categoryName = params.name
        productInfoInstance.tUserId = Long.valueOf(session.user.id)
        productInfoInstance.isSync = 0;

        if (productInfoInstance.save(flush: true)) {
            //保存成功
            flash.message = "Saved successfully"
            redirect(action: "examineList")
            return
        } else {
            productInfoInstance.errors.allErrors.each {
                println "productInfoInstance|" + it
            }
            println("我的产品与服务保存失败了")
            return
        }

    }


    def editProductUpdate = {

        def description = params.description
        Manufacturer manufacturer = TUser.findById(Long.valueOf(session.user.id)).getManufacturer()


        def productInfoInstance = ProductInfoLog.findByCategoryNameAndStatus(params.name, 1);


        if (productInfoInstance == null) {
            productInfoInstance = new ProductInfoLog()
            productInfoInstance.dateCreated = new Date()
        }

        productInfoInstance.edition = params.edition
        productInfoInstance.lastUpdated = new Date()
        productInfoInstance.productCategoryName = params.name
        productInfoInstance.manufacturer = manufacturer
        productInfoInstance.status = 1
        productInfoInstance.description = description
        productInfoInstance.name = params.name
        productInfoInstance.categoryName = params.name
        productInfoInstance.tUserId = Long.valueOf(session.user.id)
        productInfoInstance.parentId = Long.valueOf(params.id)
        productInfoInstance.isSync = 0;
        println(params.id)

        if (productInfoInstance.save(flush: true)) {
            //编辑成功
            flash.message = "Edited successfully"
            redirect(action: "examineList")
            return
        } else {
            println("我的产品与服务编辑失败了")
            return
        }

    }


/**
 * 添加关键字
 */
    def editKeyWord = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        println(params.name);
        return [id: params.id, name: params.name]

    }


/**
 * 添加关键字
 */
    def addKeyWord = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

    }

/**
 * 保存关键字
 */
    def saveKeyWord = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        TUser tuser = TUser.findById(session.user.id)
        println "params.description=" + params.description

        if (params.description) {

            def tUserKeyWordLog = new TUserKeyWordLog()
            tUserKeyWordLog.name = params.description
            tUserKeyWordLog.dateCreated = new Date()
            tUserKeyWordLog.tUserId = session.user.id
            tUserKeyWordLog.manufacturerId = tuser.getManufacturer().id
            if (tUserKeyWordLog.save(flush: true)) {
                redirect(action: 'keyWordList')
            } else {
                println("添加失败了")
            }

        }

    }


/**
 * 添加我的资产
 */
    def addMyAsset = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
    }

/**
 * 保存我的资产
 */
    def saveMyAsset = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        println "params.manuName=" + params.manuName
        println "params.productCategoryName=" + params.productCategoryName
        println "params.edition=" + params.edition

        if (params.manuName && params.productCategoryName && params.edition) {
            def editionArr = params.edition.split(",")
            for (def edition : editionArr) {
                def assetName = params.manuName.trim() + " " + params.productCategoryName.trim() + " " + edition.trim()
                def assetInstance = Asset.findByName(assetName)
                println "assetInstance=" + assetInstance
                // 添加判断，判断用户是否已有此资产,如果有资产，把有的资产加到flash.message中
                if (assetInstance) {
                    def userAssetInstance = UserAsset.findByUserAndAsset(session.user, assetInstance)
                    if (!userAssetInstance) {
                        def newUserAsset = new UserAsset()
                        newUserAsset.asset = assetInstance
                        newUserAsset.user = session.user
                        newUserAsset.save(flush: true)
                    } else {
                        continue
                    }
                } else {
                    def newAsset = new Asset()
                    // TODO 将原来的HTML进行转码 防止XSS
                    newAsset.manufacturerName = HtmlUtils.htmlEscape(params.manuName)
                    newAsset.productCategoryName = HtmlUtils.htmlEscape(params.productCategoryName)
                    newAsset.edition = HtmlUtils.htmlEscape(edition)
                    newAsset.name = assetName
                    newAsset.flag = 1
                    newAsset.description = params.description
                    if (newAsset.save(flush: true)) {
                        def newUserAsset = new UserAsset()
                        newUserAsset.asset = newAsset
                        newUserAsset.user = session.user
                        newUserAsset.save(flush: true)
                    }
                }
            }
        }
        redirect(action: 'myasset')
    }


    def assetFlawList = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        params.max = Math.min(params.max ? params.int('max') : 10, 100)
        params.offset = params.offset ? params.int('offset') : 0
        StringBuffer sqlBuff = new StringBuffer()
        sqlBuff.append("select distinct f.id,f.number,f.title,f.status,f.date_created,f.comment_count,f.concern_count from flaw_product fp,")
                .append("flaw f,(select product_id from asset ast,user_asset ua where ua.user_id = ? and ast.id = ua.asset_id) temp ")
                .append("where fp.product_id = temp.product_id and f.id = fp.flaw_id ")
                .append("and f.status=9 and f.enable=1 and f.open_time<=? and f.is_open=1 and f.parent_flaw_id is null ")
                .append("order by f.date_created desc limit ?,?")
        StringBuffer sqlBuffCount = new StringBuffer()
        sqlBuffCount.append("select count(distinct f.id) as con from flaw_product fp,")
                .append("flaw f,(select product_id from asset ast,user_asset ua where ua.user_id = ? and ast.id = ua.asset_id) temp ")
                .append("where fp.product_id = temp.product_id and f.id = fp.flaw_id ")
                .append("and f.status=9 and f.enable=1 and f.open_time<=? and f.is_open=1 and f.parent_flaw_id is null")
        def flawInstanceList = SQLUtil.getResult(sqlBuff.toString(), [
                session.user.id,
                new Date(),
                params.offset,
                params.max
        ]);
        def flawInstanceTotal = SQLUtil.getResult(sqlBuffCount.toString(), [session.user.id, new Date()]).con[0];
        [flawInstanceList: flawInstanceList, flawInstanceTotal: flawInstanceTotal]
    }

/**
 * TODO 先判断用户是否有证书，如果有证书，则可以导出关联的漏洞，
 * 如果没有证书，则不允许用户导出漏洞
 * 根据用户的证书角色获取用户所能获得的漏洞字段权限
 * 然后导出excel表格
 */
    def exportAssetRelationFlaw = {
        if (!session.user) {
            session.originalRequestParams = [controller: "TUser", action: "assetFlawList"]
            redirect(controller: "TUser", action: "login")
            return
        }

        X509Certificate[] certs = (X509Certificate[]) request.getAttribute("javax.servlet.request.X509Certificate");
        if (certs != null && WebServiceUtil.verifyCertificate(certs[0])) {
            println "certs=" + certs
            def CN = WebServiceUtil.getSubjectName("CN", certs[0])
            println "CN=" + CN
            def caInstance = CA.findByCnStr(CN)
            if (caInstance && caInstance.caRole && caInstance.tuser.id == session.user.id) {
                def fieldStr = caInstance.caRole.field
                //def fieldStr = "number#title#description#formalWay#referenceLink#manufacturer#foundTime#cveStr#cause#thread#serverity#position#softStyle#patchName#patchDescription#patchUrl#metric#baseMetric#reflectProduct#"
                //导出资产关联的漏洞
                StringBuffer sqlBuff = new StringBuffer()
                sqlBuff.append("select distinct f.id from flaw_product fp,")
                        .append("flaw f,(select product_id from asset ast,user_asset ua where ua.user_id = ? and ast.id = ua.asset_id) temp ")
                        .append("where fp.product_id = temp.product_id and f.id = fp.flaw_id ")
                        .append("and f.status=9 and f.enable=1 and f.open_time<=? and f.is_open=1 and f.parent_flaw_id is null ")
                def flawIdRes = SQLUtil.getResult(sqlBuff.toString(), [session.user.id, new Date()]);
                println "flawIdRes=" + flawIdRes
                def res = new ArrayList()
                if (flawIdRes) {
                    res = AssetFlawUtil.getRelationFlawInfo(flawIdRes, fieldStr)
                }

                //写入到excel文件中
                def file = AssetFlawUtil.exportAssetRelationFlawFile(fieldStr, res)
                println "file=" + file
                response.setHeader('Content-disposition', 'attachment;filename=relationFlawList.xls')
                response.setHeader('Content-length', "${file.size()}")
                OutputStream out = new BufferedOutputStream(response.outputStream)
                try {
                    out.write(file.bytes)
                } finally {
                    out.close()
                    return false
                }


                /*println "res="+res
                 def filePath="${grailsApplication.config.filePath.assetRelationFlawFilePath}/relationFlawList.csv";
                 def out = new File(filePath)
                 if(out.exists()){
                 out.delete();
                 }
                 if (!out.getParentFile().exists()) {
                 out.getParentFile().mkdirs();
                 }
                 if(!out.exists()){
                 out.createNewFile()
                 }
                 Writer outTxt = new OutputStreamWriter(new FileOutputStream(out,true), "GBK");
                 def fieldArr = fieldStr.split("#")
                 def titleRow = new ArrayList()
                 for(def field : fieldArr){
                 titleRow.add(FieldConstants.fieldMap[field])
                 }
                 outTxt.write(titleRow.join(','));
                 outTxt.write("\r")
                 res.each {eachRes ->
                 def row = new ArrayList()
                 for(def field : fieldArr){
                 println "eachRes.getAt("+field+")="+eachRes.getAt(field)
                 row.add(eachRes.getAt(field)?eachRes.getAt(field).trim():'')
                 }
                 println "row.size()="+row.size()
                 outTxt.write((row.join(',')));
                 outTxt.write("\r")
                 }
                 outTxt.close()
                 attachmentService.downloadFile(filePath,"relationFlawList.csv",request,response)*/
            } else {
                //证书错误
                flash.message = "Certificate error"
                redirect(action: "assetFlawList")
                return
            }
        } else {
            //证书错误
            flash.message = "Certificate error"
            redirect(action: "assetFlawList")
            return
        }
    }

    def deleteAsset = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        def userAssetInstance = UserAsset.get(params.id)
        if (userAssetInstance && userAssetInstance.user.id == session.user.id) {
            userAssetInstance.delete(flush: true)
            redirect(action: "myasset")
        } else {
            render(view: "error")
            return
        }
    }

    def mycc = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0

        def ccTaskList = Task.executeQuery("from CarbonCopyTask as b where b.tuser=:tuser order by id desc", [tuser: session.user], [max: params.max, offset: params.offset])
        def ccTaskInstanceTotal = Task.executeQuery("select count(*) from CarbonCopyTask as b where b.tuser=:tuser", [tuser: session.user])[0]
        [ccTaskList: ccTaskList, ccTaskInstanceTotal: ccTaskInstanceTotal]
    }

    def imageIndex = {
        render(view: "imageIndex")
    }

    def image = {
        System.out.println("进入图片上传页面");
        Map<Object, Object> map = new HashMap<Object, Object>();
        try {
            map = new ImgCutTest().getBase();
            String uuid = UUID.randomUUID().toString();
            //前台图片展示为原图的一半
            int CJX = (int) map.get("CJX") / 2;
            session.setAttribute(uuid, CJX);
            map.put("uuid", uuid);
        } catch (Exception e) {
            e.printStackTrace();
        }
        render JSONArray.toJSONString(map);
        return
    }

    def yanZhenX = {
        System.out.println("进行验证");
        Map<Object, Object> map = new HashMap<Object, Object>();
        try {
            int CJX = (int) session.getAttribute(params.uuid);
            System.out.println("uuid-->" + params.uuid);
            System.out.println("滑动x距离-->" + params.moveEnd_X);
            System.out.println("裁剪距离-->" + CJX);
            if (params.int("moveEnd_X") > CJX - 3 && params.int("moveEnd_X") < CJX + 3) {
                //偏差在3之类
                System.out.println("拼接成功");
                map.put("YZ", "yes");
            } else {
                System.out.println("偏差过大");
                map.put("YZ", "no");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        render JSONArray.toJSONString(map);
        return
    }

    def serverityName = {
        def serverity = params.serverityId
        DictionaryInfo severityDi = DictionaryInfo.findById(serverity);
        println severityDi.name
        if (severityDi != null) {
            render severityDi.name + "危"
        }
    }

    //添加产品
    def addProduct = {
        println "flawId=" + params.int('flawId')
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        def flawInstance = Flaw.get(params.int('flawId'))
        if (!flawInstance) {
            //操作失败
            render ResultUtil.failResult("Operation failed")
        }
        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0

        def hql = "from ProductInfo p where 1=1 "
        def hqlCount = "select count(p.id) from ProductInfo p where 1=1 "
        def hqlPara = new HashMap()
        if (params.manuName) {
            def manufacturers = Manufacturer.findAllByNameLike("%" + params.manuName.trim() + "%")
            //println "manufacturers="+manufacturers
            manufacturers.add(Manufacturer.get(-1))
            hql += " and p.manufacturer in (:manufacturer) "
            hqlCount += " and p.manufacturer in (:manufacturer) "
            hqlPara.put("manufacturer", manufacturers)
        }
        if (params.productCategoryName) {
            def productCategorys = ProductCategory.findAllByNameLike("%" + params.productCategoryName.trim() + "%")
            productCategorys.add(ProductCategory.get(-1))
            hql += "and p.productCategory in (:productCategory) "
            hqlCount += " and p.productCategory in (:productCategory) "
            hqlPara.put("productCategory", productCategorys)
        }
        if (params."productInfo.edition") {
//			hql+="and p.edition like '%"+params."productInfo.edition".trim()+"%' "
//			hqlCount+=" and p.edition like '%"+params."productInfo.edition".trim()+"%' "
            //2017-7-5修改为多个版本查询  版本直接用中文分号（；）隔开，因为数据库中含有英文分号
            String editionStr = params."productInfo.edition".trim()
            String[] editionArr = editionStr.split("；");
            hql += "and p.edition in (:elist) "
            hqlCount += " and p.edition in (:elist) "
            hqlPara.put("elist", editionArr)
        }
        hql += " and not exists(select fp.id from FlawProduct fp where fp.flaw=:flawInstance and fp.product=p)"
        hqlCount += " and not exists(select fp.id from FlawProduct fp where fp.flaw=:flawInstance and fp.product=p)"
        hqlPara.put("flawInstance", flawInstance)
        def productInfoInstanceList = ProductInfo.executeQuery(hql, hqlPara, [max: params.max, offset: params.offset])
        def productInfoInstanceTotal = ProductInfo.executeQuery(hqlCount, hqlPara)

        render(view: 'addProduct', model: [flawInstance: flawInstance, productInfoInstanceList: productInfoInstanceList, productInfoInstanceTotal: productInfoInstanceTotal[0]])

    }
    //查找产品
    def findProduct = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        params.max = params.max ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0

        def hql = "from ProductInfo p where 1=1 "
        def hqlCount = "select count(p.id) from ProductInfo p where 1=1 "
        def hqlPara = new HashMap()
        if (params.manuName) {
            def manufacturers = Manufacturer.findAllByNameLike("%" + params.manuName.trim() + "%")
            //println "manufacturers="+manufacturers
            manufacturers.add(Manufacturer.get(-1))
            hql += " and p.manufacturer in (:manufacturer) "
            hqlCount += " and p.manufacturer in (:manufacturer) "
            hqlPara.put("manufacturer", manufacturers)
        }
        if (params.productCategoryName) {
            def productCategorys = ProductCategory.findAllByNameLike("%" + params.productCategoryName.trim() + "%")
            productCategorys.add(ProductCategory.get(-1))
            hql += "and p.productCategory in (:productCategory) "
            hqlCount += " and p.productCategory in (:productCategory) "
            hqlPara.put("productCategory", productCategorys)
        }
        if (params."productInfo.edition") {
//			hql+="and p.edition like '%"+params."productInfo.edition".trim()+"%' "
//			hqlCount+=" and p.edition like '%"+params."productInfo.edition".trim()+"%' "
            //2017-7-5修改为多个版本查询  版本直接用中文分号（；）隔开，因为数据库中含有英文分号
            String editionStr = params."productInfo.edition".trim()
            String[] editionArr = editionStr.split("；");
            hql += "and p.edition in (:elist) "
            hqlCount += " and p.edition in (:elist) "
            hqlPara.put("elist", editionArr)
        }
        def productInfoInstanceList = ProductInfo.executeQuery(hql, hqlPara, [max: params.max, offset: params.offset])
        def productInfoInstanceTotal = ProductInfo.executeQuery(hqlCount, hqlPara)

        render(view: 'findProduct', model: [productInfoInstanceList: productInfoInstanceList, productInfoInstanceTotal: productInfoInstanceTotal[0]])

    }

    //添加影响产品的条件
    def productCondition = {

        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        String str = params.productIds
        //以用户名唯一性做为key值，临时存储查询条件 productIds
        String key = session.user.email + "_productIds"
        session.setAttribute(key, str)
        //操作成功
        render ResultUtil.failResult("Operation succeeded")
    }
    //关联产品
    def joinProduct = {

        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        session.setAttribute("list_pageNum_new", session.getAttribute("list_pageNum"))
        def flawInstance = Flaw.get(params.int('flawId'))
        def kkk = params.flawId
        session.setAttribute("firstExamineListUpdate_flag", 1)
        if (!flawInstance) {
            //操作失败
            render ResultUtil.failResult("Operation failed")
        }
        String str2 = ""
        def str3 = "";
        net.sf.json.JSONObject jsonImp = new net.sf.json.JSONObject()
        jsonImp.put("time", DateUtil.get4yMdHms(new Date()))
        def array = new net.sf.json.JSONArray()
        String str = params.productIds
        //后端处理字符串
        def productIds = str.split(",")
        productIds.each {
            def productInstance = ProductInfo.get(it)


            def flawProductSynchro = new FlawProductSynchro()
            flawProductSynchro.flawId = kkk
            flawProductSynchro.flawProductId = productInstance
            flawProductSynchro.dateCreated = new Date()
            flawProductSynchro.synchroId = 0
            flawProductSynchro.save(flush: true)
//              flawProductSynchro.dateCreated=new Date()
//              flawProductSynchro.synchroId=0
//              flawProductSynchro.status=0
//              flawProductSynchro.flawId=flawInstance.id
//              flawProductSynchro.flawProductId=flawProduct.id

            if (flawInstance.push == 1) {
                //flawProductSynchro.save()

                net.sf.json.JSONObject jsonTemp1 = new net.sf.json.JSONObject()
                net.sf.json.JSONObject json1 = toJsonObjectService.toJsonObject(flawProduct)
                jsonTemp1.put("operate", 0)
                jsonTemp1.put("isPush", 1)
                jsonTemp1.put("isLoophole", 0)
                jsonTemp1.put("flawId", flawInstance.getId())
                jsonTemp1.put("entity", json1.toString())
                array.add(jsonTemp1)
            }

            //根据产品查询行业实现漏洞与行业的关联
            //单位
            def manufacturer = productInstance?.manufacturer
            def productCategory = productInstance?.productCategory
            def editionStr = productInstance?.edition
            def hql = " from CorporationProduct cp where manufacturer = ? and productCategory is null and edition is null " +
                    " or manufacturer=? and productCategory = ? and edition is null" +
                    " or manufacturer = ? and productCategory = ? and edition = ?"
            def cpList = CorporationProduct.executeQuery(hql, [
                    manufacturer,
                    manufacturer,
                    productCategory,
                    manufacturer,
                    productCategory,
                    editionStr
            ])
            //println "cpList="+cpList
            cpList.each {
                //def inf = IndustryFlaw.findAllByIndustryAndFlaw(it.corporation?.industry,flawInstance)
                def inf = IndustryFlaw.executeQuery(" from IndustryFlaw where industry=? and flaw=? and type=0", [
                        it.corporation?.industry,
                        flawInstance
                ])
                if (!inf) {
                    def industryFlaw = new IndustryFlaw()
                    industryFlaw.flaw = flawInstance
                    industryFlaw.creater = session.user
                    industryFlaw.type = 0
                    industryFlaw.industry = it.corporation?.industry
                    industryFlaw.save(flush: true)
                    if (industryFlaw.save(flush: true)) {

                        net.sf.json.JSONObject jsonTemp1 = new net.sf.json.JSONObject()
                        net.sf.json.JSONObject json1 = toJsonObjectService.toJsonObject(flawProduct)
                        jsonTemp1.put("operate", 0)
                        jsonTemp1.put("isPush", 1)
                        jsonTemp1.put("isLoophole", 0)
                        jsonTemp1.put("flawId", flawInstance.getId())

                        jsonTemp1.put("entity", json1.toString())
                        array.add(jsonTemp1)

                    }
                }
                def flawIndustryProduct = new FlawIndustryProduct()
                flawIndustryProduct.flaw = flawInstance
                flawIndustryProduct.industry = it.corporation?.industry
                flawIndustryProduct.corporationProduct = it
                flawIndustryProduct.creator = session.user
                flawIndustryProduct.type = 1

                if (flawIndustryProduct.save(flush: true)) {

                    net.sf.json.JSONObject jsonTemp1 = new net.sf.json.JSONObject()
                    net.sf.json.JSONObject json1 = toJsonObjectService.toJsonObject(flawIndustryProduct)
                    jsonTemp1.put("operate", 0)
                    jsonTemp1.put("isPush", 1)
                    jsonTemp1.put("isLoophole", 0)
                    jsonTemp1.put("flawId", flawInstance.getId())

                    jsonTemp1.put("entity", json1.toString())
                    array.add(jsonTemp1)

                }

            }
        }
        //操作成功
        render ResultUtil.failResult("Operation succeeded")
    }

    def editKeyWordUpdate = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }

        println "params.description=" + params.description
        TUser tuser = TUser.findById(session.user.id)
        if (params.description) {

            def tUserKeyWordLog = new TUserKeyWordLog()
            tUserKeyWordLog.name = params.description
            tUserKeyWordLog.dateCreated = new Date()
            tUserKeyWordLog.tUserId = session.user.id
            tUserKeyWordLog.manufacturerId = tuser.getManufacturer().id
            tUserKeyWordLog.parentId = Long.valueOf(params.id)
            if (tUserKeyWordLog.save(flush: true)) {
                redirect(action: 'keyWordList')
            } else {
                println("添加失败了")
            }

        }
    }

    /**
     * 批量漏洞展示
     */
    def flawBatchShow = {
        /**
         * 查询出当前登录用户所上报的漏洞列表
         */
        if (!session.user) {
            // 去登录
            session.originalRequestParams = [controller: controllerName, action: actionName]
            return redirect(controller: "TUser", action: "login")
        }
        params.max = params.maz ? params.int('max') : 10
        params.offset = params.offset ? params.int('offset') : 0

        def criteriaList = FlawBatch.createCriteria()
        def flawBatches = criteriaList.list {
            eq("user", session.user)
            maxResults(params.max)
            firstResult(params.offset)
            order('submitTime', 'desc')
        }
        def criteriaCount = FlawBatch.createCriteria()
        def flawBatchesCount = criteriaCount.get {
            eq("user", session.user)
            projections {
                rowCount()
            }
        }
        [flawBatches: flawBatches, flawBatchesCount: flawBatchesCount]
    }
    def overview = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
    }
}
