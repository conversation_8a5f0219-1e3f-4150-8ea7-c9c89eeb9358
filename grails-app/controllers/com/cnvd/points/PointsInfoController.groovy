package com.cnvd.points

import com.cnvd.Flaw
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import com.cnvd.utils.SQLUtil
/**
 * 前台用户展示自己奖金积分
 * <AUTHOR>
 *
 */
class PointsInfoController {

	/**
	 * 我的奖金积分
	 */
	def list = {
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			redirect(controller:"TUser",action:"login")
			return
		}
		params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0
		
		def criteria = Flaw.createCriteria()
		def flawInstanceList = criteria.list{
			and{
				eq("enable",1)
				eq("user",session.user)
				isNotNull("points")
			}
			maxResults(params.max)
			firstResult(params.offset)
			order("dateCreated","desc")
		}
		def countCriteria = Flaw.createCriteria()
		def flawInstanceTotal = countCriteria.get{
			and{
				eq("enable",1)
				eq("user",session.user)
				isNotNull("points")
			}
			projections { rowCount() }
		}
		
		//获取全部积分总和
		float totalsnum = 0;
		def totals;
		def c = Flaw.createCriteria()
		def flawall = c.list{
			and{
				eq("enable",1)
				eq("user",session.user)
				isNotNull("points")
			}
		}
		if(flawall){
			for(Flaw flaw : flawall){
				totalsnum += flaw.points.total
			}
			DecimalFormat decimalFormat=new DecimalFormat(".00");//构造方法的字符格式这里如果小数不足2位,会以0补足.
			totals =decimalFormat.format(totalsnum);//format 返回的是字符串
		}
		[flawInstanceList:flawInstanceList,flawInstanceTotal:flawInstanceTotal,totals:totals]
		
	}
	
	/**
	 * 积分排名
	 */
	def rank = {
		params.max = params.max ? params.int('max') : 20
		params.offset = params.offset ? params.int('offset') : 0
		def criteria = RankPoints.createCriteria()
		String dateStr = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
		def rankPointsList = criteria.list{
			and{
				if(params.name){
					like('name','%'+params.name.trim()+'%')
				}
				ge('dateCreated', new SimpleDateFormat("yyyy-MM-dd").parse(dateStr))
			}
			maxResults(params.max)
			firstResult(params.offset)
			order("chart","asc")
		}
		def countCriteria = RankPoints.createCriteria()
		def rankPointsTotal = countCriteria.get{
			and{
				if(params.name){
					like('name','%'+params.name.trim()+'%')
				}
				ge('dateCreated', new SimpleDateFormat("yyyy-MM-dd").parse(dateStr))
			}
			projections { rowCount() }
		}
		[RankPointsList:rankPointsList,RankPointsTotal:rankPointsTotal]
	}
	
    def index = { }
}
