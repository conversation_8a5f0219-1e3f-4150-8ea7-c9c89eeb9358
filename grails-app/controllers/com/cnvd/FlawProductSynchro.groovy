package com.cnvd

class FlawProductSynchro {
	String flawId //产品ID
	Date dateCreated //创建日期
	String synchroId //产品ID
	String flawProductId //产品ID
	String status //0添加 1编辑 2删除

    static constraints = {
		flawId(nullable: true)
		flawProductId(nullable: true)
		dateCreated(nullable: true)
        synchroId(nullable:true)
		status(nullable:true)
    }

//	def beforeDelete = {
//		//删除产品前先删除特征库表中数据，2016/6/6 @zp
//		/*Feature.executeUpdate("delete from Feature where product=?",[this])*/
//		executeUpdate 'DELETE FROM Feature WHERE product = ?', [this.name]
//
//		executeUpdate 'DELETE FROM FlawProduct WHERE product.id in (select id from ProductInfo where productCategory=:productCategory)', [productCategory: this]
//		//executeUpdate 'DELETE FROM ProductEdition WHERE productCategory=:productCategory', [productCategory: this]
//		executeUpdate 'DELETE FROM Asset WHERE product in (select id from ProductInfo where productCategory=:productCategory)',[productCategory:this]
//		executeUpdate 'DELETE FROM ProductInfo WHERE productCategory.id=:id', [id: this.id]
//
//	}
}
