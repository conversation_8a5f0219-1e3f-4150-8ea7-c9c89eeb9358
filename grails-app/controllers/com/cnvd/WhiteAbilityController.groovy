package com.cnvd
/**
 * 白帽子能力象限展示
 * <AUTHOR>
 *
 */
import grails.converters.JSON
import com.cnvd.ability.WhiteAbility
import java.text.SimpleDateFormat
import org.apache.commons.collections.map.HashedMap

class WhiteAbilityController {

	def list = {
		//获取当天时间
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
		SimpleDateFormat sd = new SimpleDateFormat("yyyy")
		Date nowDate = null;
		Date endDate = null;
		String year = sd.format(new Date())
		//获取月份
		Calendar calendar=Calendar.getInstance();
		//获得当前时间的月份，月份从0开始所以结果要加1
		int month=calendar.get(Calendar.MONTH)+1;
		if(month>=2&&month<5){
			nowDate = sdf.parse(year+"-02-01")
		}else if(month>=5&&month<8){
			nowDate = sdf.parse(year+"-05-01")
		}else if(month>=8&&month<11){
			nowDate = sdf.parse(year+"-08-01")
		}else if(month>=11&&month<13){
			nowDate = sdf.parse(year+"-11-01")
		}else if(month>=1&&month<2){
			String lastYear = (Integer.parseInt(year)-1)+""
			nowDate = sdf.parse(lastYear+"-11-01")
		}
		params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0

		def whiteAbilityList = WhiteAbility.findAllByDateCreatedGreaterThanEquals(nowDate,1,[max:params.max,sort:"rank",order:"asc",offset:params.offset])
		def counts = WhiteAbility.countByDateCreatedGreaterThanEquals(nowDate,1)
		render(view:"/whiteAbility/list",model:[whiteAbilityList:whiteAbilityList, counts: counts])
	
	}
	
	def show = {
		Map<String, String> map = new HashedMap();
		WhiteAbility whiteAbility = WhiteAbility.get(params.int('id'))
		map.put("name", whiteAbility.tuser.nickName)
//		map.put("email", whiteAbility.tuser.email)
		map.put("webPoints", whiteAbility.webPoints)
		map.put("shebeiPoints", whiteAbility.shebeiPoints)
		map.put("eventPoints", whiteAbility.eventPoints)
		map.put("nixiangPoints", whiteAbility.nixiangPoints)
		map.put("level", whiteAbility.level)
		
		render map as JSON
	}
	
    def index = { }
}
