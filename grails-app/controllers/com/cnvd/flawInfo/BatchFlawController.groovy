package com.cnvd.flawInfo

import au.com.bytecode.opencsv.CSVReader

import java.text.SimpleDateFormat

import com.cnvd.DetailedInfo
import com.cnvd.Flaw
import com.cnvd.productInfo.ProductInfo
import com.cnvd.utils.FileUtil
import com.cnvd.utils.POIExcelUtil
import com.cnvd.utils.UnZip

class BatchFlawController {


	def attachmentService
	def batchFlawService
	public static List infoList = new ArrayList()
	/*public static Map flawListMap = new HashMap()
	public static List hasParentFlawList = new ArrayList()
	public static List noParentFlawList = new ArrayList()*/
	
    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]
	
	def batchFlawReport = {
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			redirect(controller:"TUser",action:"login")
			return
		}
		if(session.user.userType != 100201 || session.user.status != 100304){
			render(view:"error")
			return
		}
		render(view:"/TUser/batchFlawReport")
		return
	}
	
	def save = {
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			redirect(controller:"TUser",action:"login")
			return
		}
		//英文版本经过马栋和贾博确认后,改为->现在改成批量报送，用户都可以报送
//		if(session.user.userType != 100201 || session.user.status != 100304){
//			render(view:"error")
//			return
//		}
		def batchFlaw = new BatchFlaw()
		batchFlaw.tuser = session.user
		def file = request.getFile("patchFlawFile")
		//上传漏洞附件并设置flawInstance的attachment属性
		if(file!=null && !"".equals(file.getOriginalFilename())){
			//判断上传的附件的格式及大小
			def flag = attachmentService.checkZipFile(file)
			if(flag){
				//附件符合格式及大小限制
				String filePath = "${grailsApplication.config.filePath.batchFlawFilePath}"
//				String filePath = "e:/batchFlaw/"
				String realName = new Date().getTime() //文件的真实文件名
				//TODO start
				//1. 解压zip包
				//2. 列出所有的以csv结尾的文件 如果没有csv文件,返回错误信息: 压缩包中未找到csv文件
				//3. 分别读取csv文件, 从第三行第二列开始读
				//List<String[]> list;
				//CSVReader csvReader;
				//			//解决中文乱码
				//			try {
				//				InputStreamReader isr=new InputStreamReader(new FileInputStream(file),"GBK");
				//				csvReader = new CSVReader(isr);
				//				list = csvReader.readAll();
				//				println list.size()
				//			} catch (Exception e) {
				//				e.printStackTrace()
				//				map.put("info", "模板类型有误,请上传正确的csv文件!")
				//				map.put("flag", flag)
				//				return map
				//			}
				//for (int i = 2; i < list.size(); i++) {
				//}
				//4. 找到这列(附件名称)的值 list.get(i)[20]  如果不为空,就匹配该压缩包下是否有附件名称对应的文件(带后缀的完全匹配),如果没找到就记录下来,并循环下一行数据,直到所有csv文件的所有附件名称列值判断完
				//5. 如果错误提示不为空,就返回前端提示,指定的附件不存在,并把附件名称一并返回  如果错误提示为空,就继续下面的逻辑进行入库
				//TODO end




				def attachment = attachmentService.uploadFile(file,filePath,realName)
				batchFlaw.attachment = attachment
			}else{
				//附件不符合格式或大小限制
				def fileLimit_error = "附件类型不正确或大小超出10MB"
				flash.fileLimit_error = fileLimit_error
				// render(view:'/TUser/batchFlawReport',model:[fileLimit_error:fileLimit_error])
				return 
			}
		}
		if(batchFlaw.save(flush:true)){
			//对用户上传的zip包进行操作
			flash.message = "文件上传成功，请等待后台管理员入库"
			redirect(action:"batchFlawList",controller:"TUser")
			return
			//operate(batchFlaw)
			//println "infoList="+infoList
			//render(view:"/TUser/batchFlawReportList",model:[infoList:infoList])
		}else{
			batchFlaw.errors.allErrors.each{
				println it
			}
		}
	}
	
	/*def showExcelInfo = {
		println "params.batchFlawId="+params.batchFlawId
		def batchFlaw = BatchFlaw.get(params.batchFlawId)
		operate(batchFlaw)
		println "infoList="+infoList
		render(view:"/TUser/batchFlawReportList",model:[infoList:infoList])
	}
	
	def operate(def batchFlaw){
		println "operate batchFlaw="+batchFlaw
		def tuser = batchFlaw?.tuser
		def filePath = batchFlaw?.attachment?.path
		def filename = batchFlaw?.attachment?.fileName
		println "filePath="+filePath
		println "filename="+filename
		
		//对文件进行解压
		UnZip.deCompressZipFile(filePath)
		
		//列出所有的以xls或xlsx结尾的文件
		List<String> fileNameList = FileUtil.listFiles(filePath.substring(0,filePath.lastIndexOf("."))+"/","xls");
		List<String> fileNameList2 = FileUtil.listFiles(filePath.substring(0,filePath.lastIndexOf("."))+"/","xlsx");
		println fileNameList
		fileNameList.addAll(0,fileNameList2)
		
		//分别读取每个文件
		for(String fp : fileNameList){
			POIExcelUtil excelUtil = new POIExcelUtil(fp);
			int sheetNum = excelUtil.sheetNum(POIExcelUtil.wb);
			
			//从第三行第二列开始读
			List<ArrayList<String>> dataList = excelUtil.read(POIExcelUtil.wb,1);
			String sheetName = excelUtil.getSheetName(POIExcelUtil.wb,1);
			
			for (ArrayList<String> innerList : dataList) {
				System.out.println("size="+innerList.size());
				println innerList
				def flag = isRepeat(innerList,batchFlaw)
				println "flag="+flag
				innerList.add(flag)
				infoList.add(innerList)
			}
		}
		return true
	}
	
	def isRepeat(def innerList,def batchFlaw){
		String cveStr = innerList.get(1)
		String bidStr = innerList.get(2)
		println "cveStr="+cveStr
		println "bidStr="+bidStr
		def flag = false;
		def referType1 = ReferenceType.get(1)
		def referType2 = ReferenceType.get(2)
		if(cveStr){
			String[] cveArr = cveStr.split(",")
			for(String cve : cveArr){
				def referList = ReferenceInfo.findAllByReferenceNumberAndReferenceType(cve,referType1)
				if(referList){
					for(def refer:referList){
						def f = refer.flaw
						if(f.user?.id==batchFlaw?.tuser?.id){
							flag = true
							break;
						}else{
							def flawList = Flaw.findByParentFlaw(f)
							for(def flaw:flawList){
								if(flaw?.user?.id == batchFlaw?.tuser?.id){
									flag=true
									break;
								}
							}
						}
					}
				}
			}
		}
		if(bidStr){
			def referList = ReferenceInfo.findAllByReferenceNumberAndReferenceType(bidStr,referType2)
			if(referList){
				for(def refer:referList){
					def f = refer.flaw
					if(f.user?.id==batchFlaw?.tuser?.id){
						flag = true
						break;
					}else{
						def flawList = Flaw.findByParentFlaw(f)
						for(def flaw:flawList){
							if(flaw?.user?.id == batchFlaw?.tuser?.id){
								flag=true
								break;
							}
						}
					}
				}
			}
		}
		return flag;
	}*/
	
	def download = {
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			redirect(controller:"TUser",action:"login")
			return
		}
		if(session.user.userType != 100201 || session.user.status != 100304){
			render(view:"error")
			return
		}
		def batchFlaw = BatchFlaw.findByTuserAndDownCode(session.user,params.cd)
		if(batchFlaw){
			//2021-09-12 修改代码漏洞时注释掉该功能（该功能无用）
			//attachmentService.downloadAtt(batchFlaw.attachment, request, response)
		}else{
			render(view:"/error")
			return
		}
	}
}
