package com.cnvd.flawInfo

import java.text.SimpleDateFormat


import com.cnvd.Flaw
import com.cnvd.common.Attachment
import com.cnvd.utils.CommentsUtil

class ExploitController {

	def attachmentService
	def grailsApplication
    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]

    def create = {
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		//判断当前用户有没有权限为该漏洞添加验证
		def flawInstance = Flaw.get(params.int('flawId'))
		
		if(flawInstance){
			def exploitInstance = Exploit.findByFlaw(flawInstance)
			if(exploitInstance){
				render(view:"/error")
				return
			}else{
				def c = Flaw.createCriteria()
				def flawList = c.list{
					and{
						eq("manufacturer",session?.user?.manufacturer)
						eq("status",9)
						eq("enable",1)
						eq("isOpen",1)
						isNull("parentFlaw")
						le("openTime",new Date())
					}
				}
				if(!flawList.contains(flawInstance) || exploitInstance){
					println "true"
					render(view:"/error")
					return
				}
			}
		}else{
			render(view:"/error")
			return 
		}
    }

	def saveMyEploit={
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		
		def flawInstance = Flaw.get(params.int('flawId'))
		def c = Flaw.createCriteria()
		def flawList = c.list{
			and{
				eq("manufacturer",session?.user?.manufacturer)
				eq("status",9)
				eq("enable",1)
				eq("isOpen",1)
				isNull("parentFlaw")
				le("openTime",new Date())
			}
		}
		if (!flawInstance || !flawList.contains(flawInstance)) {
			render(view:"/error")
		}
		
		def exploitInstance = new Exploit()
		
		def file = request.getFile("exploitAttFile")
		
		if(file!=null && !"".equals(file.getOriginalFilename())){
			def flag = attachmentService.checkFile(file)
			if(flag){
				//附件符合格式及大小限制
				String filePath = "${grailsApplication.config.filePath.exploitAttFilePath}" //文件的路径
				String realName = CommentsUtil.getCurrentTime() //文件的真实文件名
				def attachment = attachmentService.uploadFile(file,filePath,realName)
				exploitInstance.attachment = attachment
			}else{
				//附件不符合格式或大小限制
				def fileLimit_error = "附件类型不正确或大小超出10MB"
				return render(view:'create',model:[fileLimit_error:fileLimit_error])
			}
		}
		
		/**
		 * 保存验证信息
		 */
		exploitInstance.exploitName = params.exploitName
		exploitInstance.concept = params.concept
		exploitInstance.poc = params.poc
		exploitInstance.suggestion = params.suggestion
		exploitInstance.exploitType = 2
		exploitInstance.tuser = session.user
		exploitInstance.flaw = flawInstance
		/**
		 * 日期格式转换
		 */
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
		exploitInstance.exploitTime = sdf.parse(params.exploitTime)
		
		/**
		 * 修改验证的状态
		 */
		exploitInstance.status = 2
		if (exploitInstance.save(flush: true)) {
			// 向exploit_log表中添加验证记录
			def exploitLogInstance = new ExploitLog()
			exploitLogInstance.flawId = exploitInstance.flaw.id
			exploitLogInstance.tuser = session.user
			exploitLogInstance.type = 2
			exploitLogInstance.status = 4
			exploitLogInstance.result = 2
			exploitLogInstance.save(flush:true)
			redirect(controller: "user",action: "myflaw")
		}else {
			render(view: "create", model: [exploitInstance: exploitInstance])
		}
	}
	
	/**
	 * 验证信息显示页面
	 */
    def show = {
        def exploitInstance = Exploit.get(params.id)
		def flaw = exploitInstance?.flaw
        if (!exploitInstance || flaw?.status != 9 || flaw?.enable != 1 || flaw.isOpen!=1 || new Date().compareTo(flawInstance.openTime)<0 || flaw.parentFlaw) {
            render(view:"/error")
        }else {
            [exploitInstance: exploitInstance]
        }
    }

    def edit = {
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
        
		def flawInstance = Flaw.get(params.int('id'))
		if(!flawInstance){
			render(view:"/error")
		}else{
			def exploitInstance = Exploit.findByFlaw(flawInstance)
			if(!exploitInstance || exploitInstance.status != 3){
				//如果无验证的话，报错
				render(view:"/error")
			}else{
				//有验证，判断漏洞是否为上报给当前用户的漏洞
				def c = Flaw.createCriteria()
				def flawList = c.list{
					and{
						eq("manufacturer",session?.user?.manufacturer)
						eq("status",9)
						eq("enable",1)
						eq("isOpen",1)
						isNull("parentFlaw")
						le("openTime",new Date())
					}
				}
				if(!flawList.contains(flawInstance)){
					render(view:"/error")
					return
				}else{
					return [exploitInstance: exploitInstance,params:params]
				}
			}
		}
    }
	
	def updateExploit = {
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		
		//判断漏洞是否为上报给当前用户的漏洞
		def exploitInstance = Exploit.get(params.exploitId)
		def c = Flaw.createCriteria()
		def flawList = c.list{
			and{
				eq("manufacturer",session?.user?.manufacturer)
				eq("status",9)
				eq("enable",1)
				eq("isOpen",1)
				isNull("parentFlaw")
				le("openTime",new Date())
			}
		}
		if (!exploitInstance || exploitInstance?.status !=3 || !flawList.contains(exploitInstance?.flaw)) {
			render(view:"/error")
		}
		/**
		 * 保存验证信息
		 */
		exploitInstance.exploitName = params.exploitName
		exploitInstance.concept = params.concept
		exploitInstance.poc = params.poc
		exploitInstance.suggestion = params.suggestion
		/**
		 * 日期格式转换
		 */
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
		exploitInstance.exploitTime = sdf.parse(params.exploitTime)
		def file = request.getFile("exploitAttFile")
		
		if(file!=null && !"".equals(file.getOriginalFilename())){
			if(exploitInstance?.attachment){
				flash.message = "验证附件已存在，不能多次上传"
				redirect(action:"edit",id:params.exploitId)
				return
			}else{
				def flag = attachmentService.checkFile(file)
				if(flag){
					//附件符合格式及大小限制
					String filePath = "${grailsApplication.config.filePath.exploitAttFilePath}" //文件的路径
					String realName = CommentsUtil.getCurrentTime() //文件的真实文件名
					def attachment = attachmentService.uploadFile(file,filePath,realName)
					exploitInstance.attachment = attachment
				}else{
					//附件不符合格式或大小限制
					def fileLimit_error = "附件类型不正确或大小超出10MB"
					return render(view:'edit',model:[exploitInstance:exploitInstance,fileLimit_error:fileLimit_error])
				}
			}
		}
		
		/**
		 * 修改验证的状态
		 */
		exploitInstance.status = 2
		if (exploitInstance.save(flush: true)) {
			// 向exploit_log表中添加验证记录
			def exploitLogInstance = new ExploitLog()
			exploitLogInstance.flawId = exploitInstance.flaw.id
			exploitLogInstance.tuser = session.user
			exploitLogInstance.type = 2
			exploitLogInstance.status = 2
			exploitLogInstance.result = 2
			exploitLogInstance.save(flush:true)
			redirect(controller: "user",action: "myflaw")
		}
		else {
			render(view: "create", model: [exploitInstance: exploitInstance])
		}
	}

	/**
	* 下载验证附件
	*/
	def download = {
		def exploitInstance = Exploit.findByDownCode(params.cd)
		def flaw = exploitInstance?.flaw
		def att = exploitInstance?.attachment
		if (!exploitInstance || !att || flaw?.status != 9 || flaw?.enable != 1 || flaw.isOpen!=1 || new Date().compareTo(flaw.openTime)<0 || flaw.parentFlaw) {
            render(view:"/error")
        }
		attachmentService.downloadAttFlaw(null,flaw,att,request,response)
	}
}
