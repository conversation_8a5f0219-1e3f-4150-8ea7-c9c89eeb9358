package com.cnvd.flawInfo

import com.cnvd.TUser
import com.cnvd.common.Attachment
import com.cnvd.utils.DateUtil
import com.cnvd.utils.DateUtils
import com.cnvd.utils.IPUtil
import com.cnvd.utils.RedisUtil
import com.cnvd.utils.SQLUtil
import org.apache.commons.lang.StringUtils
import org.apache.log4j.Logger

import java.text.SimpleDateFormat

class CertificateController {

    def attachmentService
    def jcaptchaService

    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]
    private static final Logger LOGGER = Logger.getLogger(CertificateController.class);

    /**
     * 下载pdf证书
     */
    def downPDFAtt = {
        //判断是否有证书编号，如果没有证书编号，则跳转到错误页面
        def certificate = Certificate.findByDownCode(params.cd)
        if (certificate) {
            attachmentService.downloadAttCertificate(certificate.pdfAttachment, request, response)
        } else {
            //不允许下载
            render(view: "/error")
        }
    }

    /**
     * 下载证书签名
     */
    def downCerSign = {
        def certificate = Certificate.findByDownCode(params.cd)
        if (certificate) {
            //attachmentService.downloadAtt(certificate.signatureAttachment, request, response)  2021.09.11  暂时注释掉
        } else {
            //不允许下载
            render(view: "/error")
        }
    }

    def certificate_search = {}

    /**
     * 根据输入的证书编号查询证书并显示查询结果
     */
    def search = {
        //将每天的ip请求次数存进去session,一天内同一ip请求次数超过最大限制30次！
        String ip = IPUtil.getIpAddress(request)
        String dateStr = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd")
        String mapKey = ip + "-" + dateStr
        Integer mapValue = 1
        String resultIpCount = request.getSession().getAttribute(mapKey)
        if (resultIpCount) {
            Integer sessionVlaue = Integer.valueOf(resultIpCount)
            mapValue = sessionVlaue + 1
        }
        LOGGER.info(mapKey + "|第" + mapValue + "次进来！")
        if (mapValue <= 30) {
            request.getSession().setAttribute(mapKey, mapValue)
        } else {
            flash.captcha_error = '一天内同一ip请求次数超过最大限制30次！'
            return render(view: 'certificate_search', params: params)
        }
        if (params.contributor){
            def contributor = params.contributor.replaceAll("&#40;", "\\(").replaceAll("&#41;", "\\)");
            params.contributor=contributor.trim();
        }

        String validateCode = session.validateCode

        if (params.myCode.toLowerCase() != validateCode) {
            flash.captcha_error = '验证码不正确'
            return render(view: 'certificate_search', params: params)
        }
        def certificateInstance;

        System.out.println("type==="+params.type+"|storageTime=="+params.storageTime+"|contributor=="+params.contributor);

        String cid_error="";
        String sql ="";

        if(Integer.valueOf(params.type) == 0){//原创漏洞证书
            sql = "select c.id from certificate c left join flaw f on c.flaw_id=f.id " +
                    "left join tuser t on t.id=c.tuser_id where c.c_id='"+String.valueOf(params.cerId).trim()+"'  " +
                    "and f.date_created>='"+params.storageTime+" 00:00:00' and f.date_created<='"+params.storageTime+" 23:59:59' and (c.contributor = '"+params.contributor+"'or f.discoverer_name='"+params.contributor+"' or t.user_name='"+params.contributor+"')";
            cid_error="请输入正确的”证书编号“、”贡献者“、”收录时间“";
        }else if(Integer.valueOf(params.type) == 1){//支撑单位证书
            sql = "select c.id from certificate c "+
            "left join tuser t on t.id=c.tuser_id left join manufacturer m on t.manufacturer_id=m.id " +
            "where c.c_id='"+String.valueOf(params.cerId).trim()+"' and m.name='"+params.contributor+"' and c.award_time>='"+params.storageTime+" 00:00:00' and c.award_time<='"+params.storageTime+" 23:59:59'";
            cid_error="请输入正确的”证书编号“、”支撑单位“、”颁发时间“";
        }else if(Integer.valueOf(params.type) == 2){//漏洞报送证明
            sql = "select c.id from certificate c "+
                    "left join tuser t on t.id=c.tuser_id left join manufacturer m on t.manufacturer_id=m.id "+
            "where c.c_id='"+String.valueOf(params.cerId).trim()+"' and m.name='"+params.contributor+"' and c.award_time>='"+params.storageTime+" 00:00:00' and c.award_time<='"+params.storageTime+" 23:59:59'";
            cid_error="请输入正确的”证书编号“、”报送单位“、”认证时间“";
        }else if(Integer.valueOf(params.type) == 3){//表彰证书
            sql = "select c.id from certificate c "+
            "left join tuser t on t.id=c.tuser_id left join manufacturer m on t.manufacturer_id=m.id "+
            "where c.c_id='"+String.valueOf(params.cerId).trim()+"' and (m.name='"+params.contributor+"' or t.user_name='"+params.contributor+"') and c.award_time>='"+params.storageTime+" 00:00:00' and c.award_time<='"+params.storageTime+" 23:59:59'";
            cid_error="请输入正确的”证书编号“、”表彰单位或个人“、”表彰时间“";
        }else if(Integer.valueOf(params.type) == 4){//表彰证书
            sql = "select c.id from certificate c "+
                    "left join tuser t on t.id=c.tuser_id left join manufacturer m on t.manufacturer_id=m.id "+
                    "where c.c_id='"+String.valueOf(params.cerId).trim() +"'";
            cid_error="请输入正确的”证书编号“";
        }

        if (Integer.valueOf(params.type) == 4  ){
            def cerid =  String.valueOf(params.cerId).trim();
            if (cerid==null || "".equals(cerid)){
                flash.cid_error = cid_error;
                return render(view: 'certificate_search', params: params)
            }
            if (!cerid.startsWith("CNVD-NLPJ-")){
                flash.cid_error = cid_error;
                return render(view: 'certificate_search', params: params)
            }
        }

        System.out.println("sql="+sql);
        def res = SQLUtil.getResult(sql);
        String id=null;

        res.each{
            id = it.getAt("id");
        }
        System.out.println("id======"+id);
        if(id == null){
            flash.cid_error = cid_error;
            return render(view: 'certificate_search', params: params)
        }

        def criteria = Certificate.createCriteria()
        def cerList = criteria.list{
            and{
                eq('id', Long.valueOf(id))
            }
        }
        if(cerList){
            certificateInstance  = cerList.get(0);
            session.removeAttribute("validateCode") //查询完一次证书，清空一次session验证码
            attachmentService.downloadAttCertificate(certificateInstance.pdfAttachment,request, response);
        }else{
            flash.cid_error = cid_error;
            return render(view: 'certificate_search', params: params)
        }

    }

    /**
     * 在线预览pdf
     * 2021.09.11  暂时注释掉
     */
    /*def preview = {
        def att = Attachment.get(params.id)

        request.setCharacterEncoding("UTF-8");
        BufferedInputStream bis = null;
        BufferedOutputStream bos = null;
        String downLoadPath = att.path;

        long fileLength = new File(downLoadPath).length();

        response.setContentType(att.fileType);
        response.setHeader("Content-disposition", "inline; filename=" + new String(att.fileName.getBytes("utf-8"), "ISO8859-1"));
        response.setHeader("Content-Length", String.valueOf(fileLength));

        bis = new BufferedInputStream(new FileInputStream(downLoadPath));
        bos = new BufferedOutputStream(response.getOutputStream());
        byte[] buff = new byte[2048];
        int bytesRead;
        while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
            bos.write(buff, 0, bytesRead);
        }
        bis.close();
        bos.close();
    }*/
}
