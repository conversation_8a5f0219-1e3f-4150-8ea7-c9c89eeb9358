package com.cnvd.productInfo

import com.alibaba.fastjson.JSONArray
import grails.orm.HibernateCriteriaBuilder

class ProductInfoController{

    def dataSource

    def editionList = {
        params.max = Math.min(params.max ? params.int('max') : 20,50)
        params.offset = params.offset ? params.int('offset') : 1
        def offset = (params.int('offset') - 1) * params.int('max')
        def listCriteria = ProductInfo.createCriteria()
        def productInfoList = listCriteria.list {
            and {
                if (params.productCategory) {
                    def productCategory = ProductCategory.findById(params.productCategory)
                    eq("productCategory",productCategory)
                }
            }
            maxResults(params.max)
            firstResult(offset)
        }

        def countCriteria = ProductInfo.createCriteria()
        def productInfoCount = countCriteria.get {
            and {
                if (params.productCategory) {
                    def productCategory = ProductCategory.findById(params.productCategory)
                    eq("productCategory",productCategory)
                }
            }
            projections { rowCount() }
        }
        println "productInfoList的值为:"+productInfoList
        println "productInfoCount的值为:"+productInfoCount
        ["productInfoList":productInfoList,"productInfoCount":productInfoCount]
    }

    def views = {
        String editionIds = params.editionIds
        println "editionIds的值为:"+editionIds
        String sql = "select * from product_info where id in ("+editionIds+")";
        def res = new groovy.sql.Sql(dataSource).rows(sql)
        StringBuffer editionName = new StringBuffer()
        println "res的值为:"+res
        for (int i = 0; i < res.size(); i++) {
            if(i == 0){
                editionName.append(res.get(i).get("edition"));
            }else{
                editionName.append(";"+res.get(i).get("edition"));
            }
        }
        Map<Object, Object> map = new HashMap<Object, Object>();
        map.put("editionName", editionName);
        render JSONArray.toJSONString(map);
        return
    }
}