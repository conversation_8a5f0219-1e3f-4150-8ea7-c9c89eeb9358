package com.cnvd.productInfo

import com.alibaba.fastjson.JSONArray
import com.cnvd.productInfo.ProductCategory
import grails.orm.HibernateCriteriaBuilder

import java.text.SimpleDateFormat

class ProductController {

    def dataSource

    def productList = {
        params.max = Math.min(params.max ? params.int('max') : 20,50)
        params.offset = params.offset ? params.int('offset') : 1
        def offset = (params.int('offset') - 1) * params.int('max')

        def listCriteria = ProductCategory.createCriteria()
        def format = new SimpleDateFormat("yyyy-MM-dd")
        def productCategoryList = listCriteria.list {
            and {
                if (params.name) {
                    like("name","%"+ params.name +"%")
                }
                if (params.startDate) {
                    ge("dateCreated",format.parse(params.startDate))
                }
                if (params.endDate) {
                    le("dateCreated",format.parse(params.endDate)+1)
                }
            }
            maxResults(params.max)
            firstResult(offset)
        }

        def countCriteria = ProductCategory.createCriteria()
        def productCategoryTotal = countCriteria.get {
            and {
                if (params.name) {
                    like("name","%"+ params.name +"%")
                }
                if (params.startDate) {
                    ge("dateCreated",format.parse(params.startDate))
                }
                if (params.endDate) {
                    le("dateCreated",format.parse(params.endDate)+1)
                }
            }
            projections { rowCount() }
        }
        println "productCategoryList的值为:"+productCategoryList
        println "productCategoryTotal的值为:"+productCategoryTotal
        ["productCategoryList":productCategoryList,"productCategoryTotal":productCategoryTotal]
    }

    def views = {
        String productIds = params.productIds
        String sql = "select * from product_category where id in ("+productIds+")";
        def res = new groovy.sql.Sql(dataSource).rows(sql)
        StringBuffer productName = new StringBuffer()
        for (int i = 0; i < res.size(); i++) {
            if(i == 0){
                productName.append(res.get(i).get("name"));
            }else{
                productName.append(";"+res.get(i).get("name"));
            }
        }
        Map<Object, Object> map = new HashMap<Object, Object>();
        map.put("productName", productName);
        render JSONArray.toJSONString(map);
        return

    }

}
