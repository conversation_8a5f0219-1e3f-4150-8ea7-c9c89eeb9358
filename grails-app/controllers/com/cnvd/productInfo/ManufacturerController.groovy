package com.cnvd.productInfo

import com.alibaba.fastjson.JSONArray
import com.cnvd.utils.SQLUtil
import grails.converters.JSON
import grails.orm.HibernateCriteriaBuilder
import org.apache.kafka.common.protocol.types.Field
import org.hibernate.Session

import static com.cnvd.productInfo.Manufacturer.*
import static com.cnvd.productInfo.Manufacturer.*

class ManufacturerController {
	def dataSource
    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]
	def manufacturerService

    def list = {
		def ManufacturerList =Manufacturer.list();
		//render Manufacturer.list(params) as JSO<PERSON>
		 ["ManufacturerList":ManufacturerList]
    }



	def listLookup = {

//		List<Map<String, Object>> manufactureList = manufacturerService.getList(params) //获取list集合
//		Long manufactureTotal = manufacturerService.getCount(params) //获取总记录数
//
//		println "manufactureList的值为:"+manufactureList
//		println "manufactureTotal的值为:"+manufactureTotal
//
//		["manufactureList":manufactureList,"manufacturerCount":manufactureTotal]

//		println "params的值为:"+params
//		def name = params.name
//		def email = params.email
//		println "name的值为:"+name
//		println "email的值为:"+email
		params.max = Math.min(params.max ? params.int('max') : 20,100)
		params.offset = params.offset ? params.int('offset') : 1
		def offset = (params.int('offset') - 1) * params.int('max')

		def ListCriteria = Manufacturer.createCriteria()
		def manufactureList= ListCriteria.list {
			and {
				if (params.email) {
					eq("email",params.email.trim())
				}
				if (params.name) {
					like ("name",'%'+ params.name.trim() +'%')
				}

			}
			maxResults(params.max)
			firstResult(offset)
		}

		def countCriteria = Manufacturer.createCriteria()
		def manufactureTotal = countCriteria.get {
			and {
				if (params.email) {
					eq("email",params.email.trim())
				}
				if (params.name) {
					like ("name",'%'+ params.name.trim() +'%')
				}
			}
			projections { rowCount() }
		}



		println "manufacturerList的值为:"+manufactureList
		println "manufactureTotal的值为:"+manufactureTotal
		["manufactureList":manufactureList,"count":manufactureTotal]
//		def output = null;
//		for (int i = 0; i < manufactureList.size(); i++) {
//			//def manufacture = Manufacturer.findById(manufactureList.get(i))
//			//println "漏洞厂商是值为:"+manufacture
//			output = ["id":manufactureList.get(i).id,"name":manufactureList.get(i).name,"keyword":manufactureList.get(i).Keyword]
//
//		}
//		def outputs = ["code":0,"msg":"","count":manufactureTotal,"data":manufactureList]
//
//		render outputs as JSON
		//render(view: 'listLookup', model: [ManufacturerList: manufacturerList, manufacturerCount: manufacturerCount])
	}

	def views = {
		String manufacturerIds = params.manufacturerIds
		println "manufacturerIds的值为:"+manufacturerIds
		String sql = "select * from manufacturer where id in ("+manufacturerIds+")";
		def res = new groovy.sql.Sql(dataSource).rows(sql)
		StringBuffer manufacturerName = new StringBuffer()
		for (int i = 0; i < res.size(); i++) {
			if(i == 0){
				manufacturerName.append(res.get(i).get("name"));
			}else{
				manufacturerName.append(";"+res.get(i).get("name"));
			}
		}
		Map<Object, Object> map = new HashMap<Object, Object>();
		map.put("manufacturerName", manufacturerName);
		render JSONArray.toJSONString(map);
		return

	}
	
	def categoryList = {
		render ProductCategory.findAllByManufacturer(Manufacturer.get(params.manufacturerId)) as JSON
	}
	def manufacturerListByStartWord = {
		String sql="select id,name from manufacturer where name like ?"
		def res = new groovy.sql.Sql(dataSource).rows(sql,params.startWord+"%")
		render res as JSON
	}
	def categoryListByStartWord = {
		println "params.manuId="+params.manuId
		println "params.startWord = "+params.startWord
		String sql="select id,name from product_category where name like ? and manufacturer_id = ?"
		def paramList = new ArrayList()
		paramList.add(params.startWord+"%")
		paramList.add(params.int('manuId'))
		def res = new groovy.sql.Sql(dataSource).rows(sql,paramList)
		render res as JSON
	}
	def editionList = {
		render ProductInfo.findAllByProductCategory(ProductCategory.get(params.categoryId)) as JSON
	}


}
