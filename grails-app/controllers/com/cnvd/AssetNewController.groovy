package com.cnvd
/**
 * 新版资产选择功能，仅用于漏洞报送模块资产选择
 */
import com.cnvd.productInfo.Manufacturer
import com.cnvd.productInfo.ProductCategory
import com.cnvd.productInfo.ProductInfo

class AssetNewController {

    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]

	def aelectedManuList = {
		params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0
		
		def manuNameList = null
		def counts = null

		//防止DDOS攻击
		if( params.max > 1000){
			params.max = 1000;
		}
		if(params.manuName){
			manuNameList = Manufacturer.findAllByNameLike('%'+params.manuName.trim()+'%',[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
			counts = Manufacturer.executeQuery('select count(id) from Manufacturer where name like ?',['%'+params.manuName.trim()+'%'])[0]
		}else{
			manuNameList = Manufacturer.list(max:params.max,sort:"dateCreated",order:"desc",offset:params.offset)
			counts = Manufacturer.executeQuery('select count(id) from Manufacturer)')[0]
		}
		render(view:'aelectedManuList',model:[manuNameList:manuNameList,manuNameTotal:counts])
	}
	
	def aelectedProductCategoryList = {
		params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0
		def manufacturerInstance = Manufacturer.get(params.int('manuId'))
		if(!manufacturerInstance?.name.equals(params.manuName)){
			manufacturerInstance = Manufacturer.findByName(params.manuName)
		}
		def productCategoryNameList = null
		def counts = null
		if(params.manuName){
			if(params.pCategoryName){
				productCategoryNameList = ProductCategory.executeQuery(' from ProductCategory where manufacturer = ? and name like ?',
					[manufacturerInstance,'%'+params.pCategoryName.trim()+'%'],[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
				counts = ProductCategory.executeQuery('select count(id) from ProductCategory where manufacturer = ? and name like ?',
					[manufacturerInstance,'%'+params.pCategoryName.trim()+'%'])[0]
			}else{
				productCategoryNameList = ProductCategory.executeQuery('from ProductCategory where manufacturer = ?',[manufacturerInstance],[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
				counts = ProductCategory.executeQuery('select count(id) from ProductCategory where manufacturer = ?',[manufacturerInstance])[0]
			}
		}else{
			render "err"
		}
		render(view:'aelectedProductCategoryList',model:[productCategoryNameList:productCategoryNameList,productCategoryNameTotal:counts])
	}
	
	def aelectedEditionList = {
		
		params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0
		def productCategoryInstance = ProductCategory.get(params.int('productCategoryId'))

		if(productCategoryInstance!=null && !productCategoryInstance.name.equals(params.productCategoryName)){
			productCategoryInstance = ProductCategory.findByName(params.productCategoryName)
		}
		
		def editionList = null
		def counts = null
		if(!params.manuId || !params.productCategoryName){
			render "err"
		}else{
			editionList = ProductInfo.executeQuery("from ProductInfo where productCategory = ? ",[productCategoryInstance],[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
			counts = ProductInfo.executeQuery("select count(id) from ProductInfo where productCategory = ? ",[productCategoryInstance])[0]
		}
		render(view:'aelectedEditionList',model:[editionList:editionList,editionTotal:counts])
	}
}
