package com.cnvd.info

class NewsController {

    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]

    def show = {
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			redirect(controller:"TUser",action:"login")
			return
		}
		println "params.id="+params.id
        def tuserNewsInstance = TUserNews.get(params.id)
		def newsInstance = tuserNewsInstance?.news
        if (!newsInstance || tuserNewsInstance?.tuser?.id != session.user.id) {
			render(view:"error")
			return
        }else{
			if(tuserNewsInstance?.status == 0){
				tuserNewsInstance.readTime = new Date()
				tuserNewsInstance.status = 1
				tuserNewsInstance.save(flush:true)
			}
            [newsInstance: tuserNewsInstance?.news]
        }
    }
}
