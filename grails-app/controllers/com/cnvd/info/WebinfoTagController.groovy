package com.cnvd.info

import com.cnvd.Flaw

class WebinfoTagController {

    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]
	
	// 根据tag查找tag有关的webinfo
	def webinfoList = {
		def tag = Tag.get(params.id)
		def webinfoTagList = WebinfoTag.findAllByTag(tag)
		def webinfoInstanceList = new ArrayList()
		webinfoTagList.each{
			webinfoInstanceList.add(it.webinfo)
		}
		def webinfoInstanceTotal = WebinfoTag.countByTag(tag)
		render(view:"/webinfoTag/list",model:[webinfoInstanceList:webinfoInstanceList,webinfoInstanceTotal:webinfoInstanceTotal,tag:tag])
	}
}
