package com.cnvd.info

import com.cnvd.common.Attachment
import com.cnvd.utils.SQLUtil
import com.cnvd.utils.WebinfoUtil

class WebinfoController {

	def attachmentService
    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]
	
	/**
	 * 如果type为10、11、12、13则处理完之后跳转到grouplist页面
	 * 如果type为7则处理完之后跳转到cnvd简介页面
	 * 如果type为其他数字则处理完之后跳转到list页面
	 */
    def list = {
		def typeList = ['1','2','3','4','5','6','7','8','10','11','12','13','14','15','16'] as List
		if(!typeList.contains(params.type)){
			render(view:"/error")
			return
		}
        params.max = Math.min(params.max ? params.int('max') : 20, 100)
		params.offset = params.offset ? params.int('offset') : 0
		def webinfoInstanceList
		def webinfoInstanceTotal
		if(params.int('type') == 1){
			webinfoInstanceList = Webinfo.findAllByIsRecommendAndStatus(1,1,[max:params.max,offset:params.offset,sort:"releaseTime",order:"desc"])
			webinfoInstanceTotal = Webinfo.countByIsRecommendAndStatus(1,1)
		} else if(params.int('type') == 11){
			// 用户支持组
			//webinfoInstanceList = Webinfo.findAllByTypeAndStatus(params.type,1,[max:params.max,offset:params.offset,sort:"userGroupTypeId",order:"desc"])
			webinfoInstanceList = Webinfo.createCriteria().list{
				eq('type',params.int('type'))
				eq('status',1)
				order("userGroupTypeId","asc")
				order("releaseTime","asc")
				maxResults(params.max)
				firstResult(params.offset)
			}
			webinfoInstanceTotal = Webinfo.countByTypeAndStatus(params.type,1)
		} else if(params.int('type') == 12 || params.int('type') == 13 || params.int('type') == 10) {
			webinfoInstanceList = Webinfo.findAllByTypeAndStatus(params.type,1,[max:params.max,offset:params.offset,sort:"releaseTime",order:"asc"])
			webinfoInstanceTotal = Webinfo.countByTypeAndStatus(params.type,1)
		}else {
			webinfoInstanceList = Webinfo.findAllByTypeAndStatus(params.type,1,[max:params.max,offset:params.offset,sort:"releaseTime",order:"desc"])
			webinfoInstanceTotal = Webinfo.countByTypeAndStatus(params.type,1)
		}
		
		switch(WebinfoUtil.getFlagByType(params.type)){
			case 1:
				render(view:"webinfoList",model:[webinfoInstanceList: webinfoInstanceList, webinfoInstanceTotal: webinfoInstanceTotal,type:params.type])
				break;
			case 2:
				render(view:"grouplist",model:[webinfoInstanceList: webinfoInstanceList, webinfoInstanceTotal: webinfoInstanceTotal,type:params.type])
				break;
			case 3:
				render(view:"cnvdIntroduction",model:[webinfoInstanceList: webinfoInstanceList, webinfoInstanceTotal: webinfoInstanceTotal,type:params.type])
				break;
			case 4:
				render(view:"joinCnvd",model:[webinfoInstanceList: webinfoInstanceList, webinfoInstanceTotal: webinfoInstanceTotal,type:params.type])
				break;
			case 5:
				render(view:"encourage",model:[webinfoInstanceList: webinfoInstanceList, webinfoInstanceTotal: webinfoInstanceTotal,type:params.type])
				break;
			case 6:
				render(view:"/error")
				return
		}
    }

	def show = {
		session.originalRequestParams = [controller: controllerName, action: actionName,id:params.id]
		def webinfoInstance = Webinfo.get(params.id)
		if (!webinfoInstance || webinfoInstance.status != 1 || WebinfoUtil.getFlagByType(webinfoInstance.type+"") != 1) {
			flash.message = "${message(code: 'default.not.found.message', args: [message(code: 'webinfo.label', default: 'Webinfo'), params.id])}"
			render(view:"/error")
		}else{
			webinfoInstance.clickNum = (webinfoInstance.clickNum?webinfoInstance.clickNum:0)+1
			webinfoInstance.save(flush:true)
			//查询出所有与此webinfo相关的tag,只有热点新闻有标签
			def tagList
			def wtlist
			if(webinfoInstance?.type == 2){
				tagList = WebinfoTag.findAllByWebinfo(webinfoInstance)//查询出此webinfo标签的相关webinfo
				def sql = "select * from (select * from webinfo_tag order by date_created desc) temp where exists(select tag_id from webinfo_tag where temp.tag_id = tag_id and webinfo_id = ?) and webinfo_id != ? group by webinfo_id order by date_created desc"
				def res = SQLUtil.getResult(sql, [webinfoInstance?.id,webinfoInstance?.id]);
				wtlist = new ArrayList()
				res.each {
					def webinfoTagInstance = new WebinfoTag()
					webinfoTagInstance.id = it.id
					webinfoTagInstance.dateCreated = it.date_created
					webinfoTagInstance.lastUpdated = it.last_updated
					webinfoTagInstance.tag = Tag.get(it.tag_id)
					webinfoTagInstance.webinfo = Webinfo.get(it.webinfo_id)
					wtlist.add(webinfoTagInstance)
				}
			}
			//查询出当前webinfo的所有评论
			def commentList = WebinfoComment.findAllByWebinfoAndEnable(webinfoInstance,0,[sort:"dateCreated",order:"desc"])
			def commentListCount = WebinfoComment.countByWebinfo(webinfoInstance)
			webinfoInstance.content=webinfoInstance.content.replaceAll("<p align=\"center\"><strong>编号及影响产品信息</strong>","<p align=\"center\" style=\"width:300px;\"><strong>编号及影响产品信息</strong>")
			[webinfoInstance: webinfoInstance,webinfoTagList:tagList,releaseWebinfoTagList:wtlist,commentList:commentList,commentListCount:commentListCount]
		}
	}

	/**
	* 下载漏洞的附件
	*/
   def download = {
	   def webinfoInstance = Webinfo.findByDownCode(params.cd)
	   def att = webinfoInstance?.attachment
	   if (!webinfoInstance || !att || webinfoInstance.status != 1 || WebinfoUtil.getFlagByType(webinfoInstance.type+"") != 1) {
		   render(view:"/error")
	   }
	   //2021-09-12 暂时注释掉附件的下载。2022-02-22 放开下载，因为在https://www.cnvd.org.cn/webinfo/show/4823中有附件下载
	   attachmentService.downloadAttFile(att,request,response)
   }
   
   def operationStrategy = {
	   
   }
   
   def postComment = {
	   if(!session.user) {
		   session.originalRequestParams = [controller:"webinfo", action:"show",id:params.webinfoId,content:params.content]
		   return redirect(controller: "TUser", action: "login")
	   }
       if (params.myCode.toLowerCase() != session.validateCode) {
           flash.captcha_error = '验证码不正确'
           redirect(action: "show", id: params.webinfoId)
           return
       }
	   //添加一条评论
	   def comment = new WebinfoComment()
	   comment.webinfo = Webinfo.get(params.webinfoId)
	   comment.user = session.user
	   comment.content = params.content
	   if(comment.save(flush:true)){
		   redirect(action: "show", id: params.webinfoId)
	   }else{
		   flash.message = "${message(code: 'default.not.found.message', args: [message(code: 'webinfo.label', default: 'Webinfo'), params.id])}"
		   render(view:"/error")
	   }
   }
   
   
   /**
    * 下载公约签约申请表 
    * 2016/4/21
    * <AUTHOR>
    */
   def downloadGY = {
	   def att=new Attachment();
	   att.fileName = "公约签约申请表.docx"
	   String path = this.getServletContext().getRealPath("/download");
	   println "path = "+path;
	   att.path=path +"/"+att.fileName
	   attachmentService.downloadAttFile(att,request,response);

   }

	def downloadBSMB = {
		def att=new Attachment();
		att.fileName = "报送模板.csv"
		String path = this.getServletContext().getRealPath("/download");
		println "path = "+path;
		att.path=path +"/"+att.fileName
		attachmentService.downloadAttFile(att,request,response);

	}

	def downloadCKMB = {
		def att=new Attachment();
		att.fileName = "参考模板.xlsx"
		String path = this.getServletContext().getRealPath("/download");
		println "path = "+path;
		att.path=path +"/"+att.fileName
		attachmentService.downloadAttFile(att,request,response);

	}
	def downloadJSSQCL = {
		def att=new Attachment();
		att.fileName = "CNVD技术组支撑单位申请材料模板.docx"
		String path = this.getServletContext().getRealPath("/download");
		println "path = "+path;
		att.path=path +"/"+att.fileName
		attachmentService.downloadAttFile(att,request,response);

	}

	def downloadYHSQCL = {
		def att=new Attachment();
		att.fileName = "CNVD用户组支撑单位申请材料模板.docx"
		String path = this.getServletContext().getRealPath("/download");
		println "path = "+path;
		att.path=path +"/"+att.fileName
		attachmentService.downloadAttFile(att,request,response);

	}
}
