package com.cnvd

import com.cnvd.ability.MemberAbility
import com.cnvd.info.Webinfo
import grails.converters.JSON
import org.apache.commons.collections.map.HashedMap

class MemberAbilityController {

    def show = {
		Map<String, String> map = new HashedMap();
		println params.int('id')
		Webinfo webinfo = Webinfo.get(params.int('id'))
		MemberAbility whiteAbility = MemberAbility.findByWebinfo(webinfo)
		map.put("sumNumber", whiteAbility.sumNumber)
		map.put("firstNumber", whiteAbility.firstNumber)
		map.put("flawLevel", whiteAbility.flawLevel)
		
		map.put("eventNumber", whiteAbility.eventNumber)
		map.put("eventPoints", whiteAbility.eventPoints)
		map.put("eventLevel", whiteAbility.eventLevel)
		
		map.put("noEventFlaw", whiteAbility.noEventFlaw)
		map.put("noEventLevel", whiteAbility.noEventLevel)
		
		map.put("analysis", whiteAbility.analysis)
		map.put("analysisLevel", whiteAbility.analysisLevel)
		
		map.put("support", whiteAbility.support)
		map.put("supportLevel", whiteAbility.supportLevel)
		
		map.put("noflaw", whiteAbility.eventNumber+whiteAbility.noEventFlaw)
		map.put("noflawPoints", whiteAbility.eventPoints+whiteAbility.noEventPoints)
		render map as JSON
	}
}
