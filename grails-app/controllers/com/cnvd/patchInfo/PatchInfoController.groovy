package com.cnvd.patchInfo

import com.cnvd.Flaw
import com.cnvd.SynchroErrData
import com.cnvd.common.Attachment
import com.cnvd.productInfo.Manufacturer
import com.cnvd.utils.*
import net.sf.json.JSONArray
import net.sf.json.JSONObject
import org.springframework.core.io.ClassPathResource

import java.text.SimpleDateFormat

class PatchInfoController {
	def attachmentService
	def grailsApplication
	def toJsonObjectService
	def properties = org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(new ClassPathResource("app-config.properties"))
	def patchSendMail = properties.getProperty("patch.send.mail")
	def kafkaIp = properties.getProperty("kafka.ip")
	int kafkaCount = Integer.valueOf(properties.getProperty("kafka.count"))
    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]

    def list = {
		//分页列出所有的审核已通过的补丁信息
        params.max = Math.min(params.max ? params.int('max') : 20, 100)
		params.offset = params.offset ? params.int('offset') : 0

		def hql = "from PatchInfo p where p.status='3' and p.enable=1 and exists(select id from Flaw f where p.flaw.id=f.id and f.status = 9 and f.enable = 1 and f.openTime <= :openTime and f.isOpen=1 and f.parentFlaw is null)"
		def hqlCount = "select count(p.id) from PatchInfo p where p.status='3' and p.enable=1 and exists(select id from Flaw f where p.flaw.id=f.id and f.status = 9 and f.enable = 1 and f.openTime <= :openTime and f.isOpen=1 and f.parentFlaw is null)"

		def hqlPara = new HashMap()
		hqlPara.put("openTime", new Date())

		if(params.patchName){
			hql += " and p.patchName like :patchName escape '\'"
			hqlCount += " and p.patchName like :patchName escape '\'"
			hqlPara.put("patchName","%\\"+params.patchName+"%")
		}
		if(params.startDate){
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd")
			hql += " and p.dateCreated >= :startTime"
			hqlCount += " and p.dateCreated >= :startTime"
			hqlPara.put("startTime",format.parse(params.startDate))
		}
		if(params.endDate){
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd")
			hql += " and p.dateCreated <= :endTime"
			hqlCount += " and p.dateCreated <= :endTime"
			hqlPara.put("endTime",format.parse(params.endDate))
		}
		hql+=" order by dateCreated desc"
		def patchList = PatchInfo.executeQuery(hql,hqlPara,[max:params.max,offset:params.offset])
		def count = PatchInfo.executeQuery(hqlCount,hqlPara)
        [patchInfoInstanceList: patchList, patchInfoInstanceTotal: count[0]]
    }

    def create = {
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}

		//判断当前用户有没有权限为该漏洞添加验证
		def flawInstance = Flaw.get(params.int('flawId'))

		if(flawInstance){
			def patchInstance = PatchInfo.findByFlaw(flawInstance)
			if(patchInstance){
				render(view:"/error")
				return
			}else{
				def c = Flaw.createCriteria()
				def flawList = c.list{
					and{
						eq("manufacturer",session?.user?.manufacturer)
						eq("status",9)
						eq("enable",1)
						eq("isOpen",1)
						isNull("parentFlaw")
						le("openTime",new Date())
					}
				}
				def flawIdList = new ArrayList()
				for(def f : flawList){
					flawIdList.add(f.id)
				}
				if(!flawIdList.contains(flawInstance?.id)){
					render(view:"/error")
					return
				}
			}
		}else{
			render(view:"/error")
			return
		}
    }

	def saveMyPatchInfo={
		println "params.attIds="+params.attIds
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}

		def flawInstance = Flaw.get(params.int('flawId'))
		def c = Flaw.createCriteria()
		def flawList = c.list{
			and{
				eq("manufacturer",session?.user?.manufacturer)
				eq("status",9)
				eq("enable",1)
				eq("isOpen",1)
				isNull("parentFlaw")
				le("openTime",new Date())
			}
		}
		def flawIdList = new ArrayList()
		for(def f : flawList){
			flawIdList.add(f.id)
		}
		if (!flawInstance || !flawIdList.contains(flawInstance?.id) || PatchInfo.findByFlaw(flawInstance)) {
			render(view:"/error")
		}

		def patchInfoInstance = new PatchInfo()

		def file = request.getFile("attachmentFile")
		if(file!=null && !"".equals(file.getOriginalFilename())){
			def flag = attachmentService.checkFile(file)
			if(flag){
				//附件符合格式及大小限制
				String filePath = "${grailsApplication.config.filePath.patchInfoAttFilePath}" //文件的路径
				String realName = CommentsUtil.getCurrentTime() //文件的真实文件名
				def attachment = attachmentService.uploadFile(file,filePath,realName)
				patchInfoInstance.attachment = attachment
				params.attIds = attachment.id
			}else{
				//附件不符合格式或大小限制
				def fileLimit_error = "附件类型不正确或大小超出10MB"
				return render(view:'create',model:[fileLimit_error:fileLimit_error])
			}
		}

		/**
		* 保存添加的补丁信息
		*/
		patchInfoInstance.patchName = params.patchName
		patchInfoInstance.patchUrl = params.patchUrl
		patchInfoInstance.function = params.function
		patchInfoInstance.patchDescription = params.patchDescription
		patchInfoInstance.flaw = flawInstance
		patchInfoInstance.tuser = session.user
		patchInfoInstance.createSource = "2"

		/**
		 * 修改补丁的状态
		 */
		patchInfoInstance.status = "1"
		if (patchInfoInstance.save(flush: true)) {
			if(params.attIds){
				//def idArr = params.attIds.split(";")
				//idArr.each{
					//def attachment = Attachment.get(it)
					def attachment = Attachment.get(params.int('attIds'))
					def patchInfoAttachment = new PatchInfoAttachment()
					patchInfoAttachment.patchInfo = patchInfoInstance
					patchInfoAttachment.attachment = attachment
					patchInfoAttachment.save(flush:true)
				//}
			}
			// 向补丁deallog表中插入一条记录
			def dealLog = new DealLog()
			dealLog.patchId = patchInfoInstance.patchId
			dealLog.tuser = patchInfoInstance.tuser
			dealLog.description = patchInfoInstance.patchDescription
			dealLog.status = "2"
			dealLog.save(flush:true)
			flawInstance.ivp = 2
			flawInstance.save(flush:true)
			redirect(controller:"user",action: "myflaw")
		}
		else {
			render(view: "create", model: [patchInfoInstance: patchInfoInstance])
		}
	}

    def update = {
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}

		//判断漏洞是否为上报给当前用户的漏洞
		def patchInfoInstance = PatchInfo.get(params.int('patchInfoId'))
		def c = Flaw.createCriteria()
		def flawList = c.list{
			and{
				eq("manufacturer",session?.user?.manufacturer)
				eq("status",9)
				eq("enable",1)
				eq("isOpen",1)
				isNull("parentFlaw")
				le("openTime",new Date())
			}
		}
		def flawIdList = new ArrayList()
		for(def f:flawList){
			flawIdList.add(f.id)
		}
		if (!patchInfoInstance || patchInfoInstance?.status !="2" || !flawIdList.contains(patchInfoInstance?.flaw?.id)) {
			render(view:"/error")
		}

		/**
		 * 保存添加的补丁信息
		 */

		if (!patchInfoInstance) {
			render(view:"/error")
		}
		patchInfoInstance.patchName = params.patchName
		patchInfoInstance.patchUrl = params.patchUrl
		patchInfoInstance.function = params.function
		patchInfoInstance.patchDescription = params.patchDescription

		//发送kafka同步信息start
		/*
        isPush 是否是点击推送按钮 0推送按钮 1不是推送按钮{默认传0}
        isLoophole 0是漏洞信息，1是非漏洞信息
        flawId 如果是1非漏洞信息 不传这个字段
        operate 0新增1修改2删除3软删除
        */
		JSONObject jsonImp = new JSONObject()
		def arrayDate = new JSONArray()

		def file = request.getFile("patchAttFile")
		if(file!=null && !"".equals(file.getOriginalFilename())){
			if(patchInfoInstance?.attachment){
				//补丁存在附件
				flash.message = "补丁附件已存在，不能多次上传附件"
				redirect(action:"edit",id:params.patchInfoId)
				return
			}else{
				def flag = attachmentService.checkFile(file)
				if(flag){
					//附件符合格式及大小限制
					String filePath = "${grailsApplication.config.filePath.patchInfoAttFilePath}" //文件的路径
					String realName = CommentsUtil.getCurrentTime() //文件的真实文件名
					def attachment = attachmentService.uploadFile(file,filePath,realName)
					patchInfoInstance.attachment = attachment

					JSONObject jsonImp0= new JSONObject()
					jsonImp0.put("isPush", 1)
					jsonImp0.put("isLoophole", 0)
					jsonImp0.put("flawId", patchInfoInstance.flawId)
					jsonImp0.put("operate", 0)
					JSONObject json0 = toJsonObjectService.toJsonObject(attachment)
					jsonImp0.put("entity", json0.toString())
					arrayDate.add(jsonImp0)
				}else{
					//附件不符合格式或大小限制
					def fileLimit_error = "附件类型不正确或大小超出10MB"
					return render(view:'edit',model:[patchInfoInstance:patchInfoInstance,fileLimit_error:fileLimit_error])
				}
			}
		}

		/**
		 * 修改补丁的状态
		 */
		patchInfoInstance.status = "1"
        if (patchInfoInstance.save(flush: true)) {
			// 向补丁deallog表中插入一条记录
			def dealLog = new DealLog()
			dealLog.patchId = patchInfoInstance.patchId
			dealLog.tuser = patchInfoInstance.tuser
			dealLog.description = patchInfoInstance.patchDescription
			dealLog.status = "2"
			dealLog.save(flush:true)



			JSONObject jsonImp1 = new JSONObject()
			jsonImp1.put("isPush", 1)
			jsonImp1.put("isLoophole", 0)
			jsonImp1.put("flawId", patchInfoInstance.flawId)
			jsonImp1.put("operate", 1)
			JSONObject json1 = toJsonObjectService.toJsonObject(patchInfoInstance)
			jsonImp1.put("entity", json1.toString())
			arrayDate.add(jsonImp1)
			//----------------------------------------------------------------------------------------
			JSONObject jsonImp2 = new JSONObject()
			jsonImp2.put("isPush", 1)
			jsonImp2.put("isLoophole", 1)
			jsonImp2.put("operate", 0)
			JSONObject json2 = toJsonObjectService.toJsonObject(dealLog)
			jsonImp2.put("entity", json2.toString())
			arrayDate.add(jsonImp2)
			//----------------------------------------------------------------------------------------

			jsonImp.put("time", DateUtil.get4yMdHms(new Date()))
			jsonImp.put("date", arrayDate.toString())
			sendKafkaMsg(patchInfoInstance.flaw,jsonImp)
			//发送kafka同步信息end

			//修改补丁信息发送邮件给后台管理员
			if(patchSendMail) {
				def toMail = patchSendMail.split(";")
				def subject = "补丁更新通知"
				def flawNumber = patchInfoInstance?.flaw?.status == 9 ? patchInfoInstance.flaw.number : (patchInfoInstance.flaw.tempNumber ? (patchInfoInstance.flaw.tempNumber) : (patchInfoInstance.flaw.number))
				StringBuilder content = new StringBuilder();
				content.append("<p style=\"margin-left:20px\">您好：</p>");
				content.append("<p style=\"margin-left:20px\">CNVD编号：" + flawNumber + "</p>");
				content.append("<p style=\"margin-left:20px\">备注：" + patchInfoInstance.remark + "</p>");
				content.append("<p style=\"margin-left:20px\">祝好！</p>");

				SendMail sendMail = SendMail.getInstance()
				try {
					sendMail.send(toMail,subject, content.toString(), null);
					log.info("发送邮件成功")
				} catch (Exception e) {
					log.error("发送邮件失败|" + e.toString())
					flash.message = "发送邮件失败"
					redirect(action: "edit", id: params.patchInfoId)
					return
				}
			}else{
				log.info("补丁信息发送邮件配置为空")
			}


            redirect(controller:"user",action: "myflaw")
        }else {
            render(view: "create", model: [patchInfoInstance: patchInfoInstance])
        }
    }

    def show = {
		def patchInfoInstance
		if(params.id.startsWith("CNPD")){
			patchInfoInstance = PatchInfo.findByPatchId(params.id)
		}else{
			patchInfoInstance = PatchInfo.get(params.int('id'))
		}
		println "patchInfoInstance="+patchInfoInstance
		def flaw = patchInfoInstance?.flaw
        if (!patchInfoInstance || patchInfoInstance.status != "3"
			|| flaw?.status != 9 || flaw?.enable != 1 || new Date().compareTo(flaw?.openTime)<0
			|| flaw?.enable !=1 || flaw?.parentFlaw) {
            render(view:"/error")
        }else {
			/**
			 * 查询出最新补丁
			 */
			def hql = " from PatchInfo where status = :status and id != :patchInfoId and flaw.status = 9 and flaw.enable = 1 and flaw.openTime <= :openTime and flaw.isOpen=1 and flaw.parentFlaw is null order by dateCreated desc"
			def hqlPara = new HashMap()
			hqlPara.put("status","3")
			hqlPara.put("patchInfoId",patchInfoInstance?.id)
			hqlPara.put("openTime",new Date())
			def lastedPatchInfoList = PatchInfo.executeQuery(hql,hqlPara,[max:10,offset:0])
            [patchInfoInstance: patchInfoInstance,lastedPatchInfoList:lastedPatchInfoList]
        }
    }
    def edit = {
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}

		def flawInstance = Flaw.get(params.int('id'))
		if(!flawInstance){
			render(view:"/error")
		}else{
			def patchInstance = PatchInfo.findByFlaw(flawInstance)
			if(!patchInstance || patchInstance.status != "2"){
				render(view:"/error")
			}else{
				def c = Flaw.createCriteria()
				def flawList = c.list{
					and{
						eq("manufacturer",session?.user?.manufacturer)
						eq("status",9)
						eq("enable",1)
						eq("isOpen",1)
						isNull("parentFlaw")
						le("openTime",new Date())
					}
				}
				def flawIdList = new ArrayList()
				for(def f:flawList){
					flawIdList.add(f.id)
				}
				if(!flawIdList.contains(flawInstance?.id)){
					render(view:"/error")
					return
				}else{
					return [patchInstance: patchInstance]
				}
			}
		}
    }

	/**
	 * 下载补丁附件
	 */
	def download = {
		println "params.id="+params.id
		println "params.cd="+params.cd
		def patchInfoInstance = PatchInfo.findByDownCode(params.cd)
		def flaw = patchInfoInstance?.flaw
		if (!patchInfoInstance || patchInfoInstance.status != "3"
			|| flaw?.status != 9 || flaw?.enable != 1 || new Date().compareTo(flaw?.openTime)<0
			|| flaw?.enable !=1 || flaw?.parentFlaw) {
			render(view:"/error")
		}
		def attachment = Attachment.get(Long.parseLong(EncryptUtils.aesDecrypt(params.id+"")))
		if(PatchInfoAttachment.findByPatchInfoAndAttachment(patchInfoInstance,attachment)){
			//2021-09-12 暂时注释掉
			//attachmentService.downloadAtt(attachment,request,response)
		}else{
			render(view:"/error")
		}
	}
	/**
	 * 下载漏洞的附件
	 */
	def downloadself = {
		println "params.attId="+params.attId
		println "params.cd="+params.cd
		if(!session.user) {
			session.originalRequestParams = [controller:"TUser", action:"mypatchInfoShow"]
			return redirect(controller: "TUser", action: "login")
		}
		def patchInfo = PatchInfo.findByDownCode(params.cd)
		if(patchInfo){
			def attachment = Attachment.get(params.attId)
			//def attachment1 = PatchInfoAttachment.findByPatchInfoAndAttachment(patchInfo,attachment)
			if(attachment){
				attachmentService.downloadAttPatch(session.user,patchInfo,attachment,request,response)
			}else{
				render(view:"/error")
				return
			}
		}else{
			render(view:"/error")
			return
		}
	}
	def listResult = {
		params.max = Math.min(params.max ? params.int('max') : 20, 100)
		params.offset = params.offset ? params.int('offset') : 0
		def patchInfoInstanceList = PatchInfo.list(max:params.max,offset:params.offset)
		def patchInfoInstanceTotal = PatchInfo.count()

		def queryHqlRs = "from Flaw f where f.openTime<=? and f.status = 9 and f.enable = 1 and f.isOpen=1 and f.parentFlaw is null "
		def queryHql=""
		def queryPara = new ArrayList()
		queryPara.add(new Date())

		if(params.serverityIdStr){
			def str = params.serverityIdStr.substring(0,params.serverityIdStr.length()-1)
			queryHql += " and f.serverityId in ("+str+")"
		}

		if(params.manufacturerId && !"-1".equals(params.manufacturerId)){
			def manufacturer = Manufacturer.get(params.manufacturerId)
			queryHql += " and f.manufacturer = ? "
			queryPara.add(manufacturer)
		}
		if(params.cnvdId){
			if("0".equals(params.cnvdIdFlag)){
				queryHql += " and f.number like ? escape '\' "
				queryPara.add("%\\"+params.cnvdId+"%")
			}else if("1".equals(params.cnvdIdFlag)){
				queryHql += " or f.number like ? escape '\'"
				queryPara.add("%\\"+params.cnvdId+"%")
			}
		}
		queryHql += " order by openTime desc,title asc"
		queryHqlRs=queryHqlRs+queryHql

		def flawInstanceList = Flaw.executeQuery(queryHqlRs,queryPara)
		flawInstanceList.add(Flaw.get(-1L))
		def patchInfoCriteria = PatchInfo.createCriteria()
		patchInfoInstanceList = patchInfoCriteria.list{
			and{
				eq("status","3")
				eq("enable",1)
				if(flawInstanceList){
					'in'("flaw",flawInstanceList)
				}
			}
			maxResults(params.max)
			firstResult(params.offset)
			order("dateCreated","desc")
		}
		def countCriteria = PatchInfo.createCriteria()
		patchInfoInstanceTotal = countCriteria.get{
			and{
				eq("status","3")
				eq("enable",1)
				if(flawInstanceList){
					'in'("flaw",flawInstanceList)
				}
			}
			projections { rowCount() }
		}

		[patchInfoInstanceList:patchInfoInstanceList,patchInfoInstanceTotal:patchInfoInstanceTotal]
	}

	def uploadAtt = {
		def file = request.getFile("Filedata");

		if(attachmentService.checkFile(file)){
			String filePath = "${grailsApplication.config.filePath.patchInfoAttFilePath}" //文件的路径
			String realName = new Date().getTime() //文件的真实文件名
			def attachment = attachmentService.uploadFile(file,filePath,realName)
			println "attId="+attachment.id
			println "fileName="+attachment.fileName

			//render "attId:"+attachment.id+";fileName:"+attachment.fileName
			render ""+attachment.id
		}else{
			render "type error"
		}
	}


	/**
	 *
	 * 公用的发送kafka消息
	 *
	 */
	void sendKafkaMsg(Flaw flaw, JSONObject jsonImp){
		String kafkaTopic = IsToPush.isRead(flaw.getSoftStyleId().toString())
		if (kafkaTopic != null) {
			println "json|" + jsonImp.toString()
            try {
			if (flaw.push == 1) {
				if (kafkaTopic != null) {
					try {
						new SendMsgProducer(jsonImp, kafkaIp, kafkaTopic).start()
					} catch (Exception e) {
						def synchroErrData = new SynchroErrData()
						synchroErrData.errorData = jsonImp.toString()
						synchroErrData.errorReason = e.message.toString()
						synchroErrData.status = 0
						synchroErrData.topic = kafkaTopic
						synchroErrData.save(flush: true)
					}
				}
			}
		  }catch (Exception e) {
			def synchroErrData = new SynchroErrData();
			synchroErrData.errorData=jsonImp.toString();
			synchroErrData.errorReason=e.message.toString();
			synchroErrData.status=0;
			synchroErrData.topic=kafkaTopic
			synchroErrData.save(flush: true)
		}
		}
	}
}
