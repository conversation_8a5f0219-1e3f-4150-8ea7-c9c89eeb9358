package com.cnvd.scan

import java.util.regex.Matcher
import java.util.regex.Pattern

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import com.cnvd.common.Attachment;
import com.cnvd.utils.HttpPostJson;

import grails.converters.JSON

class ScanExpController {

	def jcaptchaService
	def attachmentService
	/**
	 * 我的exp任务
	 */
	def list = {
		String user = session.user;
		/**
		 * 查询出当前登录用户所上报的URL列表
		 */
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			return redirect(controller:"TUser",action:"login")
		}
		def expTaskList
		def counts
		params.max = params.max ? params.int('max') : 5
		params.offset = params.offset ? params.int('offset') : 0

		expTaskList = ExpTask.findAllByUserAndEnable(session.user,1,[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
		counts = ExpTask.countByUserAndEnable(session.user,1)

		render(view:"/scanExp/list",model:[expTaskList:expTaskList, counts: counts])
	}
	
	/**
	 * 创建验证任务
	 */
	def create = {
		/**
		 * 先判断是否有用户登录，如果有的话，则跳转到URL上报页面，不然跳转到登录页面
		 */
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		render(view:"/scanExp/create",model:[obj:params])
	}
	
	/**
	 * 验证任务保存
	 */
	def save = {

		if(!session.user){
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		//校验任务名称是否为空
		if(!params.name){
			def name_error = "请输入任务名称"
			render(view:"/scanExp/create",model:[name_error:name_error])
			return
		}
		//校验任务名称是否为空
		if(params.name.length()<4 || params.name.length()>24){
			def name_error = "请输入正确的任务名称"
			render(view:"/scanExp/create",model:[name_error:name_error])
			return
		}
		//校验验证地址为空
		if(!params.targets){
			def targets_error = "请输入验证地址"
			render(view:"/scanExp/create",model:[tags_error:targets_error])
			return
		}
		
		//校验验证码
		try{
			if (!jcaptchaService.validateResponse("imageCaptcha", session.id, params.myCode)){
				def myCode_error = "验证码不正确"
				render(view:"/scanExp/create",model:[myCode_error:myCode_error])
				return
			}
		}catch(Exception e){
			def myCode_error = "验证码不正确"
			render(view:"/scanExp/create",model:[myCode_error:myCode_error])
			return
		}
		//---------访问验证平台---------------
		String name = params.name
		String targets = params.targets
		println targets
		String tags = params.tags
		int level = params.int('level')
		String taskurl = "http://192.168.101.203/fire/task/add"

		def resstring = "";
		HttpPostJson ac = new HttpPostJson(taskurl);
		JSONObject obj = new JSONObject();
		obj.put("name", name);
		obj.put("targets", targets);
		obj.put("tags", tags);
		obj.put("level", level);
//		System.out.println(ac.post(obj.toString()));
		resstring = ac.post(obj.toString())
		//将返回的数据转换
		def res = JSON.parse(resstring);
		if(res){
			String code = res.code;
			String msg = res.msg;
			String task_id = res.task_id;
			//存储
			def expInstance = new ExpTask()
			if(Integer.parseInt(code)==10500){
				//失败
				expInstance.status = 0
			}
			expInstance.user = session.user
			expInstance.name = name
			expInstance.targets = targets
			expInstance.tags = tags
			expInstance.level = level
			expInstance.code = code
			expInstance.msg = msg
			expInstance.task_id = task_id
			expInstance.save(flush: true)
		}else{
			def expInstance = new ExpTask()
			expInstance.user = session.user
			expInstance.name = name
			expInstance.targets = targets
			expInstance.tags = tags
			expInstance.level = level
			expInstance.status = 0
			expInstance.save(flush: true)
		}
		redirect(controller:"scanExp",action:"list")
		return
	}
	
	/**
	 * 下载pdf附件
	 */
	def downLoadPdf = {
		if(!session.user){
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		def et = ExpTask.get(params.id)
		if(session.user.id != et.user.id){
			render(view:"/error")
			return
		}
		if(et.status!=4){
			render(view:"/error")
			return
		}
		AttExpPdf att = et.expPdf
		attachmentService.downloadPdf(att,request,response)
	}
	
	/**
	 * 下载html附件
	 */
	def downLoadHtml = {
		if(!session.user){
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		def et = ExpTask.get(params.id)
		if(session.user.id != et.user.id){
			render(view:"/error")
			return
		}
		if(et.status!=4){
			render(view:"/error")
			return
		}
		AttExpHtml att = et.expHtml
		attachmentService.downloadHtml(att,request,response)
	}
	
    def index = { }
}
