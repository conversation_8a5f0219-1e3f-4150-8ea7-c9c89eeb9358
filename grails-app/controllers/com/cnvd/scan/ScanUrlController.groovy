package com.cnvd.scan

import com.cnvd.utils.MD5
import com.cnvd.common.Attachment
import java.net.URL;
import java.text.SimpleDateFormat
import java.util.regex.Matcher
import java.util.regex.Pattern
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse
import com.cnvd.utils.SQLUtil
import grails.converters.JSON

/**
 * 扫描URL
 * <AUTHOR>
 *
 */
class ScanUrlController {
	def jcaptchaService

	/**
	 * 我的上报
	 */
	def urlInfo = {
		String user = session.user;
		/**
		 * 查询出当前登录用户所上报的URL列表
		 */
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			return redirect(controller:"TUser",action:"login")
		}
		def urlInstanceList
		def counts
		params.max = params.max ? params.int('max') : 5
		params.offset = params.offset ? params.int('offset') : 0

		urlInstanceList = ScanUrl.findAllByUserAndEnable(session.user,1,[max:params.max,sort:"submitTime",order:"desc",offset:params.offset])
		counts = ScanUrl.countByUserAndEnable(session.user,1)

		render(view:"/scan/urlInfo",model:[urlInstanceList:urlInstanceList, counts: counts])
	}

	/**
	 * 上报URL及相关信息
	 */
	def create = {
		/**
		 * 先判断是否有用户登录，如果有的话，则跳转到URL上报页面，不然跳转到登录页面
		 */
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		def urlInstance = new ScanUrl()
		render(view:"/scan/urlCreate",model:[urlInstance:urlInstance,obj:params])
	}

	/**
	 * 上报URL页面信息的保存
	 */
	def save = {

		if(!session.user){
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		//校验url是否为空
		if(!params.url){
			def url_error = "请输入URL"
			render(view:"/scan/urlCreate",model:[url_error:url_error])
			return
		}
		//校验url格式是否正确
		def regEx = /^http:\/\/.*/;	//https网址正则
		def _regEx = /^https:\/\/.*/;	//http
		// 编译正则表达式
		Pattern pattern = Pattern.compile(regEx);
		Matcher matcher = pattern.matcher(params.url);
		Pattern _pattern = Pattern.compile(_regEx);
		Matcher _matcher = _pattern.matcher(params.url);
		// 字符串是否与正则表达式相匹配
		boolean rs = matcher.matches();
		boolean _rs = _matcher.matches();
		if(!rs && !_rs){
			def http_error = "格式有误，请输入正确的URL！"
			render(view:"/scan/urlCreate",model:[http_error:http_error])
			return
		}

		//校验要扫描的漏洞是否为空
		if(!params.subBugNameValue){
			def bugName_error = "请选择要扫描的漏洞"
			render(view:"/scan/urlCreate",model:[bugName_error:bugName_error])
			return
		}
		
		//校验验证码
		try{
			if (!jcaptchaService.validateResponse("imageCaptcha", session.id, params.myCode)){
				def myCode_error = "验证码不正确"
				render(view:"/scan/urlCreate",model:[myCode_error:myCode_error])
				return
			}
		}catch(Exception e){
			def myCode_error = "验证码不正确"
			render(view:"/scan/urlCreate",model:[myCode_error:myCode_error])
			return
		}
		
		def urlInstance = new ScanUrl(params)
		urlInstance.status = 1
		urlInstance.url = params.url
		urlInstance.statusDo = 1
		urlInstance.submitTime = new Date()
		urlInstance.enable = 1
		urlInstance.user = session.user
		//漏洞名称
		def str  = params.subBugNameValue
		def bugname = str.substring(0, str.length()-1)
		urlInstance.bugName = bugname
		urlInstance.save(flush: true)
		redirect(controller:"scanUrl",action:"urlInfo")
		return
	}

	/**
	 * 显示我的URL信息
	 */
	def myurl = {
		//	response.setCharacterEncoding("UTF-8")
		response.setContentType("text/html;charset=UTF-8");
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			redirect(controller:"TUser",action:"login")
			return
		}
		def urlInstance = ScanUrl.get(params.id)
		def bugName = urlInstance.bugName
		String[] strs = bugName.split (",");
		StringBuffer sb = new StringBuffer()
		for(String s:strs){
			if(s.equals("SqlScanner")){
				sb.append("SQL注入漏洞， ")
			}else if(s.equals("XSS")){
				sb.append("XSS漏洞， ")
			}else if(s.equals("Pollution")){
				sb.append("参数污染， ")
			}else if(s.equals("Struts")){
				sb.append("JAVA漏洞库， ")
			}else if(s.equals("Include")){
				sb.append("文件包含漏洞， ")
			}else if(s.equals("URLRedirect")){
				sb.append("URL跳转漏洞， ")
			}else if(s.equals("Nginx")){
				sb.append("Nginx任意文件执行漏洞， ")
			}else if(s.equals("SVN")){
				sb.append("SVN/CVS文件泄漏漏洞， ")
			}else if(s.equals("PaddingOracle")){
				sb.append("Padding Oracle漏洞， ")
			}else if(s.equals("DirectoryListing")){
				sb.append("目录遍历漏洞， ")
			}else if(s.equals("ApacheCGI")){
				sb.append("ApacheCGI命令执行漏洞， ")
			}else if(s.equals("DNS")){
				sb.append("DNS漏洞， ")
			}else if(s.equals("FlashCrossdomain")){
				sb.append("Flash安全配置缺陷， ")
			}else if(s.equals("Mysql")){
				sb.append("Mysql弱口令及远程溢出， ")
			}else if(s.equals("Ftp")){
				sb.append("Ftp弱口令及远程溢出库， ")
			}else if(s.equals("WordPress")){
				sb.append("WordPress漏洞库， ")
			}else if(s.equals("Webs")){
				sb.append("Web漏洞集(CMS、BBS等)， ")
			}else if(s.equals("Overflows")){
				sb.append("缓冲区溢出漏洞， ")
			}else if(s.equals("Systems")){
				sb.append("系统漏洞/网络脆弱性扫描， ")
			}
		}
		def sbstr = sb.substring(0, sb.length()-2)
		if (!urlInstance || urlInstance?.user?.id != session?.user?.id) {
			render(view:"/error")
		}else{
			render(view:'/scan/urlData',model:[urlInstance:urlInstance,bugName:sbstr])
		}
	}

	/**
	 * 调用接口
	 */
	def scanApi = {
		if(!session.user){
			render "error"
			return
		}
		def urlInstance = ScanUrl.get(params.urlId)
		//判断是否为当前用户的扫描链接
		if(!urlInstance || urlInstance.user.id != session.user.id || urlInstance.statusDo!=1){
			render "error"
			return
		}

		def theurl = urlInstance.url	//要扫描的url
		def maxConnect = 6  //每秒钟HTTP并发请求数量，该值要大于等于3
		def type = "normal"	//扫描策略，可以为normal（正常扫描）或deep（深度扫描）
		def agent = "XSpider"	//【可选参数】发送HTTP请求时添加的User-Agent
		def proxy = "***************"	//【可选参数】扫描器的代理IP
		def t = (new Date().getTime()).toString().substring(0,10)	//当前时间
		def	username = "cert"	//用户名
		def password = "AAAfafd730724c50724727ec3e084690"	//密码AAAfafd730724c50724727ec3e084690
		def cookie = ""
		def exts = urlInstance.bugName

		//------------------------
		def taskurl = "http://www.aisec.cn/aiscanner/api.php?act=add&url="+theurl+"&maxConnect="+maxConnect+"&type=normal&agent="+agent+"&username="+username+"&password="+password+"&exts="+exts+""

		def resstring = "";
		URL urltask = new URL(taskurl);
		java.net.HttpURLConnection httpurlconnec = (java.net.HttpURLConnection) urltask.openConnection();
		httpurlconnec.setDoOutput(true);
		httpurlconnec.setRequestMethod("POST");

		java.io.BufferedReader inbuf ;
		try {
			inbuf = new java.io.BufferedReader(new java.io.InputStreamReader(httpurlconnec.getInputStream()));
		} catch (Exception e) {
			e.printStackTrace()
			render "err"
			return
		}
		def readline;
		while ((readline = inbuf.readLine()) != null) {
			resstring += readline;
		}
		inbuf.close();

		//判断返回值是否为ID
		try {
			int flagint =Integer.parseInt(resstring);
		} catch (Exception e) {
			render "err"
			return
		}
		
		ScanFlag scanFlag = new ScanFlag()
		scanFlag.scanTaskId = resstring;
		scanFlag.flag = 0;
		scanFlag.dateCreated = new Date();
		if(scanFlag.save(flush:true)){//保存Scanbug
			//将任务id保存到url展示表中
			urlInstance.scanTaskId = resstring;
			
			urlInstance.status = 2;
			urlInstance.statusDo = 2;
			urlInstance.scanTime = new Date();
			urlInstance.save(flush:true)	//保存scanurl

		}

		render resstring
		return
	}

	/**
	 * 下载导出
	 */
	def downLoadResource ={

		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			redirect(controller:"TUser",action:"login")
			return
		}
		def urlId = params.'urlId'
		
		//判断是否为该用户，并且是否可下载
		def urlInstance = ScanUrl.get(urlId)
		//判断是否为当前用户的扫描链接
		if(urlInstance && urlInstance.user.id==session.user.id && urlInstance.statusDo==4){
			def idSql = "SELECT scan_task_id FROM scan_url t WHERE t.id = '"+urlId+"' "
			def scanTaskIdList = SQLUtil.getList(idSql);
			def scanTaskId = scanTaskIdList[0].scan_task_id
	
			def fileName='ID为'+scanTaskId+'的任务详细信息'+new Date().getTime()+'.csv'
			def path = '/share_file/black/file/virusAddress/'
			def outList = new ArrayList<ArrayList<String>>()
			def headList = new ArrayList<String>()
			def innerList = new ArrayList<String>()
	
			headList.add("编号")		//scanurl	number // URL编号
			headList.add("URL")		//scanurl	url	//URL
	//		headList.add("扫描漏洞任务ID")	//scaninfo	scanTaskId  扫描漏洞任务ID
			headList.add("本次任务网站是否安全")
			headList.add("本次任务要检测的全部漏洞信息")
			headList.add("提示数量")	//scaninfo表 vuls_count	漏洞数量
			headList.add("当前漏洞名称")
			headList.add("当前漏洞等级")
			headList.add("当前漏洞url")
			headList.add("推荐的展示漏洞的HTML格式")
			headList.add("漏洞危害具体说明")
			headList.add("漏洞参考信息")
			
			headList.add("漏洞危害简介")
			headList.add("修复方案")
	
			outList.add(headList)
			def sqlScanBug = "SELECT * FROM scan_bug t WHERE t.scan_task_id = '"+scanTaskId+"' AND t.user_id = '"+session.user.id+"'"
			def sqlScanInfo = "SELECT * FROM scan_info t WHERE t.scan_task_id = '"+scanTaskId+"' AND t.user_id = '"+session.user.id+"'"
			def sqlScanUrl = "SELECT * FROM scan_url t WHERE t.scan_task_id = '"+scanTaskId+"' AND t.user_id = '"+session.user.id+"'"
	
			def scanbuglist = SQLUtil.getList(sqlScanBug);
			def scaninfolist = SQLUtil.getList(sqlScanInfo);
			def scanurllist = SQLUtil.getList(sqlScanUrl);
	
			def number = scanurllist[0].number;
			def url = scanurllist[0].url;
			def level = scaninfolist[0].level;
			def vulsCount = scaninfolist[0].vuls_count;
			def bugName = scanurllist[0].bug_name;
			String[] strs = bugName.split (",");
			StringBuffer sb = new StringBuffer()
			for(String s:strs){
				if(s.equals("SqlScanner")){
					sb.append("SQL注入漏洞， ")
				}else if(s.equals("XSS")){
					sb.append("XSS漏洞， ")
				}else if(s.equals("Pollution")){
					sb.append("参数污染， ")
				}else if(s.equals("Struts")){
					sb.append("JAVA漏洞库， ")
				}else if(s.equals("Include")){
					sb.append("文件包含漏洞， ")
				}else if(s.equals("URLRedirect")){
					sb.append("URL跳转漏洞， ")
				}else if(s.equals("Nginx")){
					sb.append("Nginx任意文件执行漏洞， ")
				}else if(s.equals("SVN")){
					sb.append("SVN/CVS文件泄漏漏洞， ")
				}else if(s.equals("PaddingOracle")){
					sb.append("Padding Oracle漏洞， ")
				}else if(s.equals("DirectoryListing")){
					sb.append("目录遍历漏洞， ")
				}else if(s.equals("ApacheCGI")){
					sb.append("ApacheCGI命令执行漏洞， ")
				}else if(s.equals("DNS")){
					sb.append("DNS漏洞， ")
				}else if(s.equals("FlashCrossdomain")){
					sb.append("Flash安全配置缺陷， ")
				}else if(s.equals("Mysql")){
					sb.append("Mysql弱口令及远程溢出， ")
				}else if(s.equals("Ftp")){
					sb.append("Ftp弱口令及远程溢出库， ")
				}else if(s.equals("WordPress")){
					sb.append("WordPress漏洞库， ")
				}else if(s.equals("Webs")){
					sb.append("Web漏洞集(CMS、BBS等)， ")
				}else if(s.equals("Overflows")){
					sb.append("缓冲区溢出漏洞， ")
				}else if(s.equals("Systems")){
					sb.append("系统漏洞/网络脆弱性扫描， ")
				}
			}
			def sbstr = sb.substring(0, sb.length()-2)
	
			if(scanbuglist.size() == 0){
				innerList.add(number)
				innerList.add(url)
	//			innerList.add(scanTaskId)
				innerList.add(level)
				innerList.add(sbstr)
				innerList.add(vulsCount)
				outList.add(innerList)
				innerList=new ArrayList<String>()
	
			}else{
				for(int i=0; i<scanbuglist.size(); i++){
					innerList.add(number)
					innerList.add(url)
	//				innerList.add(scanTaskId)
					innerList.add(level)
					innerList.add(sbstr)
					innerList.add(vulsCount)
					innerList.add(scanbuglist[i].name)
					def levels = scanbuglist[i].level
					if(levels.equals("1")){
						levels = "高危"
					}else if (levels.equals("2")){
						levels = "中危"
					}else{
						levels = "低危"
					}
					innerList.add(levels)
					innerList.add(scanbuglist[i].url)
					innerList.add(scanbuglist[i].html)
					innerList.add(scanbuglist[i].bug_describe)
					innerList.add(scanbuglist[i].bug_from)
					innerList.add(scanbuglist[i].harm)
					innerList.add(scanbuglist[i].solution)
					outList.add(innerList)
					innerList=new ArrayList<String>()
				}
			}
	
			//this.downloadAtt(this.getDownLoadCSV(outList, path, fileName),request,response); 2021-09-12 暂时注释掉
			
		}else{
			render "error"
			return
		}
	}

	//  文件下载
	def getDownLoadCSV(List<List<String>> outList,String path, String fileName){
		File csvFile = null;
		BufferedWriter csvWtriter = null;
		try{
			String file= path+fileName;
			csvFile = new File(file)
			File parent = csvFile.getParentFile();
			if (parent != null && !parent.exists()) {
				parent.mkdirs();
			}
			csvFile.createNewFile();
			csvWtriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(
					csvFile), "GB2312"), 1024);
			// 写入文件内容
			for (ArrayList<ArrayList<String>> row : outList) {
				for (Object data : row) {
					StringBuffer sb = new StringBuffer();
					String rowStr = sb.append("\"\t").append(data).append("\",").toString();
					csvWtriter.write(rowStr);
				}
				csvWtriter.newLine();
			}
			csvWtriter.flush();
		}catch(Exception e){
			e.printStackTrace();
		}finally{
			csvWtriter.close();
		}

		def att=new Attachment();
		att.fileName = fileName
		att.path=path+fileName
		return att;
	}


	/* 2021-09-12 暂时注释掉
	def downloadAtt(Attachment att,HttpServletRequest request,HttpServletResponse response){
		request.setCharacterEncoding("UTF-8");
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;

		String downLoadPath = att.path;
		long fileLength = new File(downLoadPath).length();
		def browserType = request.getHeader("User-Agent")
		response.setContentType("application/octet-stream");
		byte[] bytes = browserType.contains("MSIE") ? att.fileName.getBytes() : att.fileName.getBytes("utf-8");
		response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
		response.setHeader("Content-Length", String.valueOf(fileLength));

		bis = new BufferedInputStream(new FileInputStream(downLoadPath));
		bos = new BufferedOutputStream(response.getOutputStream());
		byte[] buff = new byte[2048];
		int bytesRead;
		while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
			bos.write(buff, 0, bytesRead);
		}
		bis.close();
		bos.close();
	}*/

}
