package com.cnvd

import com.cnvd.common.Attachment
import com.cnvd.flawInfo.PocExamineHistory
import com.cnvd.productInfo.Manufacturer
import com.cnvd.productInfo.ProductCategory
import com.cnvd.productInfo.ProductInfo
import com.cnvd.utils.CommentsUtil
import com.cnvd.utils.EncryptUtils
import com.cnvd.utils.SQLUtil
import grails.converters.JSON
import org.apache.commons.lang.StringUtils

import java.text.SimpleDateFormat

class FlawPocController{

    def attachmentService
    def jcaptchaService
    def KEY = "0c4e0acf07ac97ba7edd7fb2a1936fb9"

    def create = {
        if(!session.user){
            session.originalRequestParams = [controller:controllerName,action:actionName]
            redirect(controller:"user",action:"login")
            return
        }
        return
    }

    def save = {
        withForm {
            if(!session.user){
                session.originalRequestParams = [controller:controllerName, action:actionName]
                return redirect(controller: "user", action: "login")
            }
            if(!params.findTime){
                def findTime_error = "请选择发现时间"
                render(view: "create",model: [findTime_error:findTime_error])
            }
            if(!params.manuName){
                def manuName_error = "请选择漏洞厂商"
                render(view: "create",model: [manuName_error:manuName_error])
            }
            if(!params.productCategoryName){
                def productCategoryName_error = "请选择影响产品"
                render(view: "create",model: [productCategoryName_error:productCategoryName_error])
            }
            if(!params.edition || !params.editionId){
                def edition_error = "请选择产品版本"
                render(view: "create",model: [edition_error:edition_error])
            }
            if(!params.flawLevel){
                def flawLevel_error = "请选择危害等级"
                render(view: "create",model: [flawLevel_error:flawLevel_error])
            }
            if(!params.flawSource){
                def flawSource_error = "请输入漏洞来源"
                render(view: "create",model: [flawSource_error:flawSource_error])
            }
            if(!params.flawName){
                def flawName_error = "请选择漏洞"
                render(view: "create",model: [flawName_error:flawName_error])
            }
            if(!params.searchRules){
                def searchRules_error = "请选择漏洞应用搜索规则"
                render(view: "create",model: [searchRules_error:searchRules_error])
            }
            if(!params.description){
                def description_error = "请输入POC描述"
                render(view: "create",model: [description_error:description_error])
            }
            if(!params.refLink){
                def refLink_error = "请输入参考链接"
                render(view: "create",model: [refLink_error:refLink_error])
            }
            if(!params.testUrl){
                def testUrl_error = "请输入测试URL"
                render(view: "create",model: [testUrl_error:testUrl_error])
            }
            if(!params.attachment){
                def attachment_error = "请选择poc文件"
                render(view: "create",model: [attachment_error:attachment_error])
            }
            def flawPocInstance = new FlawPoc()
            Map pathType = new HashMap()
            pathType.put(1,"互联网案例")
            pathType.put(2,"docker镜像地址")
            pathType.put(3,"自建靶场")
            pathType.put(4,"开源下载链接")
            pathType.put(5,"其他")
            def file = request.getFile("attachment")
            if(file!=null && !"".equals(file.getOriginalFilename())){
                def flag = attachmentService.checkFile(file)
                if(flag){
                    String filePath = "${grailsApplication.config.filePath.flawPocAttFilePath}"
                    String realName = CommentsUtil.getCurrentTime() //文件的真实文件名
                    def attachment = attachmentService.uploadFile(file,filePath,realName)
                    flawPocInstance.attachment = attachment  //poc文件
                }else{
                    def attachment_error = "附件类型不正确或附件大小为0,请重新上传！"
                    render(view:'create',model:[attachment_error:attachment_error])
                    return
                }
            }
            flawPocInstance.user = session.user
            String flawName = EncryptUtils.aesDecrypt(params.flawName,KEY)
            flawPocInstance.flawName = flawName //漏洞名称
            flawPocInstance.flawType = 0 //固定为通用型
            flawPocInstance.flawLevel = params.int('flawLevel') //漏洞等级 18高，19中，20低
            flawPocInstance.flawSource = params.flawSource //漏洞来源
            flawPocInstance.cnvdNumber = params.cnvdNumber //cnvd编号
            flawPocInstance.cve = params.cve //cve编号
            flawPocInstance.bid = params.bid //bid编号
            flawPocInstance.officialWebsite = params.officialWebsite ////厂商官网
            flawPocInstance.softStyleId = Integer.parseInt(params.softStyleId) //漏洞影响对象类型
            String description = EncryptUtils.aesDecrypt(params.description,KEY)
            flawPocInstance.description = description //poc描述
            flawPocInstance.formalWay = params.formalWay //官方修复建议
            flawPocInstance.refLink = params.refLink //参考链接
            flawPocInstance.ksRun = params.ksRun //运行平台
            flawPocInstance.appType = params.int('appType') //应用类型 1: 'http', 2: 'https', 3: 'other'
            flawPocInstance.appPort = params.appPort //应用端口
            if(StringUtils.isNotBlank(params.environmentPath)){  //环境路径
                flawPocInstance.environmentPath = pathType.get(params.int('pathType')) + "\t" + params.environmentPath
            }
            flawPocInstance.testUrl = params.testUrl //测试url
            flawPocInstance.pocScript = params.pocScript //poc脚本
            def format = new SimpleDateFormat("yyyy-MM-dd")
            flawPocInstance.findTime = format.parse(params.findTime)
            Date date = new Date()
            flawPocInstance.createTime = date
            flawPocInstance.updateTime = date
            flawPocInstance.isZeroDay = params.int('isZeroDay') //是否零日(1是、0否)
            flawPocInstance.searchRules = params.int('searchRules') //搜索规则(0空、1fofa搜索语句、2钟馗之眼搜索语句、3谷歌语法搜索语句)
            flawPocInstance.searchStatement = params.searchStatement //搜索语句
            try{
                if(flawPocInstance.save(flush: true)){
                    def editionIdsStr = params.editionId
                    def editionList = Arrays.asList(editionIdsStr.split(","))
                    editionList.each {
                        def flawPocProduct = new FlawPocProduct()
                        flawPocProduct.flawPoc = flawPocInstance
                        flawPocProduct.product = ProductInfo.get(Long.valueOf(it))
                        flawPocProduct.dateCreated = new Date()
                        flawPocProduct.lastUpdated = new Date()
                        if(!flawPocProduct.save(flush: true)){
                            render(view: "create",model: [flawPocInstance:flawPocInstance])
                            return
                        }
                    }
                    redirect(controller:"user",action: "flawPocList")
                }else{
                    flawPocInstance.errors.each {
                        println(it)
                    }
                    render(view: "create",model: [flawPocInstance:flawPocInstance])
                }
            }catch(Exception e){
                e.printStackTrace()
            }
        }.invalidToken {
            render(view:"/error")
        }
    }
    /**
     * 获取评分
     */
    def score = {
        if(!session.user){
            render "error"
        }
        def flawPoc = FlawPoc.findById(params.flawPocId);
//        def flawPoc1 = new FlawPoc()
//        flawPoc1.id = flawPoc.id
//        def pocSorce = PocScore.findByFlawPoc(flawPoc)
        String sql = "select * from poc_score where flaw_poc_id = ?";
        def list = new ArrayList<Object>()
        if (params.flawPocId) {
            list.add(params.flawPocId)
        }
//        def pocSorce = PocScore.find(sql,list.get(0));
//        def pocSorce = PocScore.find(sql);
       def pocSorces = SQLUtil.getResult(sql,list)

        def pocSorce = null;
        if (pocSorces.size()>0) {
            for (int i = 0; i < pocSorces.size(); i++) {
                pocSorce = pocSorces.get(i)
            }
        }

        ["flawPoc":flawPoc,"pocSorce":pocSorce]
    }

    def statusDetail = {
        if(!session.user){
            render "error"
        }
//        String sql = "select fp.id as id,peh.examine_node as examineNode,peh.examine_option as examineOption,peh.examine_time as examineTime from flaw_poc fp ,poc_examine_history peh where fp.id = peh.flaw_poc_id and \n" +
//                "fp.id = ? "
//        def paramList = new ArrayList<Object>()
//        if (params.flawPocId) {
//            paramList.add(params.flawPocId)
//        }

//        List<Map<String, Object>> resultList = SQLUtil.getResult(sql,paramList)
        def flawPoc = FlawPoc.findById(params.flawPocId)
        def pocExamineList = PocExamineHistory.findAllByFlawPoc(flawPoc)
        println "pocExamineList的值为:"+pocExamineList
        println "flawPoc的值为:"+flawPoc
        ["flawPoc":flawPoc,"pocExamineList":pocExamineList]
    }

    def edit = {
        if(!session.user) {
            session.originalRequestParams = [controller:controllerName, action:actionName]
            return redirect(controller: "user", action: "login")
        }
        def flawPocInstance = FlawPoc.get(params.long('id'))
        if(!flawPocInstance || session.user?.id == new Long(params.id)){
            render(view:"/error")
            return
        }
        if(flawPocInstance?.status != -1){
            flash.operationErr = "操作错误,请重新操作"
            redirect(controller:"user",action:"flawPocList")
            return
        }
        return [flawPocInstance:flawPocInstance]
    }

    def download = {
        if(!session.user) {
            return redirect(controller: "user", action: "login")
        }
        def attachment = Attachment.get(params.long('id'))
        //2021-09-12  待后期完善
        //attachmentService.downloadAtt(attachment,request,response)
    }

    def deleteAttachment = {
        if(!session.user) {
            return redirect(controller: "user", action: "login")
        }
        def flawPocInstance = FlawPoc.get(params.long('flawPocId'))
        println(flawPocInstance)
        if(flawPocInstance && session.user?.id == flawPocInstance.user.id && flawPocInstance?.status == 2){
            flawPocInstance.attachment = null
            flawPocInstance.save(flush: true)
            render(text:"success",contentType:"text/plain",encoding:"UTF-8")
        }else{
            render(view:"/error")
            return
        }
    }

    def update = {
        if(!session.user) {
            session.originalRequestParams = [controller:controllerName, action:actionName]
            return redirect(controller: "user", action: "login")
        }
        def flawPocInstance = FlawPoc.get(params.long('id'))
        if(!flawPocInstance || flawPocInstance.status != -1){
            render(view:"/error")
            return
        }
        if(!params.findTime){
            def findTime_error = "发现时间不能为空"
            render(view:"edit",model:[findTime_error:findTime_error,flawPocInstance: flawPocInstance])
            return
        }
        if(!params.manuName){
            def manuName_error = "厂商不能为空"
            render(view:"edit",model:[manuName_error:manuName_error,flawPocInstance: flawPocInstance])
            return
        }
        if(!params.productCategoryName){
            def productCategoryName_error = "产品不能为空"
            render(view:"edit",model:[productCategoryName_error:productCategoryName_error,flawPocInstance: flawPocInstance])
            return
        }
        if(!params.edition){
            def edition_error = "版本不能为空"
            render(view:"edit",model:[edition_error:edition_error,flawPocInstance: flawPocInstance])
            return
        }
        if(!params.softStyleId){
            def softStyleId_error = "影响对象类型不能为空"
            render(view:"edit",model:[softStyleId_error:softStyleId_error,flawPocInstance: flawPocInstance])
            return
        }
        if(!params.flawName){
            def flawName_error = "漏洞名称不能为空"
            render(view:"edit",model:[flawName_error:flawName_error,flawPocInstance: flawPocInstance])
            return
        }
        if(!params.flawLevel){
            def flawLevel_error = "漏洞危险等级不能为空"
            render(view:"edit",model:[flawLevel_error:flawLevel_error,flawPocInstance: flawPocInstance])
            return
        }
        if(!params.flawSource){
            def flawSource_error = "漏洞来源不能为空"
            render(view:"edit",model:[flawSource_error:flawSource_error,flawPocInstance: flawPocInstance])
            return
        }
        if(!params.searchRules){
            def searchRules_error = "搜索规则不能为空"
            render(view:"edit",model:[searchRules_error:searchRules_error,flawPocInstance: flawPocInstance])
            return
        }
        if(!params.descriptions){
            def description_error = "POC描述不能为空"
            render(view:"edit",model:[description_error:description_error,flawPocInstance: flawPocInstance])
            return
        }
        if(!params.refLink){
            def refLink_error = "参考链接不能为空"
            render(view:"edit",model:[refLink_error:refLink_error,flawPocInstance: flawPocInstance])
            return
        }
        if(!params.testUrl){
            def testUrl_error = "测试URL不能为空"
            render(view:"edit",model:[testUrl_error:testUrl_error,flawPocInstance: flawPocInstance])
            return
        }
        if (params.myCode.toLowerCase()!=session.validateCode){
            def manuName_error = "验证码不正确"
            render(view:"edit",model:[manuName_error:manuName_error,flawPocInstance: flawPocInstance])
            return
        }
        def file = request.getFile("attachment")
        if(file!=null && !"".equals(file.getOriginalFilename())){
            def flag = attachmentService.checkFile(file)
            if(flag){
                //附件符合格式及大小限制
                String filePath = "${grailsApplication.config.filePath.flawPocAttFilePath}"
                String realName = CommentsUtil.getCurrentTime() //文件的真实文件名
                def attachment = attachmentService.uploadFile(file,filePath,realName)
                flawPocInstance.attachment = attachment
            }else{
                //附件不符合格式或大小限制
                def fileLimit_error = "附件类型不正确或大小超出限制"
                return render(view:'edit',model:[flawPocInstance:flawPocInstance,fileLimit_error:fileLimit_error])
            }
        }
        Map pathType = new HashMap()
        pathType.put(1,"互联网案例")
        pathType.put(2,"docker镜像地址")
        pathType.put(3,"自建靶场")
        pathType.put(4,"开源下载链接")
        pathType.put(5,"其他")
        String findTimeStr = params.findTime
        flawPocInstance.findTime = new SimpleDateFormat("yyyy-MM-dd").parse(findTimeStr)
        flawPocInstance.officialWebsite = params.officialWebsite
        flawPocInstance.softStyleId = params.int('softStyleId')
        flawPocInstance.flawSource = params.flawSource
        flawPocInstance.status = 1 //一级审核
        flawPocInstance.flawName = EncryptUtils.aesDecrypt(params.flawName,KEY)
        flawPocInstance.flawType = 0 //固定为通用型
        flawPocInstance.flawLevel = params.int('flawLevel')
        flawPocInstance.cnvdNumber = params.cnvdNumber
        flawPocInstance.cve = params.cve
        flawPocInstance.bid = params.bid
        String description = EncryptUtils.aesDecrypt(params.description,KEY)
        flawPocInstance.description = description
        flawPocInstance.formalWay = params.formalWay
        flawPocInstance.refLink = params.refLink
        flawPocInstance.ksRun = params.ksRun
        flawPocInstance.appType = params.int('appType')
        flawPocInstance.appPort = params.appPort
        flawPocInstance.environmentPath = params.environmentPath
        flawPocInstance.testUrl = params.testUrl
        flawPocInstance.pocScript = params.pocScript
        flawPocInstance.isZeroDay = params.int('isZeroDay')
        flawPocInstance.searchRules = params.int('searchRules')
        flawPocInstance.searchStatement = params.searchStatement
        flawPocInstance.updateTime = new Date()
        if(flawPocInstance.save(flush: true)){
            String sql = "DELETE FROM flaw_poc_product WHERE flaw_poc_id = " + flawPocInstance.id
            SQLUtil.execute(sql)
            def editionIdsStr = params.editionId
            def editionList = Arrays.asList(editionIdsStr.split(","))
            editionList.each {
                def flawPocProduct = new FlawPocProduct()
                flawPocProduct.flawPoc = flawPocInstance
                flawPocProduct.product = ProductInfo.get(Long.valueOf(it))
                flawPocProduct.dateCreated = new Date()
                flawPocProduct.lastUpdated = new Date()
                if(!flawPocProduct.save(flush: true)){
                    render(view: "edit",model: [flawPocInstance:flawPocInstance])
                    return
                }
            }
            return redirect(controller:"user",action: "flawPocList")
        }else{
            flawPocInstance.errors.allErrors.each {
                println(it)
            }
            render(view: "edit",model: [flawPocInstance:flawPocInstance])
        }
    }

    def show = {

        def flawPocInstance = FlawPoc.get(params.long('id'))
        if(!flawPocInstance || flawPocInstance.status == -1){
            render(view:"/error")
        }
//        render(view: "flawPocList",model:[flawPocInstance:flawPocInstance] )
        [flawPocInstance:flawPocInstance]
    }

    def flawPocDetail = {
//        println "flawPocId的值为"+params.flawPocId
//       def flawPoc = FlawPoc.findById(params.flawPocId);
        def flawPoc = FlawPoc.get(params.flawPocId)
        [flawPocInstance:flawPoc]
    }

    def synchronization = {
        if(!session.user) {
            session.originalRequestParams = [controller:controllerName, action:actionName]
            return redirect(controller: "user", action: "login")
        }
        String sql = "SELECT m.id as '厂商id',pi.id as '产品版本id',pc.id as '产品id',m.`name` as '厂商名称',pc.`name` as '产品名称',pi.`name` as '版本号' \n" +
                "FROM flaw_poc_product fp LEFT JOIN product_info pi ON pi.id = fp.product_id LEFT JOIN manufacturer m ON m.id = pi.manufacturer_id\n" +
                "LEFT JOIN product_category pc ON pc.id = pi.product_category_id\n" +
                "WHERE fp.flaw_poc_id = ?"
        def list = new ArrayList<Object>()
        List<Map<String, Object>> dataResult = null
        list.add(params.long('id'))
        dataResult = SQLUtil.getResult(sql,list)
        println "dataresult的值为:"+dataResult
        def listResult = ["dataResult": dataResult]
        render listResult as JSON
    }
}
