package com.cnvd.common

import com.cnvd.flawInfo.Exploit
import com.cnvd.flawInfo.ExploitAttachment
import com.cnvd.patchInfo.PatchInfo
import com.cnvd.patchInfo.PatchInfoAttachment
import com.cnvd.utils.Constants
import com.cnvd.utils.EncryptUtils
import org.apache.commons.lang.StringUtils
import org.apache.commons.lang.math.NumberUtils
import javax.imageio.ImageIO;
import java.awt.*
import java.awt.image.BufferedImage

class CommonController {
	public static final String VALIDATE_CODE = "validateCode";

	private int w = 80;
	private int h = 35;
	def attachmentService
	def downloadPdf = {
        println "params.cd="+params.cd
        println "params.type="+params.type
        println "params.id="+params.id

        if(params.int('type') == Constants.FLAW_EXPOIT){
            //如果session不存在，直接返回错误页面
            if (!session?.user){
                render(view:"/error")
                return
            }
            def exploit = Exploit.findByDownCode(params.cd)
            def task = Task.findByExploit(exploit)
            //验证任务附件不公开，如果验证任务不存在，这直接返回错误页面
            if(!exploit||!task){
                println "AAAAAAAAAAAAAAAAAA"
                render(view:"/error")
                return
            }
            //验证任务附件不公开，验证任务管理的前台用户如果和session用户不一致，返回错误页面
			def nickName = session?.user?.nickName?session?.user?.nickName:""
            if (task?.targetTUser?.id!=session?.user?.id&&!nickName.endsWith('分中心')){
                render(view:"/error")
                return
            }
            def attId = EncryptUtils.aesDecrypt(params.id)
            def att = AttachmentPdf.get(Long.parseLong(attId))
            attachmentService.downloadAttPdf(att,request,response)
        }else if(params.int('type') == Constants.FLAW_PATCH){
            def patchInfo = PatchInfo.findByDownCode(params.cd)
            if(!patchInfo){
                render(view:"/error")
                return
            }
            def flaw = patchInfo.flaw
            //判断session用户是否存在
            if (session?.user){
                //存在
                def task = Task.findByPatchInfo(patchInfo)
                def flag= false
                if (task){
					def nickName = session?.user?.nickName?session?.user?.nickName:""
                    if (task?.targetTUser?.id==session?.user?.id&&!nickName.endsWith('分中心')){
                        flag= true
                    }
                }
                if ((flaw.status==9&&flaw.enable==1&&flaw.isOpen==1&&flaw.openTime<new Date()&&flaw.parentFlaw==null&&'3'.equals(patchInfo.status))||(flag)){
                    def attId = EncryptUtils.aesDecrypt(params.id)
                    def att = AttachmentPdf.get(Long.parseLong(attId))
                    attachmentService.downloadAttPdf(att,request,response)
                }else{
                    render(view:"/error")
                    return
                }
            }else{
                //不存在
                if (flaw.status==9&&flaw.enable==1&&flaw.isOpen==1&&flaw.openTime<new Date()&&flaw.parentFlaw==null&&'3'.equals(patchInfo.status)){
                    def attId = EncryptUtils.aesDecrypt(params.id)
                    def att = AttachmentPdf.get(Long.parseLong(attId))
                    attachmentService.downloadAttPdf(att,request,response)
                }else{
                    render(view:"/error")
                    return
                }
            }
        }else{
            println "CCCCCCCCCCCCCCCCC"
            render(view:"/error")
            return
        }
	}

	def flawDownloadPdf = {
		if (!session.user) {
			session.originalRequestParams = [controller: controllerName, action: actionName]
			redirect(controller: "TUser", action: "login")
			return
		}
		def attId = params.id
		def att = AttachmentPdf.get(params.id)
		attachmentService.downloadAttPdf(att,request,response)
	}

	def downSbxz = {
		try{
			request.setCharacterEncoding("UTF-8");
			BufferedInputStream bis = null;
			BufferedOutputStream bos = null;

			String downLoadPath = "/cnvd/bug_report.xls";
			long fileLength = new File(downLoadPath).length();

			response.setContentType("application/octet-stream");
			response.setHeader("Content-disposition", "attachment; filename="+ new String("bug_report.xls".getBytes("utf-8"), "ISO8859-1"));
			response.setHeader("Content-Length", String.valueOf(fileLength));

			bis = new BufferedInputStream(new FileInputStream(downLoadPath));
			bos = new BufferedOutputStream(response.getOutputStream());
			byte[] buff = new byte[2048];
			int bytesRead;
			while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
				bos.write(buff, 0, bytesRead);
			}

		}catch(Exception e){
			e.printStackTrace()
		}finally{
			bis.close();
			bos.close();
		}
	}

	def myCodeNew = {
		OutputStream out=null;
		try{
		response.setHeader("Pragma", "no-cache");
		response.setHeader("Cache-Control", "no-cache");
		response.setDateHeader("Expires", 0);
		response.setContentType("image/jpeg");

		/*
		 * 得到参数高，宽，都为数字时，则使用设置高宽，否则使用默认值
		 */
		String width = request.getParameter("width");
		String height = request.getParameter("height");
		if (StringUtils.isNumeric(width) && StringUtils.isNumeric(height)) {
			w = NumberUtils.toInt(width);
			h = NumberUtils.toInt(height);
			//防御dos攻击，限制长宽大小
			if (w >= 200){
				w = 200;
			}
			if (h >= 200){
				h = 200;
			}
		}

		BufferedImage image = new BufferedImage(w, h, BufferedImage.TYPE_INT_RGB);
		Graphics g = image.getGraphics();

		/*
		 * 生成背景
		 */
		createBackground(g);

		/*
		 * 生成字符
		 */
		String s = createCharacter(g);

		request.getSession().setAttribute(VALIDATE_CODE, s.toLowerCase());
		request.getSession().setAttribute(s.toLowerCase(), System.currentTimeMillis());

		g.dispose();
		out = response.getOutputStream();
		ImageIO.write(image, "JPEG", out);
		}catch(Exception e){
			e.printStackTrace()
		}finally{
			if(out != null){
				out.close();
			}
		}
	}

	private Color getRandColor(int fc,int bc) {
		int f = fc;
		int b = bc;
		Random random=new Random();
		if(f>255) {
			f=255;
		}
		if(b>255) {
			b=255;
		}
		return new Color(f+random.nextInt(b-f),f+random.nextInt(b-f),f+random.nextInt(b-f));
	}

	private void createBackground(Graphics g) {
		// 填充背景
		g.setColor(getRandColor(220,250));
		g.fillRect(0, 0, w, h);
		// 加入干扰线条
		for (int i = 0; i < 8; i++) {
			g.setColor(getRandColor(40,150));
			Random random = new Random();
			int x = random.nextInt(w);
			int y = random.nextInt(h);
			int x1 = random.nextInt(w);
			int y1 = random.nextInt(h);
			g.drawLine(x, y, x1, y1);
		}
	}

	private String createCharacter(Graphics g) {
		char[] codeSeq = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J',
			'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W',
			'X', 'Y', 'Z','a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'j',
			'k', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',
			'x', 'y', 'z','2', '3', '4', '5', '6', '7', '8', '9' ];
		String[] fontTypes = ["\u5b8b\u4f53","\u65b0\u5b8b\u4f53","\u9ed1\u4f53","\u6977\u4f53","\u96b6\u4e66"];
		Random random = new Random();
		StringBuilder s = new StringBuilder();
		for (int i = 0; i < 4; i++) {
			String r = String.valueOf(codeSeq[random.nextInt(codeSeq.length)]);//random.nextInt(10));
			g.setColor(new Color(50 + random.nextInt(100), 50 + random.nextInt(100), 50 + random.nextInt(100)));
			g.setFont(new Font(fontTypes[random.nextInt(fontTypes.length)],Font.BOLD,26));
			g.drawString(r, 15 * i + 5, 19 + random.nextInt(8));
//			g.drawString(r, i*w/4, h-5);
			s.append(r);
		}
		return s.toString();
	}


	
	def downMould = {
		request.setCharacterEncoding("UTF-8");
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;
 
		String downLoadPath = "/cnvd/batchFlawReportMould.xls";
		long fileLength = new File(downLoadPath).length();
 
		response.setContentType("application/octet-stream");
		response.setHeader("Content-disposition", "attachment; filename="+ new String("漏洞报送模板.xls".getBytes("utf-8"), "ISO8859-1"));
		response.setHeader("Content-Length", String.valueOf(fileLength));
 
		bis = new BufferedInputStream(new FileInputStream(downLoadPath));
		bos = new BufferedOutputStream(response.getOutputStream());
		byte[] buff = new byte[2048];
		int bytesRead;
		while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
			bos.write(buff, 0, bytesRead);
		}
		bis.close();
		bos.close();
	}
	def download = {
		println "params.cd="+params.cd
		println "params.type="+params.type
		println "params.id="+params.id
		
		if(params.int('type') == Constants.FLAW_EXPOIT){
			//如果session不存在，直接返回错误页面
			if (!session?.user){
				render(view:"/error")
				return
			}
			def exploit = Exploit.findByDownCode(params.cd)
			def task = Task.findByExploit(exploit)
			//验证任务附件不公开，如果验证任务不存在，这直接返回错误页面
			if(!exploit||!task){
				render(view:"/error")
				return
			}
			//验证任务附件不公开，验证任务管理的前台用户如果和session用户不一致，返回错误页面
			def nickName = session?.user?.nickName?session?.user?.nickName:""
			if (!nickName.endsWith('分中心')&&task?.targetTUser?.id!=session?.user?.id){
				render(view:"/error")
				return
			}
			def attachment = Attachment.get(Long.parseLong(EncryptUtils.aesDecrypt(params.id+"")))
			if(ExploitAttachment.findByExploitAndAttachment(exploit,attachment)){
				attachmentService.downloadAttFile(attachment,request,response)
			}else{
				render(view:"/error")
				return
			}
		}else if(params.int('type') == Constants.FLAW_PATCH){
			def patchInfo = PatchInfo.findByDownCode(params.cd)
			if(!patchInfo){
				render(view:"/error")
				return
			}

			def flaw = patchInfo.flaw

            //判断session用户是否存在
			if (session?.user){
                //存在
                def task = Task.findByPatchInfo(patchInfo)
                def flag= false
                if (task){
					def nickName = session?.user?.nickName?session?.user?.nickName:""
                    if (task?.targetTUser?.id==session?.user?.id&&!nickName.endsWith('分中心')){
                        flag= true
                    }
                }
				if ((flaw.status==9&&flaw.enable==1&&flaw.isOpen==1&&flaw.openTime<new Date()&&flaw.parentFlaw==null&&'3'.equals(patchInfo.status))||(flag)){
					def attachment = Attachment.get(Long.parseLong(EncryptUtils.aesDecrypt(params.id+"")))
					if(PatchInfoAttachment.findByPatchInfoAndAttachment(patchInfo,attachment)){
						attachmentService.downloadAttFile(attachment,request,response)
					}else{
						render(view:"/error")
						return
					}
				}else{
					render(view:"/error")
					return
				}
			}else{
            //不存在
					if (flaw.status==9&&flaw.enable==1&&flaw.isOpen==1&&flaw.openTime<new Date()&&flaw.parentFlaw==null&&'3'.equals(patchInfo.status)){
						def attachment = Attachment.get(Long.parseLong(EncryptUtils.aesDecrypt(params.id+"")))
						if(PatchInfoAttachment.findByPatchInfoAndAttachment(patchInfo,attachment)){
							attachmentService.downloadAttFile(attachment,request,response)
						}else{
							render(view:"/error")
							return
						}
					}else{
						render(view:"/error")
						return
					}
				}

		}else{
			render(view:"/error")
			return
		}
	}
}
