package com.cnvd.api

import com.cnvd.utils.SQLUtil
//添加
import com.cnvd.utils.DateUtil

class SyncFlawInfoUtil {
	
	public String getSyncStr(String startTime, String endTime){
		long start = System.currentTimeMillis();
		String syncStr = "<?xml version=\"1.0\" encoding=\"utf-8\"?>";
		
		String addedFlawStr = this.getAddedFlawStr(startTime, endTime);
		syncStr += "<info>"
		syncStr += addedFlawStr;
		syncStr += "</info>";
		println "cost time="+(System.currentTimeMillis() - start);
		return syncStr;
	}
	
	public String wrapXmlContent(String content){
		StringBuffer appender = new StringBuffer("");
		if ((content != null) && (!content.trim().isEmpty())) {
			appender = new StringBuffer(content.length());
		for (int i = 0; i < content.length(); i++) {
			char ch = content.charAt(i);
			if ((ch == '\t') || (ch == '\n') || (ch == '\r') ||
				((ch >= ' ') && (ch <= 55295)) || ((ch >= 57344) && (ch <= 65533)) || ((ch >= 65536) && (ch <= 1114111))) {
					appender.append(ch);
				}
			}
		}
		String result = appender.toString();
		return "<![CDATA[" + result.replaceAll("]]>", "]]<") + "]]>";
	}
	
	public String getAddedFlawStr(String startTime, String endTime){
		String flawStr = "<addedFlaw>";
		String flawQuerySql = "select fw.id,"+
				"fw.title,fw.number,fw.is_first,fw.is_zero,"+
				"fw.cause_id,fw.thread_id,fw.serverity_id,fw.position_id,"+
				"fw.soft_style_id,fw.is_original,"+
				"(select number from flaw f where f.id = fw.parent_flaw_id) parentFlawNumber,"+
				"date_format(fw.date_created,'%Y-%m-%d %H:%i:%s') dateCreated,"+
				"date_format(fw.submit_time,'%Y-%m-%d %H:%i:%s') submitTime,"+
				"date_format(fw.storage_time, '%Y-%m-%d %H:%i:%s') storageTime," +
				"date_format(fw.found_time, '%Y-%m-%d %H:%i:%s') foundTime,fw.reference_link,"+
				"fw.is_event,fw.detailed_info_id,"+
				"attachment_id,fw.basemetric_id,fw.environmental_metric_id,fw.temporal_metric_id," +
				"ifnull((select cast(group_concat(product_id) as char) from flaw_product fp where fp.flaw_id = fw.id), '') reflectProductId,"+
				"(select fk.keyword_str from flaw_keyword fk where fk.flaw_id = fw.id) splitWordStr "+
				"from flaw fw ";
		//String whereSql = "where status =9 and enable = 1 and is_open = 1";
		//修改where条件，与页面查询保持一致
		String whereSql = "where open_time <'"+ DateUtil.getTomorrowStrDefault()+"' and status =9 and enable = 1 and is_open = 1 AND parent_flaw_id is null";
		if(startTime != null && startTime.length() > 0){
			whereSql += " and storage_time >= '"+startTime+"' ";
		}
		if(endTime != null && endTime.length() > 0){
			whereSql += " and storage_time < '"+endTime+"' ";
		}
		flawQuerySql += whereSql;
		
		def res = SQLUtil.getResult(flawQuerySql);
		println "getAddedFlawStr.size="+res.size();
		
		String subFlawStr = "";
		res.each{
			String flawId = it.getAt("id");
			String title = it.getAt("title");
			String number = it.getAt("number");
			String isFirst = it.getAt("is_first");
			String isZero = it.getAt("is_zero");
			String causeId = it.getAt("cause_id");
			String threadId = it.getAt("thread_id");
			String serverityId = it.getAt("serverity_id");
			String positionId = it.getAt("position_id");
			String softStyleId = it.getAt("soft_style_id");
			String isOriginal = it.getAt("is_original");
			String parentFlawNumber = it.getAt("parentFlawNumber");
			String dateCreated = it.getAt("dateCreated");
			String submitTime = it.getAt("submitTime");
			String storageTime = it.getAt("storageTime");
			storageTime = storageTime == null ? "": storageTime;
			String foundTime = it.getAt("foundTime");
			String referenceLink = it.getAt("reference_link");
			String isEvent = it.getAt("is_event");
			String attachmentId = it.getAt("attachment_id");
			String baseMetricId = it.getAt("basemetric_id");
			String environMentalMetricId = it.getAt("environmental_metric_id");
			String temporalMetricId = it.getAt("temporal_metric_id");
			String reflectProductIdStr = it.getAt("reflectProductId");
			String detailedInfoId = it.getAt("detailed_info_id");
			String detailInfoStr = "";
			if(detailedInfoId != null && detailedInfoId.length() > 0){
				detailInfoStr = getDetailInfo(detailedInfoId);
			}
			String exploitStr = getExploitInfo(flawId);
			String patchInfoStr = getPatchInfo(flawId);
			
			String baseMetricStr = "";
			if(baseMetricId != null && baseMetricId.length() > 0){
				baseMetricStr = getBaseMetricStr(baseMetricId);
			}
			String environMentalMetricStr = "";
			if(environMentalMetricId != null && environMentalMetricId.length() > 0){
				environMentalMetricStr = getEnvironMentalMetricStr(environMentalMetricId);
			}
			String temporalMetricStr = "";
			if(temporalMetricId != null && temporalMetricId.length() > 0){
				temporalMetricStr = getTemporalMetricStr(temporalMetricId);
			}
			String attachmentStr = "";
			if(attachmentId != null && attachmentId.length() > 0){
				attachmentStr = getAttachmentStr(attachmentId);
			}
			String referenceInfoStr = getReferenceInfoList(flawId);
			String reflectProductStr = "";
			if(reflectProductIdStr != null && reflectProductIdStr.length() > 0){
				reflectProductStr = getReflectProductStr(reflectProductIdStr);
			}
			String splitWordStr = it.getAt("splitWordStr");
			String industryKeywordsInfo = getIndustryKeywordsInfo(flawId);
			String industryProductInfo = getIndustryProductInfo(flawId);
			String flawInfoStr = "<info>";
			flawInfoStr += "<title>"+wrapXmlContent(title)+"</title>"+
				"<number>"+wrapXmlContent(number)+"</number>"+
				"<is_first>"+wrapXmlContent(isFirst)+"</is_first>"+
				"<is_zero>"+wrapXmlContent(isZero)+"</is_zero>"+
				"<cause_id>"+wrapXmlContent(causeId)+"</cause_id>"+
				"<thread_id>"+wrapXmlContent(threadId)+"</thread_id>"+
				"<serverity_id>"+wrapXmlContent(serverityId)+"</serverity_id>"+
				"<position_id>"+wrapXmlContent(positionId)+"</position_id>"+
				"<soft_style_id>"+wrapXmlContent(softStyleId)+"</soft_style_id>"+
				"<is_original>"+wrapXmlContent(isOriginal)+"</is_original>"+
				"<date_created>"+wrapXmlContent(dateCreated)+"</date_created>"+
				"<submit_time>"+wrapXmlContent(submitTime)+"</submit_time>"+
				"<storage_time>"+wrapXmlContent(storageTime)+"</storage_time>"+
				"<found_time>"+wrapXmlContent(foundTime)+"</found_time>"+
				"<reference_link>"+wrapXmlContent(referenceLink)+"</reference_link>"+
				"<is_event>"+wrapXmlContent(isEvent)+"</is_event>"+
				reflectProductStr+
				detailInfoStr+attachmentStr+exploitStr+
				patchInfoStr+baseMetricStr+environMentalMetricStr+
				temporalMetricStr+referenceInfoStr+industryKeywordsInfo+industryProductInfo;
			if(splitWordStr != null && splitWordStr.length() > 0){
				flawInfoStr += "<splitKeywords>"+wrapXmlContent(splitWordStr)+"</splitKeywords>";
			}
			if(parentFlawNumber != null && parentFlawNumber.length() > 0){
				flawInfoStr += "<parent_flaw_number>"+wrapXmlContent(parentFlawNumber)+"</parent_flaw_number>";
			}
			flawInfoStr += "</info>";
			subFlawStr += flawInfoStr;
		}
		
		flawStr += subFlawStr;
		flawStr += "</addedFlaw>";
		return flawStr;
	}
	
	public String getReflectProductStr(String reflectProductIdStr){
		String reflectProductStr = "";
		
		String subStr = "";
		String[] productIdArr = reflectProductIdStr.split(",");
		for(String productId : productIdArr){
			String sql = "select mf.name mfName,date_format(mf.date_created, '%Y-%m-%d %H:%i:%s') mfDt,mf.description mfDesc,"+
				"pc.name pcName,date_format(pc.date_created, '%Y-%m-%d %H:%i:%s') pcDt,pc.description pcDesc,"+
				"date_format(pinfo.date_created, '%Y-%m-%d %H:%i:%s') pinfoDt,"+
				"pinfo.name pinfoName,pinfo.edition pinfoEdition,pinfo.description pinfoDesc "+
				"from product_info pinfo,product_category pc,manufacturer mf "+
				"where pinfo.product_category_id = pc.id and pc.manufacturer_id = mf.id "+
				"and pinfo.id = "+productId;
			def res = SQLUtil.getResult(sql);
			String productStr = "<info>";
			res.each{
				String manuName = it.getAt("mfName");
				String manuDesc = it.getAt("mfDesc");
				String pcName = it.getAt("pcName");
				String pcDesc = it.getAt("pcDesc");
				String pinfoName = it.getAt("pinfoName");
				String pinfoEdition = it.getAt("pinfoEdition");
				String pinfoDesc = it.getAt("pinfoDesc");
				productStr += "<manufacturer>"+
								"<name>"+wrapXmlContent(manuName)+"</name>"+
								"<description>"+wrapXmlContent(manuDesc)+"</description>"+
							"</manufacturer>"+
							"<product_category>"+
								"<name>"+wrapXmlContent(pcName)+"</name>"+
								"<description>"+wrapXmlContent(pcDesc)+"</description>"+
							"</product_category>"+
							"<product_info>"+
								"<description>"+wrapXmlContent(pinfoDesc)+"</description>"+
								"<edition>"+wrapXmlContent(pinfoEdition)+"</edition>"+
								"<name>"+wrapXmlContent(pinfoName)+"</name>"+
							"</product_info>";
			}
			productStr += "</info>";
			subStr += productStr;
		}
		if(subStr.length() > 0){
			reflectProductStr += "<reflectProduct>";
			reflectProductStr += subStr;
			reflectProductStr += "</reflectProduct>";
		}
		
		return reflectProductStr;
	}
	
	public String getAttachmentStr(String attachmentId){
		String attachmentStr = "";
		
		String sql = "select file_name, file_size, file_type, path,real_name from attachment where id = "+attachmentId;
		def res = SQLUtil.getResult(sql);
		res.each{
			String fileName = it.getAt("file_name");
			String fileSize = it.getAt("file_size");
			String fileType = it.getAt("file_type");
			String path = it.getAt("path");
			String realName = it.getAt("real_name");
			attachmentStr += "<attachment>"+
						"<file_name>"+wrapXmlContent(fileName)+"</file_name>" +
						"<file_size>"+wrapXmlContent(fileSize)+"</file_size>" +
						"<file_type>"+wrapXmlContent(fileType)+"</file_type>" +
						"<path>"+wrapXmlContent(path)+"</path>" +
						"<real_name>"+wrapXmlContent(realName)+"</real_name>" +
					"</attachment>";
		}
		
		return attachmentStr;
	}
	
	public String getExploitInfo(String flawId){
		String exploitStr = "";
		
		String sql = "select attachment_id,date_format(date_created, '%Y-%m-%d %H:%i:%s') dt," +
				"exploit_name,concept,poc,suggestion,reference_link," +
				"date_format(exploit_time, '%Y-%m-%d %H:%i:%s') exploitTime " +
				"from exploit where flaw_id = "+flawId+
				" order by date_created limit 1";
		def res = SQLUtil.getResult(sql);
			
		res.each{
			String attId = it.getAt("attachment_id");
			String exploitName = it.getAt("exploit_name");
			String concept = it.getAt("concept");
			String poc = it.getAt("poc");
			String suggestion = it.getAt("suggestion");
			String exploitTime = it.getAt("exploitTime");
			String referenceLink = it.getAt("reference_link");
			exploitStr += "<exploit>" +
						"<exploit_name>"+wrapXmlContent(exploitName)+"</exploit_name>" +
						"<concept>"+wrapXmlContent(concept)+"</concept>" +
						"<poc>"+wrapXmlContent(poc)+"</poc>" +
						"<suggestion>"+wrapXmlContent(suggestion)+"</suggestion>" +
						"<exploit_time>"+wrapXmlContent(exploitTime)+"</exploit_time>" +
						"<reference_link>"+wrapXmlContent(referenceLink)+"</reference_link>";
			if(attId != null && attId.length() > 0){
				exploitStr += getAttachmentStr(attId);
			}
			exploitStr += "</exploit>";
		}
		
		return exploitStr;
	}
	
	public String getPatchInfo(String flawId){
		String patchInfoStr = "";
		String sql = "select attachment_id,date_format(date_created, '%Y-%m-%d %H:%i:%s') dt," +
				"patch_name,patch_description,patch_url,function from patch_info where flaw_id = "+flawId+
				" order by date_created limit 1";
		def res = SQLUtil.getResult(sql);
		
		res.each{
			String attId = it.getAt("attachment_id");
			String patchName = it.getAt("patch_name");
			String desc = it.getAt("patch_description");
			String patchUrl = it.getAt("patch_url");
			String function = it.getAt("function");
			patchInfoStr += "<patch_info>" +
						"<patch_name>"+wrapXmlContent(patchName)+"</patch_name>" +
						"<description>"+wrapXmlContent(desc)+"</description>" +
						"<patch_url>"+wrapXmlContent(patchUrl)+"</patch_url>" +
						"<function>"+wrapXmlContent(function)+"</function>";
			if(attId != null && attId.length() > 0){
				patchInfoStr += getAttachmentStr(attId);
			}
			patchInfoStr += "</patch_info>";
		}
		
		return patchInfoStr;
	}
	
	/**
	 * 根据漏洞id获取所影响行业信息
	 * @param flawId
	 * @return
	 */
	public String getIndustryKeywordsInfo(String flawId){
		String str = "";
		String sql = "select itry.name industryName,"+
			"(select GROUP_CONCAT(word SEPARATOR '|') from key_word where id in(select keyword_id from flaw_industry_keyword fik where fik.flaw_id = iflaw.flaw_id and industry_id = iflaw.industry_id)) keywords "+
			"from industry_flaw iflaw,industry itry "+
			"where type = 1 and iflaw.industry_id = itry.id "+
			"and flaw_id = "+flawId;
		def res = SQLUtil.getResult(sql);
		String subStr = "";
		res.each{
			String info = "";
			String industryName = it.getAt("industryName");
			String keywords = it.getAt("keywords");
			info += "<info>"+
					"<name>"+wrapXmlContent(industryName)+"</name>"+
					"<keywords>"+wrapXmlContent(keywords)+"</keywords>"+
				"</info>";
			subStr += info;
		}
		if(subStr.length() > 0){
			str += "<industry_keywords>";
			str += subStr;
			str += "</industry_keywords>";
		}
		return str;
	}
	
	private String getIndustryProductInfo(String flawId){
		String str = "";
		String sql = "select idt.name industryName,"+
			"(select c.name from corporation_product cp, corporation c where cp.id = fip.corporation_product_id and cp.corporation_id = c.id) corName,"+
			"(select manu.name from corporation_product cp, manufacturer manu where cp.id = fip.corporation_product_id and cp.manufacturer_id = manu.id) manuName,"+
			"(select pc.name from corporation_product cp, product_category pc where cp.id = fip.corporation_product_id and cp.product_category_id = pc.id) pcName,"+
			"(select cp.edition from corporation_product cp where cp.id = fip.corporation_product_id) edition "+
			"from flaw_industry_product fip,industry idt,industry_flaw iflaw "+
			"where iflaw.flaw_id = "+flawId+
			" and idt.id = iflaw.industry_id "+
			"and fip.industry_id = iflaw.industry_id "+
			"and fip.flaw_id = iflaw.flaw_id "+
			"and iflaw.type = 0";
		def res = SQLUtil.getResult(sql);
		String subStr = "";
		res.each{
			String info = "";
			String industryName = it.getAt("industryName");
			String corName = it.getAt("corName");
			String manuName = it.getAt("manuName");
			String pcName = it.getAt("pcName");
			String edition = it.getAt("edition");
			info += "<info>"+
				"<name>"+wrapXmlContent(industryName)+"</name>"+
				"<corName>"+wrapXmlContent(corName)+"</corName>";
			String productStr = "<product_info>";
			if(manuName != null && manuName.length() > 0){
				productStr += "<manuName>"+wrapXmlContent(manuName)+"</manuName>";
			}
			if(pcName != null && pcName.length() > 0){
				productStr += "<pcName>"+wrapXmlContent(pcName)+"</pcName>";
			}
			if(edition != null && edition.length() > 0){
				productStr += "<edition>"+wrapXmlContent(edition)+"</edition>";
			}
			productStr += "</product_info>";
			info += productStr;
			info += "</info>";
			subStr += info;
		}
		if(subStr.length() > 0){
			str += "<industry_product>"+subStr+"</industry_product>";
		}
		return str;
	}
	
	/**
	 * 根据漏洞id查询引用集合
	 * @param conn
	 * @param flawId
	 * @return
	 */
	public String getReferenceInfoList(String flawId){
		String str = "";
		String sql = "select link_name,link_url,reference_number,reference_type_id,link_source" +
				" from reference_info where flaw_id="+flawId;
		def res = SQLUtil.getResult(sql);
		String subReferStr = "";
		res.each{
			String subStr = "";
			String linkName = it.getAt("link_name");
			String linkUrl = it.getAt("link_url");
			String referNumber = it.getAt("reference_number");
			String referTypeId = it.getAt("reference_type_id");
			String linkSource = it.getAt("link_source");
			subStr += "<info>"+
					"<link_name>"+wrapXmlContent(linkName)+"</link_name>" +
					"<link_url>"+wrapXmlContent(linkUrl)+"</link_url>" +
					"<reference_number>"+wrapXmlContent(referNumber)+"</reference_number>" +
					"<reference_type>"+wrapXmlContent(referTypeId)+"</reference_type>" +
					"<link_source>"+wrapXmlContent(linkSource)+"</link_source>";
			subStr += "</info>";
			subReferStr += subStr;
		}
		
		if(subReferStr.length() > 0){
			str += "<reference>";
			str += subReferStr;
			str += "</reference>";
		}
		return str;
	}
	
	public String getDetailInfo(String detailedInfoId){
		String detailInfoStr = "";
		String sql = "select description,formal_way,temp_way from detailed_info where id="+detailedInfoId;
		def res = SQLUtil.getResult(sql);
		res.each{
			String desc = it.getAt("description");
			String formalWay = it.getAt("formal_way");
			String tempWay = it.getAt("temp_way");
			detailInfoStr += "<detail_info>" +
				"<description>"+wrapXmlContent(desc)+"</description>" +
				"<formal_way>"+wrapXmlContent(formalWay)+"</formal_way>" +
				"<temp_way>"+wrapXmlContent(tempWay)+"</temp_way>" +
			"</detail_info>";
		}
		
		return detailInfoStr;
	}
	
	private String getTemporalMetricStr(String temporalMetricId) {
		String temporalMetricStr = "";
		String sql = "select * from temporal_metric where id="+temporalMetricId;
		def res = SQLUtil.getResult(sql);
		res.each{
			String exploitability_id = it.getAt("exploitability_id");
			String remediation_level_id = it.getAt("remediation_level_id");
			String report_confidence_id = it.getAt("report_confidence_id");
			String score = it.getAt("score");
			temporalMetricStr += "<temporal_metric>" +
						"<exploitability_id>"+wrapXmlContent(exploitability_id)+"</exploitability_id>" +
						"<remediation_level_id>"+wrapXmlContent(remediation_level_id)+"</remediation_level_id>" +
						"<report_confidence_id>"+wrapXmlContent(report_confidence_id)+"</report_confidence_id>" +
						"<score>"+wrapXmlContent(score)+"</score>" +
					"</temporal_metric>";
		}
		
		return temporalMetricStr;
	}

	private String getEnvironMentalMetricStr(String environMentalMetricId) {
		String environmentMetricStr = "";
		String sql = "select * from environmental_metric where id="+environMentalMetricId;
		def res = SQLUtil.getResult(sql);
		res.each{
			String availability_require_id = it.getAt("availability_require_id");
			String collateral_damage_potential_id = it.getAt("collateral_damage_potential_id");
			String confidentiality_require_id = it.getAt("confidentiality_require_id");
			String integrity_require_id = it.getAt("integrity_require_id");
			String target_distribution_id = it.getAt("target_distribution_id");
			String score = it.getAt("score");
			environmentMetricStr += "<environmental_metric>" +
						"<availability_require_id>"+wrapXmlContent(availability_require_id)+"</availability_require_id>" +
						"<collateral_damage_potential_id>"+wrapXmlContent(collateral_damage_potential_id)+"</collateral_damage_potential_id>" +
						"<confidentiality_require_id>"+wrapXmlContent(confidentiality_require_id)+"</confidentiality_require_id>" +
						"<integrity_require_id>"+wrapXmlContent(integrity_require_id)+"</integrity_require_id>" +
						"<target_distribution_id>"+wrapXmlContent(target_distribution_id)+"</target_distribution_id>" +
						"<score>"+wrapXmlContent(score)+"</score>" +
					"</environmental_metric>";
		}
		
		return environmentMetricStr;
	}
	
	private String getBaseMetricStr(String baseMetricId) {
		String baseMetricStr = "";
		String sql = "select * from base_metric where id="+baseMetricId;
		def res = SQLUtil.getResult(sql);
		res.each{
			String access_complexity_id = it.getAt("access_complexity_id");
			String access_vector_id = it.getAt("access_vector_id");
			String authentication_id = it.getAt("authentication_id");
			String availability_impact_id = it.getAt("availability_impact_id");
			String confidentiality_impact_id = it.getAt("confidentiality_impact_id");
			String integrity_impact_id = it.getAt("integrity_impact_id");
			String score = it.getAt("score");
			baseMetricStr += "<base_metric>" +
						"<access_complexity_id>"+wrapXmlContent(access_complexity_id)+"</access_complexity_id>" +
						"<access_vector_id>"+wrapXmlContent(access_vector_id)+"</access_vector_id>" +
						"<authentication_id>"+wrapXmlContent(authentication_id)+"</authentication_id>" +
						"<availability_impact_id>"+wrapXmlContent(availability_impact_id)+"</availability_impact_id>" +
						"<confidentiality_impact_id>"+wrapXmlContent(confidentiality_impact_id)+"</confidentiality_impact_id>" +
						"<integrity_impact_id>"+wrapXmlContent(integrity_impact_id)+"</integrity_impact_id>" +
						"<score>"+wrapXmlContent(score)+"</score>" +
					"</base_metric>";
		}
		
		return baseMetricStr;
	}
}
