package com.cnvd.api

import com.cnvd.utils.SQLUtil

class SyncFlawInfoController {

    //def index = { }

	/*def getFlawInfo = {
		//TODO 加token
		def startTime = params.startTime;
		def endTime = params.endTime;
		println "startTime="+startTime+" endTime="+endTime
		SyncFlawInfoUtil flawInfoUtil = new SyncFlawInfoUtil();
		String str = flawInfoUtil.getSyncStr(startTime, endTime);
		render(text:str,contentType:"text/xml",encoding:"UTF-8");
	}*/
}
