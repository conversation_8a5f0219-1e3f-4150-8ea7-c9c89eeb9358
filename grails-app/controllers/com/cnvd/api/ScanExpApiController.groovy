package com.cnvd.api

import grails.converters.JSON
import org.apache.commons.collections.map.HashedMap
import com.cnvd.scan.ExpTask
import com.cnvd.utils.CommentsUtil

class ScanExpApiController {

	def attachmentService
	def grailsApplication
    def create = {
		
		List<Map<String, String>> maps = new ArrayList<Map<String, String>>();
		Map<String, String> map = new HashedMap();
		try {
			String taskId = request.getParameter("task_id")
			//附件
			def htmlFile = request.getFile("html")
			def pdfFile = request.getFile("pdf")
			
			ExpTask expTask = ExpTask.findByTask_id(taskId)
			if(expTask){
				expTask.status = 2
				if(htmlFile!=null && !"".equals(htmlFile.getOriginalFilename())){
					String filePath = "${grailsApplication.config.filePath.expoitFileHtml}"
					String realName = CommentsUtil.getCurrentTime() //文件的真实文件名
					def attachment = attachmentService.uploadHtmlFile(htmlFile,filePath,realName)
					expTask.expHtml = attachment
				}
				if(pdfFile!=null && !"".equals(pdfFile.getOriginalFilename())){
					String filePath = "${grailsApplication.config.filePath.expoitFilePdf}"
					String realName = CommentsUtil.getCurrentTime() //文件的真实文件名
					def attachment = attachmentService.uploadPdfFile(pdfFile,filePath,realName)
					expTask.expPdf = attachment
				}
				if(expTask.save(flush: true)){
					map.put("status", 1)
					render map as JSON;
				}
			}else{
				map.put("status", 0)
				map.put("result", "未找到该id的任务")
				render map as JSON;
			}
			
		} catch (Exception e) {
			e.printStackTrace()
			String es = e.getMessage()
			map.put("status", 0)
			map.put("result", es)
			render map as JSON;
		}
		
	}
}
