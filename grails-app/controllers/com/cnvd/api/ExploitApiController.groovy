package com.cnvd.api

import grails.converters.JSON;

import com.cnvd.ExploitTemp

import org.apache.commons.collections.map.HashedMap
import com.cnvd.utils.CommentsUtil

class ExploitApiController {

	def attachmentService
	def grailsApplication
    def index = { }
	/**
	 * 录入利用代码
	 */
	def create = {
		List<Map<String, String>> maps = new ArrayList<Map<String, String>>();
		Map<String, String> map = new HashedMap();
		Map<String, String> mapCount = new HashedMap();
		try {
			String number = request.getParameter("number")
			String Vendor = request.getParameter("Vendor")
			String Product = request.getParameter("Product")
			String Version = request.getParameter("Version")
			String VulnerabillityName = request.getParameter("VulnerabillityName")
			String VulnerabillityDescribe = request.getParameter("VulnerabillityDescribe")
			String CVE = request.getParameter("CVE")
			String BID = request.getParameter("BID")
			String CNVD = request.getParameter("CNVD")
			String EXPName = request.getParameter("EXPName")
			//附件
			def file = request.getFile("EXPFile")
			String Platform = request.getParameter("Platform")
			String Type = request.getParameter("Type")
			String DockerType = request.getParameter("DockerType")
			String EXPEnvironment = request.getParameter("EXPEnvironment")
			String Port = request.getParameter("Port")
			String Refer = request.getParameter("Refer")
			
			ExploitTemp exploitTemp = new ExploitTemp()
			exploitTemp.number = number
			exploitTemp.vendor = Vendor
			exploitTemp.product = Product
			exploitTemp.proVersion = Version
			exploitTemp.vulnerabillityName = VulnerabillityName
			exploitTemp.vulnerabillityDescribe = VulnerabillityDescribe
			exploitTemp.cve = CVE
			exploitTemp.bid = BID
			exploitTemp.cnvd = CNVD
			exploitTemp.expName = EXPName
//			exploitTemp.expFile = EXPFile
			if(file!=null && !"".equals(file.getOriginalFilename())){
				String filePath = "${grailsApplication.config.filePath.expoitFilePath}"
				String realName = CommentsUtil.getCurrentTime() //文件的真实文件名
				def attachment = attachmentService.uploadFileExp(file,filePath,realName)
				exploitTemp.expFile = attachment
			}
			exploitTemp.platform = Platform
			exploitTemp.type = Type
			exploitTemp.dockerType = DockerType
			exploitTemp.expEnvironment = EXPEnvironment
			exploitTemp.port = Port
			exploitTemp.refer = Refer
			if(exploitTemp.save(flush: true)){
				map.put("status", 1)
				maps.add(map)
				render maps as JSON;
			}
		} catch (Exception e) {
			e.printStackTrace()
			String es = e.getMessage()
			map.put("status", 0)
			mapCount.put("result", es)
			maps.add(map)
			maps.add(mapCount)
			render maps as JSON;
		}
		
	}
}
