package com.cnvd.api


import java.text.SimpleDateFormat
import java.util.regex.Matcher
import java.util.regex.Pattern

import com.cnvd.utils.SQLUtil
import com.cnvd.utils.Aes;
import com.cnvd.ApiTaskLog
import com.cnvd.common.Task
import com.cnvd.common.Attachment
import grails.converters.JSON

import org.apache.commons.collections.map.HashedMap

/**
 * 
 * <AUTHOR>
 *
 */
class ApiTaskOutController {
	def attachmentService
	/**
	 *www.cnvd.org.cn/api/task?key=aaaaaa
	 *其中key值为通过AES加密的
	 * 提供task对外接口，返回对应用户的任务类型 标题 描述 附件字段。
	 * key对应task表中targettuser_id字段
	 * key对应tuser中id字段
	 * 生成key的名称为tuser表中的nick_name
	 * 
	 */
	//	static allowedMethods = [taskout: "POST", download: "POST"]
	def taskout = {
		//response.setCharacterEncoding("UTF-8")
		response.setContentType("text/html;charset=UTF-8");
		List<Map<String, String>> maps = new ArrayList<Map<String, String>>();
		def keyi = params.key;
		def createdate = params.date;
		def keysub = keyi.substring(0, 32);

		def ApiTaskLogInstance = ApiTaskLog.findByKeyNumber(keyi)

		if(!ApiTaskLogInstance){	//判断接口记录中是否有该Key，没有则不允许调用
			Map<String, String> map = new HashedMap();
			map.put("error","params is error！");
			maps.add(map);
		}else if(createdate == null){  //校验date是否为空
			Map<String, String> map = new HashedMap();
			map.put("error","params is error！");
			maps.add(map);
		}else if(keyi == null){	 //校验key是否为空
			Map<String, String> map = new HashedMap();
			map.put("error","params is error！");
			maps.add(map);
		}else{
			try{
				//年月日验证规则
				String regEx = /^((((19|20)\d{2})-(0?[13-9]|1[012])-(0?[1-9]|[12]\d|30))|(((19|20)\d{2})-(0?[13578]|1[02])-31)|(((19|20)\d{2})-0?2-(0?[1-9]|1\d|2[0-8]))|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))-0?2-29))$/
				// 编译正则表达式
				Pattern pattern = Pattern.compile(regEx);
				Matcher matcher = pattern.matcher(createdate);
				boolean flag = matcher.matches();
				if(!flag){
					Map<String, String> map = new HashedMap();
					map.put("error","params is error！");
					maps.add(map);
					render maps as JSON
					return
				}

				def key = Aes.decode(keysub);
				def intKey = Integer.valueOf(key);

				if(intKey instanceof Integer){

					String sql = "SELECT * FROM task t WHERE t.targettuser_id = '"+key+"' AND t.date_created LIKE '"+createdate+"%'";
					def apiInstanceList = SQLUtil.getList(sql);

					//判断是否为中国电信集团，如果是则增加输出属性
					if(intKey!=18262){
						for(int i = 0; i  < apiInstanceList.size() ; i++){
							int  yy = apiInstanceList.size();
							Map<String, String> map = new HashedMap();
							def id_task = apiInstanceList[i].id;
							def title = apiInstanceList[i].title;
							//时间
							def dateCreated = apiInstanceList[i].date_created;
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							String fdate = sdf.format(dateCreated);
							map.put("title",title);
							map.put("info", apiInstanceList[i].info);
							map.put("dateCreated", fdate);
							map.put("finishDate","48h");
							//
							def attid=0;
							def flawId = apiInstanceList[i].flaw_id;
							//def _val="http://localhost:8080/cnvd/apiTaskOut/download?id="
							def _val="http://www.cnvd.org.cn/apiTaskOut/download?id="
							if(flawId!=null){
								String sqlStr = "SELECT * FROM flaw t WHERE t.id = '"+flawId+"';";
								def flawInstance = SQLUtil.getList(sqlStr);

								attid=flawInstance[0].attachment_id
								if(attid!=null){
									_val+=Aes.encode(attid+"")
								}else{
									_val="无附件"
								}
							}else{
								_val="无附件"
							}
							map.put("downCode", _val);
							maps.add(map);
						}
					}else{
						//电信集团输出
						for(int i = 0; i  < apiInstanceList.size() ; i++){
							int  yy = apiInstanceList.size();
							Map<String, String> map = new HashedMap();
							def id_task = apiInstanceList[i].id;
							def title = apiInstanceList[i].title;
							//时间
							def dateCreated = apiInstanceList[i].date_created;
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							String fdate = sdf.format(dateCreated);
							map.put("title",title);
							map.put("info", apiInstanceList[i].info);
							map.put("dateCreated", fdate);
							map.put("finishDate","48h");
							def attid=0;
							def flawId = apiInstanceList[i].flaw_id;
							//def _val="http://localhost:8080/cnvd/apiTaskOut/download?id="
							def _val="http://www.cnvd.org.cn/apiTaskOut/download?id="
							if(flawId!=null){
								String sqlStr = "SELECT * FROM flaw t WHERE t.id = '"+flawId+"';";
								def flawInstance = SQLUtil.getList(sqlStr);

								attid=flawInstance[0].attachment_id
								if(attid!=null){
									_val+=Aes.encode(attid+"")
								}else{
									_val="无附件"
								}
							}else{
								_val="无附件"
							}
							map.put("downCode", _val);
							//新增属性
							def taskInstance = Task.get(id_task.toInteger());
							//漏洞名称
							map.put("vulnerabillityName", taskInstance.flaw.title);
							//漏洞来源
							if(taskInstance.flaw.user.userName){
								map.put("vulnerabillitySource", taskInstance.flaw.user.userName);
							}else{
								map.put("vulnerabillitySource", taskInstance.flaw.user.nickName);
							}
							
							//漏洞编号
							if(taskInstance.flaw.number){
								map.put("CNVD", taskInstance.flaw.number);
							}else{
								map.put("CNVD", taskInstance.flaw.tempNumber);
							}
							//漏洞附件名称
							if(taskInstance.flaw.attachment.realName){
								map.put("vulnerabillityFileName", taskInstance.flaw.attachment.realName);
							}else{
								map.put("vulnerabillityFileName", "无附件");
							}
							
							maps.add(map);
						}
					}

				}else{
					Map<String, String> map = new HashedMap();
					map.put("error","params is error！");
					maps.add(map);
				}
			}catch(Exception e){
				Map<String, String> map = new HashedMap();
				map.put("error","params is error！");
				maps.add(map);
			}finally{
			}
		}
		render maps as JSON
	}
	def download = {
		def attId = params.id
		def tid=Aes.decode(attId)
		def att = Attachment.get(tid)
		//attachmentService.downloadAtt(att,request,response) 2021-09-12 暂时注释掉
	}
	/**
	 * 创建接口key值
	 */
	def setKey = {
		//		def tuserId = params.tuserid

		//def tuserIds = "14100,10286,14945,17656,14093,14698,15362,14797,14774,15200,14282,14798,15148,14773,15640,14095,14568,14094,15629,15637,14541,14488,15642,15043,9989,14800,15674,15631,14936,15277,15193,18262,15842,9827"
		def tuserIds = "19055"
		//		def tuserNames = "上海分中心,云南分中心,内蒙古分中心,北京分中心,吉林分中心,四川分中心,天津分中心,宁夏分中心,安徽分中心,山东分中心,山西分中心,广东分中心,广西分中心,新疆分中心,江苏分中心,江西分中心,河北分中心,河南分中心,浙江分中心,海南分中心,湖北分中心,湖南分中心,甘肃分中心,福建分中心,贵州分中心,贵州分中心,辽宁分中心,重庆分中心,陕西分中心,青海分中心,黑龙江分中心,中国电信集团公司,赛尔网络,上海交通大学"
		def tuserNames = "教育部科技司"
		def tuserId = tuserIds.split(",");

		def tuserName = tuserNames.split(",");

		//		def i = tuserId.size();
		def j = tuserName.size();

		for(int i=0; i<tuserId.size(); i++){
			def tid=Aes.encode(tuserId[i])
			def time = new Date().getTime();
			def key = tid+time
			//保存
			ApiTaskLog atl = new ApiTaskLog();
			atl.tuserId = tuserId[i];
			atl.tuserName = tuserName[i];
			atl.dateCreated = new Date();
			atl.keyNumber = key;
			atl.save();
			println key;
		}


	}
}
