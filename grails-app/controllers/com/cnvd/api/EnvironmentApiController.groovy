package com.cnvd.api

import com.cnvd.Environment
import grails.converters.JSON
import java.text.SimpleDateFormat
import org.apache.commons.collections.map.HashedMap

/**
 * 验证环境数据接口
 * <AUTHOR>
 *
 */
class EnvironmentApiController {
	def create = {
//		List<Map<String, String>> maps = new ArrayList<Map<String, String>>();
		Map<String, String> map = new HashedMap();
		Map<String, String> mapCount = new HashedMap();
		try {
			SimpleDateFormat sf=new SimpleDateFormat("yyyy-MM-dd");
			String numberStr = request.getParameter("Number")
			int number = Integer.parseInt(numberStr);
			String name = request.getParameter("InstanceName")
			String instance = request.getParameter("InstanceFilePath")
			String md5 = request.getParameter("MD5")
			//日期为yyyy-MM-dd格式
			String insDateCreate = request.getParameter("CreateTime")
			String insDateUpdate = request.getParameter("ModifyTime")
			String edition = request.getParameter("Version")
			String vulType = request.getParameter("VulType")
			String vulName = request.getParameter("VulnerabillityName")
			String cnvdNum = request.getParameter("CNVD")
			String cveNum = request.getParameter("CVE")
			String bidNum = request.getParameter("BID")
			String position = request.getParameter("VerifyPath")
			String permission = request.getParameter("Authorification")
			String ise = request.getParameter("isExploit")
			String expContent = request.getParameter("ExploitFile")
			String isp = request.getParameter("isPatch")
			String patchFile = request.getParameter("PatchFile")
			String introduction = request.getParameter("Introduction")
			
			println "Number="+number+",InstanceName="+name+",InstanceFilePath="+instance+",MD5="+md5+",CreateTime="+insDateCreate+",ModifyTime="+insDateUpdate+",Version="+edition+",VulType="+vulType+",VulnerabillityName="+vulName+",CNVD="+cnvdNum+",CVE="+cveNum+",BID="+bidNum+",VerifyPath="+position+",Authorification="+permission+",isExploit="+ise+",ExploitFile="+expContent+",isPatch="+isp+",PatchFile"+patchFile
			Environment environment = new Environment()
			environment.number = number
			environment.name = name
			environment.instance = instance
			environment.md5 = md5
			environment.insDateCreate = sf.parse(insDateCreate)
			environment.insDateUpdate = sf.parse(insDateUpdate)
			environment.edition = edition
			environment.vulType = vulType
			environment.vulName = vulName
			environment.cnvdNum = cnvdNum
			environment.cveNum = cveNum
			environment.bidNum = bidNum
			environment.position = position
			environment.permission = permission
			environment.ise = ise
			environment.expContent = expContent
			environment.isp = isp
			environment.patchFile = patchFile
			environment.introduction = introduction
			
			if(environment.save(flush: true)){
				map.put("status", 1)
//				maps.add(map)
				render map as JSON;
			}
		} catch (Exception e) {
//			e.printStackTrace()
			String es = e.getMessage()
			map.put("status", 0)
			map.put("result", es)
//			maps.add(map)
//			maps.add(mapCount)
			render map as JSON;
		}
		
	}
}
