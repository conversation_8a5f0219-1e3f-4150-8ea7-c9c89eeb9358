package com.cnvd.api

import grails.converters.JSON;

import com.cnvd.info.Webinfo;

class ApiController {

	def index = { }

	def sync = {
		long date = params.long('d')
		def releaseTime = new Date(date)
		def webInfoCriteria = Webinfo.createCriteria()
		def webinfoInstanceList = webInfoCriteria.list {
			and {
				'in'('type',[1, 2, 14])
				eq("status",1)
				gt('releaseTime',releaseTime)
			}
		}
		def result = new ArrayList<Map<String,Object>>()
		for (Webinfo webinfo : webinfoInstanceList) {
			def resultMap = new HashMap<String,Object>()
			resultMap.put('id', webinfo.id)
			resultMap.put('title', webinfo.title)
			resultMap.put('content', webinfo.content)
			resultMap.put('type', webinfo.type)
			result.add(resultMap)
		}
		render result as JSON
		return
	}
}
