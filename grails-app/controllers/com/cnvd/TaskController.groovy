package com.cnvd

import com.cnvd.common.Attachment
import com.cnvd.common.AttachmentPdf
import com.cnvd.common.CarbonCopyTask
import com.cnvd.common.Task
import com.cnvd.patchInfo.PatchInfo
import com.cnvd.patchInfo.PatchInfoAttachment
import com.cnvd.patchInfo.PatchInfoAttachmentPdf
import com.cnvd.utils.Constants
import com.cnvd.utils.DateUtils
import com.cnvd.utils.EncryptUtils
import com.cnvd.utils.OfficeToPDF
import grails.converters.JSON

class TaskController {
	def attachmentService
	def grailsApplication
	def taskService
	static allowedMethods = [save: "POST", update: "POST", delete: "POST"]

	def index = {
		redirect(action: "myTaskList", params: params)
	}

	def zzTaskInfo={
		if(!session.user){
			render(view:"/error")
			return
		}

		def titleInstance = Task.findByIdAndTargetTUser(params.id,session.user)
        def flawId = titleInstance.flawId
        def taskInstance = Task.executeQuery("from Task as b where b.flaw.id=:flawId and b.type=:type and b.enable=1",[flawId:flawId,type:Constants.FLAW_EXPOIT])[0]

		if(taskInstance){
			[taskInstance:taskInstance]
		}else{
			render(view:"/error")
			return
		}
	}
	def czTaskInfo={
		if(!session.user){
			render(view:"/error")
			return
		}

		def titleInstance = Task.findByIdAndTargetTUser(params.id,session.user)
		def flawId = titleInstance.flawId
		def taskInstance = Task.executeQuery("from Task as b where b.flaw.id=:flawId and b.type=:type and b.enable=1",[flawId: flawId,type:Constants.FLAW_PATCH])[0]

		if(taskInstance){
			[taskInstance:taskInstance]
		}else{
			render(view:"/error")
			return
		}
	}

	def taskInfo={
		if(!session.user){
			render(view:"/error")
			return
		}
		def taskInstance = Task.findByIdAndTargetTUser(params.id,session.user)
		def flawTypesParamMiddle = FlawTypesParamMiddle.findAllByFlaw(taskInstance.flaw);
		if(taskInstance){
			[taskInstance:taskInstance,flawTypesParamMiddle: flawTypesParamMiddle]
		}else{
			render(view:"/error")
			return
		}
	}
	def ccTaskInfo = {
		if(!session.user){
			render(view:"/error")
			return
		}
		def taskInstance = Task.findById(params.id)
		def ccTaskInstance = CarbonCopyTask.findByTaskAndTuser(taskInstance,session.user)
		if(ccTaskInstance){
			[taskInstance:taskInstance]
		}else{
			render(view:"/error")
			return
		}
	}
	
	def submitTask={
		if(!session.user){
			return redirect(controller: "TUser", action: "login")
		}
		def Id = EncryptUtils.aesDecrypt(params.id)
		def taskInstance = Task.findByIdAndTargetTUser(Long.parseLong(Id),session.user)
		if(taskInstance&&taskInstance?.targetTUser?.id==session?.user?.id){
			[taskInstance: taskInstance,offset:params.offset]
		}else{
			render(view:"/error")
		}
	}
	def submitPatch={
		if(!session.user){
			return redirect(controller: "TUser", action: "login")
		}
		def Id = EncryptUtils.aesDecrypt(params.id)
		def patchInfo = PatchInfo.get(Long.parseLong(Id))
		if(patchInfo&&patchInfo?.tuser?.id==session?.user?.id){
			[patchInfo:patchInfo,offset:params.offset]
		}else{
			render(view:"/error")
		}
	}
	def pathInfoShow = {
		def patchInfo = PatchInfo.get(params.id)
		[patchInfo: patchInfo]
	}

	def savePatch={
		String filePath = "${grailsApplication.config.filePath.patchInfoAttFilePath}" //文件的路径
		//def root =""
		def root ="D:"
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			redirect(controller:"TUser",action:"login")
			return
		}
		def file = request.getFile("attachmentFile")
			PatchInfo patchInfo= PatchInfo.get(params.id)
			patchInfo.status=1
			patchInfo.properties=params
		    patchInfo.isDisposalTaskPatch=0
			if(params.isDeleteAtt!=null && params.int('isDeleteAtt')==1){
				patchInfo.attachment=null
			}
		if(patchInfo.save(flush:true)){
			PatchInfoAttachment.executeUpdate('delete from PatchInfoAttachment where patchInfo = ?',[patchInfo])
			PatchInfoAttachment.executeUpdate('delete from PatchInfoAttachmentPdf where patchInfo = ?',[patchInfo])
			if(params.attIds){
				def attArr = params.attIds.split(";")
				attArr.each{
					def attachment = Attachment.get(it)
					/*if(attachment){
						def tempPacthInfo = PatchInfoAttachment.findByAttachment(attachment)
						if(!tempPacthInfo){*/
							def patchInfoAttachment = new PatchInfoAttachment()
							patchInfoAttachment.patchInfo = patchInfo
							patchInfoAttachment.attachment = attachment
							patchInfoAttachment.save(flush:true)

							def fileName=attachment.getFileName()
							def realName= DateUtils.getCurrentTime()
							def attachmentPdf = new AttachmentPdf()
							//生成pdf附件
							def fileNameSuffix=fileName.substring(fileName.lastIndexOf("."))
							//如果上传的附件是word格式
							if (fileNameSuffix.equals(".doc")||fileNameSuffix.endsWith(".docx")){
								def newFileName=fileName.substring(0, fileName.lastIndexOf("."))

								def result = OfficeToPDF.office2PDF(root+attachment.getPath(), root+filePath+realName+".pdf")
								//操作成功与否的提示信息. 如果返回 -1, 表示找不到源文件, 或url.properties配置错误; 如果返回 0,则表示操作成功; 返回1, 则表示转换失败; 返回2, 则表示打水印失败
								if (result==-1){
									println("PDF生成找不到源文件")
									render(view:"/error")
									return
								}else if (result==1){
									println("PDF生成转换失败")
									render(view:"/error")
									return
								}else if (result==2){
									println("PDF生成打水印失败")
									render(view:"/error")
									return
								}else if (result==0){
									File pdfFile = new File(root+filePath+realName+"_pdf.pdf")
									//pdf附件保存
									attachmentPdf.realName=realName+"_pdf.pdf"
									attachmentPdf.fileName=newFileName+"_pdf.pdf"
									attachmentPdf.fileType="application/pdf"
									attachmentPdf.fileSize=pdfFile.length()
									attachmentPdf.path=filePath+realName+"_pdf.pdf"
									attachmentPdf.attachmentId=attachment.id
									attachmentPdf.save(flush:true)

									def patchInfoAttachmentPdf = new PatchInfoAttachmentPdf()
									patchInfoAttachmentPdf.patchInfo = patchInfo
									patchInfoAttachmentPdf.attachmentPdf = attachmentPdf
									patchInfoAttachmentPdf.save(flush: true)
								}
							}
						/*}
					}*/
				}
			}
		}else {
			patchInfo.errors.allErrors.each{ println it }
		}
		redirect(controller: "user",action: "mypatchInfo",params:[offset:params.offset])
		return
	}

	def saveExploitTask={
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			redirect(controller:"TUser",action:"login")
			return
		}
		def file = request.getFile("file")
		def taskInstance = Task.findByIdAndTargetTUser(params.id,session.user)
		if(!taskInstance){
			render(view:"/error")
			return
		}
		def toActionName="myexploits"
		if(taskInstance.type==Constants.FLAW_PATCH){
			toActionName="mydisposition"
		}
		println "params的值为" + params
		if(session.user.id!=taskInstance.targetTUser.id || taskService.submitTask(params,file)){
			redirect(controller: "user",action: toActionName,params:[offset:params.offset])
			return
		}else{
			flash.attErr = "上传文件格式或者其他错误,请检查后重新保存"
			redirect(action: "submitTask",params:[id:taskInstance.id])
		}
	}
	def viewTaskDetail={
		if(!session.user){
			redirect(controller: "TUser", action: "login")
			return
		}
		def taskInstance = Task.findByIdAndTargetTUser(params.id,session.user)

		[taskInstance:taskInstance]
	}

	def download = {
		if(!session.user){
			return redirect(controller: "TUser", action: "login")
		}
		def taskInstance = Task.findByDownCodeAndTargetTUser(params.cd,session.user)
		if(taskInstance){
			if("patchInfo".equals(params.t)){
				if(taskInstance?.patchInfo?.attachment){
					attachmentService.downloadAtt(taskInstance?.patchInfo?.attachment,request,response)//2021-09-12 待后续处理，可能有漏洞
				}else{
					render(view:"/error")
				}
			}else if("exploit".equals(params.t)){
				if(taskInstance?.exploit?.attachment){
					attachmentService.downloadAtt(taskInstance?.exploit?.attachment,request,response)//2021-09-12 待后续处理，可能有漏洞
				}else{
					render(view:"/error")
				}
			}else if("task".equals(params.t)){
				if(taskInstance?.attachment){
					//2021-09-12 待后续处理，可能有漏洞
					attachmentService.downloadAttFile(taskInstance.attachment,request,response)
				}else{
					render(view:"/error")
				}
			} else if ("flaw".equals(params.t)) {
				if (taskInstance?.targetTUser?.id!=session?.user?.id){
					render(view:"/error")
				}
				if (taskInstance?.flaw?.attachment) {
					//2021-09-12 待后续处理，可能有漏洞
					attachmentService.downloadAttFile(taskInstance.flaw.attachment,request,response)
				} else {
					render(view:"/error")
				}
			}
		}else{
			render(view:"/error")
		}
	}

	//TODO 对文件格式进行验证
	def uploadAtt = {
		if(!session.user){
			return redirect(controller: "TUser", action: "login")
		}
		println "AAAAAAAAAAAAAAAAAAAAAAAA"
		println "type="+params.type

		def file = request.getFile("file");
		println "file="+file
		println "fileName="+file.getOriginalFilename()

		if(taskService.checkFile(file)){
			String filePath = ""
			if(params.int('type') == 1){
				filePath = "${grailsApplication.config.filePath.exploitAttFilePath}" //文件的路径
			}else if(params.int('type') == 3){
				filePath = "${grailsApplication.config.filePath.patchInfoAttFilePath}" //文件的路径
			}
			println "filePath="+filePath
			String realName = new Date().getTime() //文件的真实文件名
			def attachment = attachmentService.uploadFile(file,filePath,realName)
			println "attachment="+attachment
			println "attId="+attachment.id
			println "fileName="+attachment.fileName

			//render "attId:"+attachment.id+";fileName:"+attachment.fileName
			def output = ["attachmentId":attachment.id]

			render output as JSON
			//render ""+attachment.id
		}else{
			render "type error"
		}
	}

}
