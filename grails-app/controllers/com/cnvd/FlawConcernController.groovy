package com.cnvd

import com.cnvd.utils.Constants

class FlawConcernController {

	def userService
	
    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]

	/**
	 * 对漏洞进行关注
	 */
	def concernFlaw = {
		if(session.user){
			String returnInfo
			//获得关注的漏洞
			Flaw flawInstance = Flaw.findByDownCode(params.downCode)
			if(session.user.id == flawInstance.user.id){
				//漏洞报送人与关注此漏洞者为同一个人
				returnInfo = "您不能对自己发布的漏洞进行关注！"
			}else{
				//漏洞报送人与关注此漏洞者不是同一个人
				FlawConcern flawConcern = FlawConcern.findByUserAndFlaw(session?.user,flawInstance)
				if(flawConcern){
					returnInfo = "您已经关注过此漏洞了!"
				}else{
					flawConcern = new FlawConcern()
					flawConcern.user = session.user
					flawConcern.flaw = flawInstance
					if(flawConcern.save(flush:true)){
						returnInfo = "关注成功"
						flawInstance.concernCount = (flawInstance.concernCount?flawInstance.concernCount:0)+1
						/**
						 * 给被关注人添加积分，并添加积分明细
						 */
						userService.addUserScore(flawInstance.user,Constants.concernedType,Constants.concernedValue)
					}else{
						returnInfo = "关注失败"
					}
				}
			}
			render(template:"/common/concernInfo",model:[returnInfo:returnInfo,flawInstance:flawInstance])
		}else{
			session.originalRequestParmas = [controller:controllerName,action:actionName]
			redirect(controller:"TUser",action:"login")
		}
	}
}
