package com.cnvd

import com.cnvd.utils.Constants

class FlawCommentsController {

	def userService
    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]

	/**
	 * 保存用户的评论，并向flaw_comments表中插入数据
	 * 漏洞评论时，判断是否是第一次评论
	 * 如果是第一次评论，则给评论人和漏洞上报者加积分
	 * 如果不是第一次评论，则不加积分
	 * 漏洞评论的回复，不加积分
	 */
    def save = {
		println 222
		//token
		withForm {
			if(session.user){
				/**
				 * 判断评论人（session.user.userId）和漏洞的发布人(Flaw.user.userId)是否为同一个人
				 * 如果是同一个人，则提示“不能对自己的漏洞进行评论”
				 * 如果不是同一个人，则评论成功
				 */
				def flawInstance = Flaw.get(params.flawId)
				def flawCommentsInstance = new FlawComments()
				if(params.parentCommentId){
					//表明是对已有的“评论”进行“回复”操作
					//设置评论人属性
					flawCommentsInstance.user = session.user
					//设置评论的漏洞
					flawCommentsInstance.flaw = flawInstance
					//查询被回复的评论
					def parentCmtInstance = FlawComments.get(params.parentCommentId)
					if(parentCmtInstance.user.id == session.user.id){
						//表明被回复的评论人与回复人为同一人
						flash.message = "您不能对自己发布的评论进行回复！"
						redirect(controller:"flaw",action:"show",id:params.flawId)
						return
					}else{
						flawCommentsInstance.content = params.reply
						flawCommentsInstance.parentComment = parentCmtInstance
					}
					if(flawCommentsInstance.save(flush:true)){
						flawInstance.commentCount = (flawInstance.commentCount?flawInstance.commnetCount:0)+1
						//评论成功，跳转到漏洞显示页面（相当于是刷新了当前页面）
						redirect(controller : "flaw", action: "show", id: params.flawId)
						return
					}else{
						flash.message="评论失败"
						redirect(controller : "flaw", action: "show", id: params.flawId)
						return
					}
				}else{
					/**
					 * 表明是对漏洞进行“评论”操作
					 * 判断评论人（session.user.userId）和漏洞的发布人(Flaw.user.userId)是否为同一个人
					 * 如果是同一个人，则提示“不能对自己的漏洞进行评论”
					 * 如果不是同一个人，则评论成功
					 */
					if(session.user.id == flawInstance.user.id){
						flash.message = "您不能对自己上报的漏洞进行评论！";
						redirect(controller:"flaw",action:"show",id:params.flawId)
						return
					}else{
						//设置评论人属性
						flawCommentsInstance.user = session.user
						//设置评论的漏洞
						flawCommentsInstance.flaw = flawInstance
						//设置评论的内容
						flawCommentsInstance.content = params.comments
						
						//根据评论人查找其对应的评论的数量
						def flawCommentsCount = FlawComments.countByUserAndFlaw(session.user,flawInstance)
						FlawComments.withTransaction{status ->
							if(flawCommentsInstance.save(flush:true)){
								flawInstance.commentCount = (flawInstance.commentCount?flawInstance.commentCount:0)+1
								if(flawCommentsCount == 0){
									//表明此评论人是第一次对此漏洞进行评论，需要增加评论人和漏洞上报者的积分
									//更新评论人用户积分
									session.user.integValue = session?.user?.integValue?session?.user?.integValue:0 + Constants.commentByYouValue
									TUser tuser = TUser.get(session.user.id)
									userService.addUserScore(tuser,Constants.commentByYouType,Constants.commentByYouValue)
									
									//更新被评论人用户积分
									userService.addUserScore(flawInstance.user,Constants.commentToYouType, Constants.commentToYouValue)
								}
								
								//评论成功，跳转到漏洞显示页面（相当于是刷新了当前页面）
								redirect(uri:"/flaw/show/"+params.flawId+"#commentList")
							}else{
								flash.message = "评论失败"
								redirect(controller:"flaw",action: "show",id:params.flawId)
								return
							}
						}
					}
				}
			}else{
				//没有用户登录，需要跳转到用户登录页面
				redirect(controller:"TUser",action:"login")
			}
		}.invalidToken {  
		
		render(view:"/error") }
    }
}
