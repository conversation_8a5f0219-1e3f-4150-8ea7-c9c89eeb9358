package com.cnvd
/**
 * 每周漏洞以及补丁信息
 */
import org.apache.commons.collections.map.HashedMap
import grails.converters.JSON
import java.text.SimpleDateFormat
import com.cnvd.flawInfo.DictionaryInfo
import com.cnvd.flawInfo.FlawProduct
import com.cnvd.flawInfo.ReferenceInfo
import com.cnvd.patchInfo.PatchInfo
import com.cnvd.flawInfo.ReferenceType

class ShowApiController {

	def showFlaw = {
		List<Map<String, String>> maps = new ArrayList<Map<String, String>>();
		SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-dd")
		def hql = " from Flaw where storageTime<'2017-02-13' and storageTime>='2017-02-06' and isEvent=0 and isu=1"
		def flawList = Flaw.executeQuery(hql)
		for (Flaw flawInstance: flawList) {
			Map<String, String> map = new HashedMap();
			map.put("CNVD-ID",flawInstance?.number);
			map.put("漏洞名称",flawInstance?.title);
			map.put("发布时间",sdf.format(flawInstance?.openTime));
			//危害级别
			def severityDi=DictionaryInfo.get(flawInstance?.serverityId);
			if(severityDi?.value == 1){
				map.put("危害级别","低")
			}else if(severityDi?.value == 2){
				map.put("危害级别","中")
			}else if(severityDi?.value == 3){
				map.put("危害级别","高")
			}else{
				map.put("危害级别","未评级")
			}
			
			//影响产品
			def flawProductList = FlawProduct.findAllByFlaw(flawInstance)
			def products = "";
			for (FlawProduct flawProduct:flawProductList) {
				products+=flawProduct?.product?.name
				products+="   "
			}
			map.put("影响产品",products);
			
			//BID
			def bids = "";
			def bidList = ReferenceInfo.findAllByFlawAndReferenceType(flawInstance,ReferenceType.get(2))
			if(bidList){
				for (ReferenceInfo referenceInfoInstance:bidList) {
					bids+=referenceInfoInstance?.referenceNumber
					bids+="   "
				}
				map.put("BUGTRAQ ID",bids);
			}else{
				map.put("BUGTRAQ ID","");
			}
			//CVE
			def cves = "";
			def cveList = ReferenceInfo.findAllByFlawAndReferenceType(flawInstance,ReferenceType.get(1))
			if(cveList){
				for (ReferenceInfo cveInfoInstance:cveList) {
					cves+=cveInfoInstance?.referenceNumber
					cves+="   "
				}
				map.put("CVE ID",cves);
			}else{
				map.put("CVE ID","");
			}
			map.put("漏洞描述",flawInstance?.detailedInfo?.description);
			if(flawInstance?.isEvent == 0){
				map.put("漏洞类型","通用软硬件漏洞");
			}else{
				map.put("漏洞类型","事件型漏洞");
			}
			map.put("参考链接",flawInstance?.referenceLink);
			map.put("漏洞解决方案",flawInstance?.detailedInfo?.formalWay);
			map.put("漏洞发现者",flawInstance?.discovererName);
			//厂商补丁
			def patchInfoInstance = PatchInfo.findByFlawAndEnable(flawInstance,1)
			String changshang = patchInfoInstance?.patchName
			if(changshang){
				map.put("厂商补丁",changshang)
			}else{
				map.put("厂商补丁","(无补丁信息)")
			}
			map.put("报送时间",sdf.format(flawInstance?.submitTime));
			maps.add(map);
		}
		render maps as JSON
	}
	
	def showPatch = {
		List<Map<String, String>> maps = new ArrayList<Map<String, String>>();
		SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-dd")
		def hql = " from Flaw where storageTime<'2017-02-13' and storageTime>='2017-02-06' and isEvent=0 and isu=1"
		def flawList = Flaw.executeQuery(hql)
		for (Flaw flawInstance: flawList) {
			def patchInfoInstance = PatchInfo.findByFlawAndEnable(flawInstance,1)
			if(patchInfoInstance){
				Map<String, String> map = new HashedMap();
				map.put("补丁名称",patchInfoInstance?.patchName)
				map.put("所属漏洞编号",patchInfoInstance?.flaw?.number)
				map.put("补丁链接",patchInfoInstance?.patchUrl)
				map.put("补丁描述",patchInfoInstance?.patchDescription)
				maps.add(map);
			}
		}
		render maps as JSON
	}
}
