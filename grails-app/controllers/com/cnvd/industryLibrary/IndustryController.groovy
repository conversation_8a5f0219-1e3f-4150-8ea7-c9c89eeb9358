package com.cnvd.industryLibrary

class IndustryController {

    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]
	def flawList = {
		println "params.industryId="+params.id
		def industryInstance = Industry.get(params.id)
		if(!industryInstance){
			render(view:"/error")
		}else{
			[industryInstance:industryInstance]
		}
	}

	def flawPaginate = {
		params.max = Math.min(params.max ? params.int('max') : 20, 100)
		params.offset = params.offset ? params.int('offset') : 0
		def industryInstance = Industry.get(params.industryId)
		if(!industryInstance){
			render(view:"/error")
		}
		def hql = " from IndustryFlaw iflaw,Flaw f where iflaw.industry = ? and iflaw.flaw = f "+
			"and f.status=9 and f.enable=1 and f.openTime <= ? and f.isOpen=1 and f.parentFlaw is null "
		def flawList = IndustryFlaw.executeQuery("select distinct iflaw.flaw"+hql+"order by f.openTime desc",[industryInstance,new Date()],[max:params.max,offset:params.offset])
		def countHql = "select count(distinct iflaw.flaw) as cnt "+hql
		def count = IndustryFlaw.executeQuery(countHql,[industryInstance,new Date()])[0]
		render(view:"/industry/flawPaginate",model:[flawList:flawList,flawTotal:count])
	}
}
