package com.cnvd
/**
 * 全量接口展示页面
 * <AUTHOR>
 *
 */
import com.cnvd.cer.CA
import cnvd.TotalFlawApi
class TotalFlawApiController {

	def grailsApplication
    def list = {
		if(!session.user){
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		//eq("user",session.user)		
		def totalFlawApi = TotalFlawApi.findByTuser(session.user)
		def caInstace = CA.findByTuserAndEnable(session.user,0)
		[CaInstace:caInstace,TotalFlawApi:totalFlawApi]
	}
	
	/**
	 * 证书下载
	 */
	def download = {
		def caInstance = CA.get(params.id)
		def cerFilePath = "${grailsApplication.config.filePath.cerFilePath}"
		request.setCharacterEncoding("UTF-8");
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;

		def fileName = caInstance.cnStr+".p12"
		String downLoadPath = cerFilePath+"certs/"+fileName
		long fileLength = new File(downLoadPath).length();
		def browserType = request.getHeader("User-Agent");
		response.setContentType("application/octet-stream");
		byte[] bytes = browserType.contains("MSIE") ? fileName.getBytes() : fileName.getBytes("utf-8");
		response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
		response.setHeader("Content-Length", String.valueOf(fileLength));

		bis = new BufferedInputStream(new FileInputStream(downLoadPath));
		bos = new BufferedOutputStream(response.getOutputStream());
		byte[] buff = new byte[2048];
		int bytesRead;
		while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
			bos.write(buff, 0, bytesRead);
		}
		bis.close();
		bos.close();
	}
	
	def downKeystore = {
		def caInstance = CA.get(params.id)
		def cerFilePath = "${grailsApplication.config.filePath.cerFilePath}"
		request.setCharacterEncoding("UTF-8");
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;

		def fileName = caInstance.cnStr+".keystore"
		String downLoadPath = cerFilePath+"certs/"+fileName
		long fileLength = new File(downLoadPath).length();
		def browserType = request.getHeader("User-Agent");
		response.setContentType("application/octet-stream");
		byte[] bytes = browserType.contains("MSIE") ? fileName.getBytes() : fileName.getBytes("utf-8");
		response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
		response.setHeader("Content-Length", String.valueOf(fileLength));

		bis = new BufferedInputStream(new FileInputStream(downLoadPath));
		bos = new BufferedOutputStream(response.getOutputStream());
		byte[] buff = new byte[2048];
		int bytesRead;
		while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
			bos.write(buff, 0, bytesRead);
		}
		bis.close();
		bos.close();
	}
	
	def downTruststore = {
		def caInstance = CA.get(params.id)
		def cerFilePath = "${grailsApplication.config.filePath.cerFilePath}"
		request.setCharacterEncoding("UTF-8");
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;

		def fileName = caInstance.cnStr+".truststore"
		String downLoadPath = cerFilePath+"certs/"+fileName
		long fileLength = new File(downLoadPath).length();
		def browserType = request.getHeader("User-Agent");
		response.setContentType("application/octet-stream");
		byte[] bytes = browserType.contains("MSIE") ? fileName.getBytes() : fileName.getBytes("utf-8");
		response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
		response.setHeader("Content-Length", String.valueOf(fileLength));

		bis = new BufferedInputStream(new FileInputStream(downLoadPath));
		bos = new BufferedOutputStream(response.getOutputStream());
		byte[] buff = new byte[2048];
		int bytesRead;
		while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
			bos.write(buff, 0, bytesRead);
		}
		bis.close();
		bos.close();
	}
	
	def apply = {
		if(!session.user){
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		def totalFlawApi = TotalFlawApi.findByTuser(session.user)
		if(totalFlawApi){
			render "only"
			return 
		}else{
			totalFlawApi = new TotalFlawApi()
			totalFlawApi.tuser = session.user
			if (totalFlawApi.save(flush: true)) {
				render "suss"
			}else{
				totalFlawApi.errors.allErrors.each{ println it }
				render "err"
			}
		}
	}
}
