package com.cnvd

import com.cnvd.utils.SQLUtil
import java.text.SimpleDateFormat
import java.util.regex.Matcher
import java.util.regex.Pattern
import com.cnvd.ability.WhiteAbility
import com.cnvd.points.Points
import com.cnvd.api.SyncFlawInfoUtil
class MainTestController {

	//提取CNVD后台库中与公安部门相关的域名系统
	def police = {
		String sql = "SELECT t.detailed_info_id FROM flaw t WHERE t.is_event = 1 AND (t.title LIKE '%警用%' OR t.title LIKE '%警官%' OR t.title LIKE '%公安%')"
		def detailIds = SQLUtil.getList(sql)
		println "detailIdS大小为=="+detailIds.size()
		int k = 1;
		for (int i=0;i<detailIds.size();i++) {
			//			println "当前ID为=="+detailIds[i].detailed_info_id
			String url = "";
			String urlstr = "";
			def theid = detailIds[i].detailed_info_id
			def deteil =  DetailedInfo.get(theid)
			if(!deteil){
				continue;
			}else{
				try {

					def description =  deteil.description
					//				def flag = description.indexOf("www")
					if(description.indexOf("http:")!=-1){
						int ss = description.indexOf("http:")
						urlstr = description.substring(ss+7,description.length())
//						println "截取后的urlstr==="+urlstr
						if(urlstr.indexOf("/")!=-1){
							int aa = urlstr.indexOf("/")
							url = urlstr.substring(0, aa)
						}
						println url
					}else{
						if(description.indexOf("www")!=-1){
							int ss = description.indexOf("http:")
							url = description.substring(ss, ss+30)
							println url
						}else{
							if(description.indexOf(".com")!=-1){
								int ss = description.indexOf(".com")
								url = description.substring(ss-30, ss+4)
								println url
							}else{
								if(description.indexOf(".cn")!=-1){
									int ss = description.indexOf(".cn")
									url = description.substring(ss-30, ss+3)
									println url
								}else{
									if(description.indexOf(".net")!=-1){
										int ss = description.indexOf(".net")
										url = description.substring(ss-30, ss+4)
										println url
									}else{
										continue;
									}
								}

							}
						}
					}
					//					//正则匹配
					//					Matcher  m = Pattern.compile("(\\w+\\.(com|net|cn))").matcher(description);
					//					while(m.find()){
					//						System.out.println(theid+"===="+k);
					//						k++;
					//					}


				} catch (Exception e) {
					println "出现错误，错误描述id============="+theid
					continue;
				}

			}
		}

	}
	
	def test = {
		String personalSql = "SELECT tuser.id,tuser.nick_name AS name,count(*) AS count,TRUNCATE(sum(points.total),2) AS sum FROM flaw,tuser,points WHERE flaw.user_id = tuser.id AND flaw.points_id = points.id AND tuser.id <> 1 AND flaw. STATUS = 9 AND flaw. ENABLE = 1 AND tuser.user_type = ? AND flaw.parent_flaw_id IS NULL GROUP BY flaw.user_id ORDER BY sum DESC LIMIT 0,10";
		def personalRes = SQLUtil.getResult(personalSql, [100200]);
		int i = 0
		personalRes.each {
			i++
			//WEB/CMS漏洞
			Float webPoints = 0
			Integer webSum = 0
			//设备漏洞
			Float shebeiPoints = 0
			Integer shebeiSum = 0
			//事件型漏洞
			Float eventPoints = 0
			Integer eventSum = 0
			//软硬件逆向分析漏洞
			Float nixiangPoints = 0
			Integer nixiangSum = 0
			
			TUser tuserInstance = TUser.get(it.id)
			//WEB/CMS漏洞:已归档 	前台上报（是	是否为事件型漏洞（否）	影响对象（web应用）
			def flawList = Flaw.findAll("from Flaw t where t.isu = 0 and t.isEvent = 0 and t.softStyleId = 29 and t.user=:user and t.enable=1",[user: tuserInstance])
			for(Flaw flawInstance:flawList){
				Points points = Points.findByFlawId(flawInstance.id)
				if(points){
					webPoints+=points.total
				}
			}
			if(flawList){
				webSum = flawList.size()
			}
			//设备漏洞		-是否已归档（已归档）-是否前台上报（是）-是否为事件型漏洞（否）-影响对象（网络设备）
			def shebeiList = Flaw.findAll("from Flaw t where t.isu = 0 and t.status = 9 and t.isEvent = 0 and t.softStyleId = 31 and t.user=:user and t.enable=1",[user: tuserInstance])
			for(Flaw flawInstance:shebeiList){
				Points points = Points.findByFlawId(flawInstance.id)
				if(points){
					shebeiPoints+=points.total
				}
			}
			if(shebeiList){
				shebeiSum = shebeiList.size()
			}
			
			//事件型漏洞 	-贡献者邮箱-是否已归档（已归档）-是否前台上报（是）-是否为事件型漏洞（是）
			def eventiList = Flaw.findAll("from Flaw t where t.isu = 0 and t.status = 9 and t.isEvent = 1 and t.user=:user and t.enable=1",[user: tuserInstance])
			for(Flaw flawInstance:eventiList){
				Points points = Points.findByFlawId(flawInstance.id)
				if(points){
					eventPoints+=points.total
				}
			}
			if(eventiList){
				eventSum = eventiList.size()
			}
			
			//软硬件逆向分析漏洞	-是否已归档（已归档）-是否前台上报（是）-是否为事件型漏洞（否）-漏洞名（通过标题中切割“逆向”两个字）检索
			//何总需求（是否已归档（已归档）-是否前台上报（是）-是否为事件型漏洞（否） ，不包含web漏洞和设备漏洞的其他漏洞）
//			def nixiangiList = Flaw.findAll("from Flaw t where t.isu = 0 and t.status = 9 and t.isEvent = 0 and t.title like '%逆向%' and t.user=:user and t.enable=1",[user: tuserInstance])
			def nixiangiList = Flaw.findAll("from Flaw t where t.isu = 0 and t.status = 9 and t.isEvent = 0  and t.user=:user and t.enable=1 and t.softStyleId != 31 and t.softStyleId != 29",[user: tuserInstance])
			for(Flaw flawInstance:nixiangiList){
				Points points = Points.findByFlawId(flawInstance.id)
				if(points){
					nixiangPoints+=points.total
				}
			}
			if(nixiangiList){
				nixiangSum = nixiangiList.size()
			}
			//创建白帽子能力展示数据
			WhiteAbility whiteAbility = new WhiteAbility()
			whiteAbility.tuser = tuserInstance
			whiteAbility.rank = i
			whiteAbility.webPoints = (float)(Math.round(webPoints*100))/100
			whiteAbility.webSum = webSum
			whiteAbility.shebeiPoints = (float)(Math.round(shebeiPoints*100))/100
			whiteAbility.shebeiSum = shebeiSum
			whiteAbility.eventPoints = (float)(Math.round(eventPoints*100))/100
			whiteAbility.eventSum = eventSum
			whiteAbility.nixiangPoints = (float)(Math.round(nixiangPoints*100))/100
			whiteAbility.nixiangSum = nixiangSum
			if(!whiteAbility.save(flush:true)){
				whiteAbility.errors.allErrors.each{ println it }
			}
			
//			println tuserInstance.nickName+"的web积分为"+webPoints+"，设备漏洞积分"+shebeiPoints+"，事件型漏洞积分"+eventPoints+"，软硬件逆向分析漏洞积分"+nixiangPoints+"+++++"
			println tuserInstance.nickName+"的web积分为"+(float)(Math.round(webPoints*100))/100+"，设备漏洞积分"+(float)(Math.round(shebeiPoints*100))/100+"，事件型漏洞积分"+(float)(Math.round(eventPoints*100))/100+"，软硬件逆向分析漏洞积分"+(float)(Math.round(nixiangPoints*100))/100+"==="
		}
		println  "finish===="
		
	}
	
	//测试全量接口
	def test1 = {
//		String reflectProductStr = "";
//		SyncFlawInfoUtil syncFlawInfoUtil = new SyncFlawInfoUtil()
//		reflectProductStr = syncFlawInfoUtil.getReflectProductStr("193163,193165,193167,193179,193180,193181,193182,193184,193186,193187,193188,193189,193190,193191,193196,193198,223508,223511,223514,223517,223520,223536,223537,223538,223539,223540,223541,223556");
//		println reflectProductStr
		SyncFlawInfoUtil flawInfoUtil = new SyncFlawInfoUtil();
		String str = flawInfoUtil.getSyncStr('2011-05-19', '2011-05-20');
//		return str
//		render(text:str,contentType:"text/xml",encoding:"UTF-8");
		println str
	}
	
	//测试积分排名
	def test2 = {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
		String date = sdf.format(new Date())
		String sql = "SELECT rank.id,rank.`NAME`,rank.count,rank.sum FROM (SELECT tuser.id,tuser.nick_name AS name,count(*) AS count,TRUNCATE(sum(points.total),2) AS sum FROM flaw,tuser,points WHERE flaw.user_id = tuser.id AND flaw.points_id = points.id AND tuser.id <> 1 AND flaw. STATUS = 9 AND flaw. ENABLE = 1 AND flaw.parent_flaw_id IS NULL GROUP BY flaw.user_id ORDER BY sum DESC ) rank WHERE rank.sum>0 "
		def rankLists = SQLUtil.getResult(sql);
		int i = 0 ;
		rankLists.each {
			String sqlcheck = "SELECT t.id FROM rank_points t WHERE t.`name` = '"+it.'NAME'+"' AND t.date_created LIKE '%"+date+"%'"
			def ids = SQLUtil.getResult(sqlcheck);
			if(!ids){
				println 1111
			}else{
			println 2222
			}
		}
	}
	def index = { }
}
