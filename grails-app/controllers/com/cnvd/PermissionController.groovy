package com.cnvd
/**
 * 服务申请
 * <AUTHOR>
 *
 */
class PermissionController {
	def jcaptchaService
	def list = {
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			redirect(controller:"TUser",action:"login")
			return
		}
		params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0

		def criteria = Permission.createCriteria()
		def permissionList = criteria.list{
			and{
				eq("tuser",session.user)
			}
			maxResults(params.max)
			firstResult(params.offset)
			order("dateCreated","desc")
		}
		def countCriteria = Permission.createCriteria()
		def permissionTotal = countCriteria.get{
			and{
				eq("tuser",session.user)
			}
			projections { rowCount() }
		}
		[PermissionList:permissionList,PermissionTotal:permissionTotal]
	}

	/**
	 * 创建服务
	 */
	def create = {
		/**
		 * 先判断是否有用户登录，如果有的话，则跳转到申请服务页面，不然跳转到登录页面
		 */
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		def permissionInstance = new Permission()
		return [PermissionInstance: permissionInstance,obj:params]
	}
	def save = {
		//token
		withForm {
			if(!session.user){
				session.originalRequestParams = [controller:controllerName, action:actionName]
				return redirect(controller: "TUser", action: "login")
			}
			if (!jcaptchaService.validateResponse("imageCaptcha", session.id, params.myCode)){
				def myCode_error = "验证码不正确"
				render(view:"create",model:[myCode_error:myCode_error])
				return
			}
			def per = Permission.findByTuserAndMapping(session.user,params.int('mapping'))
			if (per){
				def per_error = "该服务已申请,请不要重复操作!"
				render(view:"create",model:[per_error:per_error])
				return
			}
			def permissionInstance = new Permission()
			permissionInstance.mapping = params.int('mapping')
			permissionInstance.tuser = session.user
			if (permissionInstance.save(flush: true)) {
				redirect(controller:"permission",action: "list")
			}else{
				permissionInstance.errors.allErrors.each{ println it }
				render(view: "create", model: [permissionInstance: permissionInstance])
			}
		}.invalidToken {  render(view:"/error") }
	}
	def deleteper = {
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			render "error"
		}

		def permissionInstance = Permission.get(params.int('id'))
		if(permissionInstance && permissionInstance.tuser.id == session.user.id){
			permissionInstance.delete(flush:true)
			render "success"
		}else{
			render "err"
		}
	}
}
