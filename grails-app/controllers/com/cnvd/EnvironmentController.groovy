package com.cnvd


import com.cnvd.utils.CloseableHttpClientUtils
import grails.converters.JSON
import org.apache.commons.collections.map.HashedMap
class EnvironmentController {

	def list = {
		/**
		 * 查询出当前登录用户所上报的URL列表
		 */
		if(!session.user){
			session.originalRequestParams = [controller:controllerName,action:actionName]
			return redirect(controller:"TUser",action:"login")
		}
		def environmentList
		def counts
		params.max = params.max ? params.int('max') : 5
		params.offset = params.offset ? params.int('offset') : 0

		environmentList = Environment.findAllByEnable(1,[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
		counts = Environment.countByEnable(1)
		render(view:"/environment/list",model:[environmentList:environmentList, counts: counts])
		
//		environmentList = EnvironmentTask.findAllByTuserAndEnable(session.user,1,[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
//		counts = EnvironmentTask.countByTuserAndEnable(session.user,1)
//		render(view:"/environment/list",model:[environmentList:environmentList, counts: counts])
	}
	def create = {
		/**
		 * 先判断是否有用户登录，如果有的话，则跳转到URL上报页面，不然跳转到登录页面
		 */
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		def enInstance = new EnvironmentTask()
		render(view:"/environment/create",model:[enInstance:enInstance,obj:params])
	}

	def aelectedManuList = {
		params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0

//		if( params.max > 1000){
//			params.max = 1000;
//		}
//		if(params.offset > 1000){
//			params.offset = 1000;
//		}
		def manuNameList = null
		def counts = null
		manuNameList = Environment.executeQuery('select distinct vulType from Environment',[],[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
		counts = Environment.executeQuery('select count(distinct vulType) from Environment)')[0]
		render(view:'/environment/aelectedManuList',model:[manuNameList:manuNameList,manuNameTotal:counts])
	}

	def aelectedCnvdList = {
		params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0
		def cnvdList = Environment.executeQuery('select distinct cnvdNum from Environment',[],[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
		def counts = Environment.executeQuery('select count(distinct cnvdNum) from Environment)')[0]
		render(view:'/environment/aelectedCnvdList',model:[cnvdList:cnvdList,manuNameTotal:counts])
	}

	def aelectedCveList = {
		params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0
		def cveList = Environment.executeQuery('select distinct cveNum from Environment',[],[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
		def counts = Environment.executeQuery('select count(distinct cveNum) from Environment)')[0]
		render(view:'/environment/aelectedCveList',model:[cveList:cveList, manuNameTotal:counts])
	}

	def aelectedBidList = {
		params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0
		def bidList = Environment.executeQuery('select distinct bidNum from Environment',[],[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
		def counts = Environment.executeQuery('select count(distinct bidNum) from Environment)')[0]
		render(view:'/environment/aelectedBidList',model:[bidLists:bidList, manuNameTotals:counts])
	}

	def save = {
		/**
		 * 先判断是否有用户登录，如果有的话，则跳转到URL上报页面，不然跳转到登录页面
		 */
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		String vulType = params.manuName
		String cnvdName = params.cnvdName
		String cveName = params.cveName
		String bidName = params.bidName

		EnvironmentTask environmentTask = new EnvironmentTask()
		environmentTask.tuser = session.user
		environmentTask.vulType = vulType
		environmentTask.cnvdNum = cnvdName
		environmentTask.cveNum = cveName
		environmentTask.bidNum = bidName
		
		String sql = "from Environment t where 1=1 ";
		if(vulType!=null && !"".equals(vulType)){
			sql+=" and t.vulType = '"+vulType+"'"
		}
		if(cnvdName!=null && !"".equals(cnvdName)){
			sql+=" and t.cnvdNum = '"+cnvdName+"'"
		}
		if(cveName!=null && !"".equals(cveName)){
			sql+=" and t.cveNum = '"+cveName+"'"
		}
		if(bidName!=null && !"".equals(bidName)){
			sql+=" and t.bidNum = '"+bidName+"'"
		}
		println sql

		def environmentList =  Environment.executeQuery(sql)
		String key = null;
		if(environmentList){
			key = environmentList.get(0).getName()
			
			String enviStr = CloseableHttpClientUtils.getEnvironment(key)
			println enviStr
			def res = JSON.parse(enviStr);
			String taskidss = res.state.trim();
			if("success".equals(taskidss)){
				String vncurl = res.data.vncurl
				println vncurl
				
				String newStr = vncurl.replaceAll("\u0026","&");
				String dd = newStr.replaceAll("\u003d", "=");
				System.out.println(dd);
				EnvironmentReceive environmentReceive = new EnvironmentReceive()
				environmentReceive.cpuNum = res.data.cpuNum
				environmentReceive.cupSpeed = res.data.cupSpeed
				environmentReceive.createDate = res.data.createDate
				environmentReceive.description = res.data.description
				environmentReceive.imageUuid = res.data.imageUuid
				environmentReceive.instanceOfferingUuid = res.data.instanceOfferingUuid
				environmentReceive.memorySize = res.data.memorySize
				environmentReceive.name = res.data.name
				environmentReceive.platform = res.data.platform
				environmentReceive.state = res.data.state
				environmentReceive.uuid = res.data.uuid
				environmentReceive.ip = res.data.ip
				environmentReceive.vncurl = res.data.vncurl
				environmentReceive.receiveState = taskidss
				environmentReceive.remark = "success"
				environmentReceive.save(flush:true)
				environmentTask.status = 1
				environmentTask.environmentReceive = environmentReceive
				environmentTask.save(flush:true)
			}else{
				EnvironmentReceive environmentReceive = new EnvironmentReceive()
				environmentReceive.receiveState = taskidss
				environmentReceive.remark = res.data
				environmentReceive.save(flush:true)
				environmentTask.status = 0
				environmentTask.environmentReceive = environmentReceive
				environmentTask.save(flush:true)
			}
		}else{
			environmentTask.status = 0
			environmentTask.save(flush:true)
		}
		redirect(controller:"environment",action:"list")
		return
	}
	
	def createEnvi = {
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		def enviId = params.int('id')
		def environmentInstance =  Environment.get(enviId)
		def environmentTaskInstance = EnvironmentTask.findByTuserAndEnvironment(session.user,environmentInstance);
		if(!environmentTaskInstance){
			render "err"
			return
		}
		//校验是否有权限
		if(environmentTaskInstance.status==1 || environmentTaskInstance.status==2 || environmentTaskInstance.status==5){
			if(environmentInstance){
				String key = environmentInstance.getName()
				String enviStr = ""
				try {
					enviStr = CloseableHttpClientUtils.getEnvironment(key)
					println enviStr
				} catch (Exception e) {
					e.printStackTrace()
	//				enviStr = ""
					render "err"
					return
				}
				def res = JSON.parse(enviStr);
				String taskidss = res.state.trim();
				def environmentTask = EnvironmentTask.findByEnvironmentAndTuser(environmentInstance,session.user)
				if("success".equals(taskidss)){
					String vncurl = res.data.vncurl
					String newStr = vncurl.replaceAll("\u0026","&");
					String dd = newStr.replaceAll("\u003d", "=");
					System.out.println(dd);
					EnvironmentReceive environmentReceive = null
					if(!environmentTask?.environmentReceive){
						environmentReceive = new EnvironmentReceive()
					}else{
						environmentReceive = environmentTask?.environmentReceive
					}
					
	//				environmentReceive = new EnvironmentReceive()
					environmentReceive.cpuNum = res.data.cpuNum
					environmentReceive.cupSpeed = res.data.cupSpeed
					environmentReceive.createDate = res.data.createDate
					environmentReceive.description = res.data.description
					environmentReceive.imageUuid = res.data.imageUuid
					environmentReceive.instanceOfferingUuid = res.data.instanceOfferingUuid
					environmentReceive.memorySize = res.data.memorySize
					environmentReceive.name = res.data.name
					environmentReceive.platform = res.data.platform
					environmentReceive.state = res.data.state
					environmentReceive.uuid = res.data.uuid
					environmentReceive.ip = res.data.ip
					environmentReceive.vncurl = res.data.vncurl
					environmentReceive.receiveState = taskidss
					environmentReceive.remark = "success"
					environmentReceive.save(flush:true)
					environmentTask.status = 3  //创建成功
					environmentTask.tuser = session.user
					environmentTask.environment = environmentInstance
					environmentTask.environmentReceive = environmentReceive
					environmentTask.save(flush:true)
					render "suss"
				}else{
					//第一次错误，马上再创建一次
					try {
						enviStr = CloseableHttpClientUtils.getEnvironment(key)
						println enviStr
					} catch (Exception e) {
						e.printStackTrace()
						render "err"
						return
					}
					res = JSON.parse(enviStr);
					taskidss = res.state.trim();
					
					
					if("success".equals(taskidss)){
						String vncurl = res.data.vncurl
						println vncurl
						
						String newStr = vncurl.replaceAll("\u0026","&");
						String dd = newStr.replaceAll("\u003d", "=");
						System.out.println(dd);
						EnvironmentReceive environmentReceive = null
						if(!environmentTask?.environmentReceive){
							environmentReceive = new EnvironmentReceive()
						}else{
							environmentReceive = environmentTask?.environmentReceive
						}
						
						environmentReceive.cpuNum = res.data.cpuNum
						environmentReceive.cupSpeed = res.data.cupSpeed
						environmentReceive.createDate = res.data.createDate
						environmentReceive.description = res.data.description
						environmentReceive.imageUuid = res.data.imageUuid
						environmentReceive.instanceOfferingUuid = res.data.instanceOfferingUuid
						environmentReceive.memorySize = res.data.memorySize
						environmentReceive.name = res.data.name
						environmentReceive.platform = res.data.platform
						environmentReceive.state = res.data.state
						environmentReceive.uuid = res.data.uuid
						environmentReceive.ip = res.data.ip
						environmentReceive.vncurl = res.data.vncurl
						environmentReceive.receiveState = taskidss
						environmentReceive.remark = "success"
						environmentReceive.save(flush:true)
						environmentTask.status = 3  //创建成功
						environmentTask.tuser = session.user
						environmentTask.environment = environmentInstance
						environmentTask.environmentReceive = environmentReceive
						environmentTask.save(flush:true)
						render "suss"
					}else{
						EnvironmentReceive environmentReceive = null
						if(!environmentTask?.environmentReceive){
							environmentReceive = new EnvironmentReceive()
						}else{
							environmentReceive = environmentTask?.environmentReceive
						}
						environmentReceive.receiveState = taskidss
						environmentReceive.remark = res.data
						environmentReceive.save(flush:true)
						environmentTask.status = 2 //创建失败
						environmentTask.tuser = session.user
						environmentTask.environment = environmentInstance
						environmentTask.environmentReceive = environmentReceive
						environmentTask.save(flush:true)
						render "suss"
					}
				}
				
			}else{
				render "err"
			}
		}else{
			render "err"
		}
	}
	
	def apply = {
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		def enviId = params.int('id')
		def environmentInstance =  Environment.get(enviId)
		def environmentTaskInstance = EnvironmentTask.findByTuserAndEnvironment(session.user,environmentInstance);
		if(!environmentTaskInstance){
			EnvironmentTask environmentTask = new EnvironmentTask()
			environmentTask.tuser = session.user
			environmentTask.environment = environmentInstance
			environmentTask.save(flush:true)
			render "suss"
		}else{
			render "only"
		}
	}
	
	def loginVnc = {
		if(!session.user) {
			session.originalRequestParams = [controller:controllerName, action:actionName]
			return redirect(controller: "TUser", action: "login")
		}
		def enviId = params.int('id')
		def environmentInstance =  Environment.get(enviId)
		def environmentTaskInstance = EnvironmentTask.findByTuserAndEnvironment(session.user,environmentInstance);
		Map<String, String> map = new HashedMap();
		if(!environmentTaskInstance){
			map.put("err", "err")
				render map as JSON
				return
		}
		//校验是否有访问权限
		if(environmentTaskInstance.status == 3){
			if(environmentTaskInstance.environmentReceive.vncurl){
				map.put("suss", "suss")
				map.put("vnc", environmentTaskInstance.environmentReceive.vncurl)
				render map as JSON
				return
			}else{
				map.put("err", "err")
				render map as JSON
				return
			}
		}else{
			map.put("err", "err")
			render map as JSON
			return
		}
	}
}
