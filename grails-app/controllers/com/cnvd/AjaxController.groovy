package com.cnvd

import com.cnvd.utils.ImageUtil
class AjaxController {

	def cutImage = {
		def filePath = "${grailsApplication.config.filePath.userImage}/${session.user.id}/"
		//截图
		ImageUtil.cut(filePath + params.url, filePath + '1.JPEG', Integer.parseInt(params.x1), Integer.parseInt(params.y1), Integer.parseInt(params.x2), Integer.parseInt(params.y2))
		new File(filePath + params.url).delete()
		render(text: "")
	}

	def reImageName = {
		def filePath = "${grailsApplication.config.filePath.userImage}/${session.user.id}/"
		def set = new HashSet<String>();
		set.add params.url
		ImageUtil.rmFiles(filePath, set)
		
		ImageUtil.rename(filePath + params.url, filePath + '1.JPEG')
		render(text: "")
	}
}

