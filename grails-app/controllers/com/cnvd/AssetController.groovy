package com.cnvd

import com.cnvd.productInfo.Manufacturer

class AssetController {

    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]

	def aelectedManuList = {
		params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0
		
		def manuNameList = null
		def counts = null
		if(params.manuName){
			//manufacturerList = Manufacturer.findAllByNameLike('%'+params.manuName.trim()+'%',[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
			manuNameList = Asset.executeQuery('select distinct manufacturerName from Asset where manufacturerName like ?',['%'+params.manuName.trim()+'%'],[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
			//assetList = Asset.findAllByManufacturerNameLike('%'+params.manuName.trim()+'%',[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
			counts = Asset.executeQuery('select count(distinct manufacturerName) from Asset where manufacturerName like ?',['%'+params.manuName.trim()+'%'])[0]
		}else{
			//manufacturerList = Manufacturer.list(max:params.max,sort:"dateCreated",order:"desc",offset:params.offset)
			manuNameList = Asset.executeQuery('select distinct manufacturerName from Asset',[],[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
			counts = Asset.executeQuery('select count(distinct manufacturerName) from Asset)')[0]
		}
		render(view:'aelectedManuList',model:[manuNameList:manuNameList,manuNameTotal:counts])
	}
	
	def aelectedProductCategoryList = {
		println "params.manuName="+params.manuName
		println "params.pCategoryName="+params.pCategoryName
		params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0
		
		def productCategoryNameList = null
		def counts = null
		if(params.manuName){
			if(params.pCategoryName){
				productCategoryNameList = Asset.executeQuery('select distinct productCategoryName from Asset where manufacturerName = ? and productCategoryName like ?',
					[params.manuName.trim(),'%'+params.pCategoryName.trim()+'%'],[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
				counts = Asset.executeQuery('select count(distinct productCategoryName) from Asset where manufacturerName = ? and productCategoryName like ?',
					[params.manuName.trim(),'%'+params.pCategoryName.trim()+'%'])[0]
			}else{
				productCategoryNameList = Asset.executeQuery('select distinct productCategoryName from Asset where manufacturerName = ?',[params.manuName.trim()],[max:params.max,sort:"dateCreated",order:"desc",offset:params.offset])
				counts = Asset.executeQuery('select count(distinct productCategoryName) from Asset where manufacturerName = ?',[params.manuName.trim()])[0]
			}
		}else{
			render "err"
		}
		render(view:'aelectedProductCategoryList',model:[productCategoryNameList:productCategoryNameList,productCategoryNameTotal:counts])
	}
	
	def aelectedEditionList = {
		println "params.manuName="+params.manuName
		println "params.productCategoryName="+params.productCategoryName
		
		/*params.max = params.max ? params.int('max') : 10
		params.offset = params.offset ? params.int('offset') : 0*/
		
		def editionList = null
		def counts = null
		if(!params.manuName || !params.productCategoryName){
			render "err"
		}else{
			editionList = Asset.executeQuery("select distinct ast.edition from Asset ast where ast.manufacturerName = ? and ast.productCategoryName = ? "+ 
				"and ast.id not in(select asset.id from UserAsset where user = ?)",
				[params.manuName.trim(),params.productCategoryName.trim(),session.user],[sort:"dateCreated",order:"desc"])
			/*counts = Asset.executeQuery("select count(distinct edition) from Asset where manufacturerName = ? and productCategoryName = ? "+
				"and ast.id not in(select asset from UserAsset where user = ?)",
				[params.manuName.trim(),params.productCategoryName.trim(),session.user])[0]*/
		}
		render(view:'aelectedEditionList',model:[editionList:editionList])
	}
}
