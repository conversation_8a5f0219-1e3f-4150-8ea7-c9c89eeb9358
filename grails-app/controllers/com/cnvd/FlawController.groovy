package com.cnvd

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cnvd.common.Attachment
import com.cnvd.common.AttachmentPdf
import com.cnvd.common.Task
import com.cnvd.flawInfo.BaseMetric
import com.cnvd.flawInfo.EnvironmentalMetric
import com.cnvd.flawInfo.MetricInfo
import com.cnvd.flawInfo.ReferenceType
import com.cnvd.flawInfo.TemporalMetric
import com.cnvd.industryLibrary.CorporationProduct
import com.cnvd.industryLibrary.FlawIndustryProduct
import com.cnvd.industryLibrary.IndustryFlaw
import com.cnvd.flawInfo.Certificate
import com.cnvd.flawInfo.DictionaryInfo
import com.cnvd.flawInfo.ExmaineHistory
import com.cnvd.flawInfo.Exploit
import com.cnvd.flawInfo.ExploitAttachment
import com.cnvd.flawInfo.ExploitAttachmentPdf
import com.cnvd.flawInfo.FlawProcess
import com.cnvd.flawInfo.FlawProduct
import com.cnvd.flawInfo.FlawUrl
import com.cnvd.flawInfo.ReferenceInfo
import com.cnvd.patchInfo.DisposalInfo
import com.cnvd.patchInfo.PatchInfo
import com.cnvd.patchInfo.PatchInfoAttachment
import com.cnvd.points.Points
import com.cnvd.productInfo.Manufacturer
import com.cnvd.productInfo.ProductCategory
import com.cnvd.productInfo.ProductInfo
import com.cnvd.utils.*
import grails.converters.JSON
import groovy.sql.Sql
import org.apache.commons.lang.StringUtils
import org.springframework.core.io.ClassPathResource

import java.text.SimpleDateFormat
import java.util.regex.Matcher
import java.util.regex.Pattern
import com.cnvd.utils.DateUtil

class FlawController {


    def flawService
    def jcaptchaService
    def attachmentService
    def grailsApplication
    def dataSource
    def toJsonObjectService

    static allowedMethods = [save: "POST", update: "POST", delete: "POST"]

    /**
     * 密钥
     */
    def KEY = "0c4e0acf07ac97ba7edd7fb2a1936fb9";
    def properties = org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(new ClassPathResource("app-config.properties"))
    def kafkaIp = properties.getProperty("kafka.ip")

    def index = {
        def weekStartDateStr = DateTimeUtil.getPrevWeekDateStr("start")
        def weekEndDateStr = DateTimeUtil.getPrevWeekDateStr("end")
        def monthStartDateStr = DateTimeUtil.getPrevMonthDateStr("start")
        def monthEndDateStr = DateTimeUtil.getPrevMonthDateStr("end")
        def yearStartDateStr = DateTimeUtil.getPrevYearDateStr("start")
        def yearEndDateStr = DateTimeUtil.getPrevYearDateStr("end")
        /*//查询出最近归档的六条漏洞信息
		 def hql = " from Flaw where status = ? and enable = ? and openTime <= ? and isOpen=1 and parentFlaw is null order by openTime desc,title asc"
		 def latestedFlawList = Flaw.executeQuery(hql,[9,1,new Date()],[max:6,offset:0])
		 //查询出上周受关注漏洞排行榜(前5条)
		 //获得上周的开始时间和结束时间
		 def startDate = DateTimeUtil.getPrevWeekDateStr("start");
		 def endDate = DateTimeUtil.getPrevWeekDateStr("end");
		 SimpleDateFormat dateformat=new SimpleDateFormat("yyyy-MM-dd");
		 //查询上周最受关注的漏洞前5条
		 //def topConFlawSql = "select flaw_id,count(*) as concernCount from flaw_concern fc where exists(select id from flaw where status=9 and enable=1 and is_open=1 and parent_flaw_id is null and open_time>=? and open_time<? and fc.flaw_id=id) group by flaw_id  order by concernCount desc limit 5"
		 def topConFlawHql = "from Flaw where status=9 and enable=1 and isOpen=1 and parentFlaw is null and openTime>=? and openTime<? order by concernCount desc"
		 def topConFlawList = Flaw.executeQuery(topConFlawHql,[dateformat.parse(startDate),dateformat.parse(endDate)+1],[max:5,offset:0])
		 //查询出最近审核通过的补丁信息(前六条)
		 def patchHql = " from PatchInfo p where p.status = '3' and exists(select id from Flaw f where f.id=p.flaw.id and f.status = 9 and f.enable = 1 and f.openTime <= ? and f.isOpen=1 and f.parentFlaw is null) order by p.dateCreated desc"
		 def patchList = PatchInfo.executeQuery(patchHql,[new Date()],[max:7,offset:0])
		 //查询出最新的热点新闻(type=2)、安全公告(type=14)、研究报告(type=9)的前六条记录
		 def webinfoHql = "from Webinfo w where 1=1 and w.status = 1 and w.type = ? order by w.dateCreated desc"
		 //热点推荐
		 def hotRecommendList = Webinfo.findAllByIsRecommendAndStatus(1,1,[max:7,offset:0,sort:"releaseTime",order:"desc"])
		 //热点新闻
		 def hotNewsList = Webinfo.executeQuery(webinfoHql,[2],[max:7,offset:0])
		 //安全公告
		 def securityNoticeList = Webinfo.executeQuery(webinfoHql,[14],[max:6,offset:0])
		 //研究报告,周报月报知识库
		 def researchReportList = Webinfo.executeQuery(webinfoHql,[4],[max:7,offset:0])
		 render(view:"/index",model:[latestedFlawList:latestedFlawList,topConFlawList:topConFlawList,
		 prevWeekStartDate:startDate,prevWeekEndDate:endDate,patchInfoInstanceList: patchList,
		 hotRecommendList:hotRecommendList,hotNewsList:hotNewsList,securityNoticeList:securityNoticeList,
		 researchReportList:researchReportList])*/
        render(view: "/index", model: [weekStartDateStr : weekStartDateStr, weekEndDateStr: weekEndDateStr,
                                       monthStartDateStr: monthStartDateStr, monthEndDateStr: monthEndDateStr,
                                       yearStartDateStr : yearStartDateStr, yearEndDateStr: yearEndDateStr])
    }
    def index1 = {
        def weekStartDateStr = DateTimeUtil.getPrevWeekDateStr("start")
        def weekEndDateStr = DateTimeUtil.getPrevWeekDateStr("end")
        def monthStartDateStr = DateTimeUtil.getPrevMonthDateStr("start")
        def monthEndDateStr = DateTimeUtil.getPrevMonthDateStr("end")
        def yearStartDateStr = DateTimeUtil.getPrevYearDateStr("start")
        def yearEndDateStr = DateTimeUtil.getPrevYearDateStr("end")
        render(view: "/index1", model: [weekStartDateStr : weekStartDateStr, weekEndDateStr: weekEndDateStr,
                                        monthStartDateStr: monthStartDateStr, monthEndDateStr: monthEndDateStr,
                                        yearStartDateStr : yearStartDateStr, yearEndDateStr: yearEndDateStr])
    }
    def listResult = {
        def start = System.currentTimeMillis()
        params.max = Math.min(params.max ? params.int('max') : 20, 100)
        params.offset = params.offset ? params.int('offset') : 0

        def queryHqlRs = "from Flaw f where f.openTime<=:openTime and f.status = 9 and f.enable = 1 and f.isOpen=1 and f.parentFlaw is null "
        def queryHqlCo = "select count(f.id) from Flaw f where f.openTime<=:openTime and f.status = 9 and f.enable = 1 and f.isOpen=1 and f.parentFlaw is null"
        def queryHql = ""
        def queryPara = new ArrayList()
        def hqlPara = new HashMap()
        hqlPara.put("openTime", new Date())

        if (params.number && !"请输入精确编号".equals(params.number)) {
            queryHql += " and (f.number = :number or f.oldNumber = :oldNumber) "
            hqlPara.put("number", params.number)
            hqlPara.put("oldNumber", params.number)
        }
        if (params.startDate) {
            queryHql += " and f.openTime >= :openTime1 "
            hqlPara.put("openTime1", new SimpleDateFormat("yyyy-MM-dd").parse(params.startDate))
        }
        if (params.endDate) {
            queryHql += " and f.openTime < :openTime2 "
            hqlPara.put("openTime2", new SimpleDateFormat("yyyy-MM-dd").parse(params.endDate) + 1)
        }

        if (params.refenceInfo) {
            if (!"-1".equals(params.referenceScope)) {
                queryHql += " and exists(select flaw.id from ReferenceInfo where flaw.id=f.id and referenceNumber like :referenceNumber and referenceType.id = :referId )"
                hqlPara.put("referenceNumber", "%" + params.refenceInfo + "%")
                hqlPara.put("referId", params.long('referenceScope'))
            } else {
                queryHql += " and exists(select flaw.id from ReferenceInfo where flaw.id=f.id and referenceNumber like :referenceNumber )"
                hqlPara.put("referenceNumber", "%" + params.refenceInfo + "%")
            }
        } else if (!"-1".equals(params.referenceScope) && params.referenceScope != null) {
            queryHql += " and exists(select flaw.id from ReferenceInfo where flaw.id=f.id and referenceType.id = :referId )"
            hqlPara.put("referId", params.long('referenceScope'))
        }

        if (params.manufacturerId && !"-1".equals(params.manufacturerId)) {
            def manufacturer = Manufacturer.get(params.manufacturerId)
            if (params.categoryId && !"-1".equals(params.categoryId)) {
                def category = ProductCategory.get(params.categoryId)
                queryHql += " and exists(select flaw.id from FlawProduct where flaw.id=f.id and exists(select id from ProductInfo where product_id=id and name like :proName escape '\' ))"
                if (params.editionId && !"-1".equals(params.editionId)) {
                    hqlPara.put("proName", "%\\" + manufacturer.name + " " + category.name + " " + params.editionId + "%")
                } else {
                    hqlPara.put("proName", "%\\" + manufacturer.name + " " + category.name + "%")
                }
            } else {
                queryHql += " and f.manufacturer = :manu "
                hqlPara.put("manu", Manufacturer.get(params.manufacturerId))
            }
        }

        if (params.causeIdStr) {
            def str = params.causeIdStr.substring(0, params.causeIdStr.length() - 1)
            def strArr = str.split(",")
            def list = new ArrayList()
            for (int i = 0; i < strArr.length; i++) {
                list.add(Integer.parseInt(strArr[i]))
            }
            queryHql += " and f.causeId in (:causeIds) "
            hqlPara.put("causeIds", list)
        }
        if (params.threadIdStr) {
            def str = params.threadIdStr.substring(0, params.threadIdStr.length() - 1)
            def strArr = str.split(",")
            def list = new ArrayList()
            for (int i = 0; i < strArr.length; i++) {
                list.add(Integer.parseInt(strArr[i]))
            }
            queryHql += " and f.threadId in (:threadIds)"
            hqlPara.put("threadIds", list)
        }
        if (params.serverityIdStr) {
            def str = params.serverityIdStr.substring(0, params.serverityIdStr.length() - 1)
            def strArr = str.split(",")
            def list = new ArrayList()
            for (int i = 0; i < strArr.length; i++) {
                list.add(Integer.parseInt(strArr[i]))
            }
            queryHql += " and f.serverityId in (:serverityIds)"
            hqlPara.put("serverityIds", list)
        }
        if (params.positionIdStr) {
            def str = params.positionIdStr.substring(0, params.positionIdStr.length() - 1)
            def strArr = str.split(",")
            def list = new ArrayList()
            for (int i = 0; i < strArr.length; i++) {
                list.add(Integer.parseInt(strArr[i]))
            }
            queryHql += " and f.positionId in (:positionIds)"
            hqlPara.put("positionIds", list)
        }
        if (params.keyword) {
            def kwd = URLDecoder.decode(params.keyword, "UTF-8")
            if ("0".equals(params.keywordFlag)) {
                if ("1".equals(params.condition)) {
                    queryHql += " and f.title like :title escape '\' "
                    hqlPara.put("title", "%\\" + kwd + "%")
                }
                if ("0".equals(params.condition)) {
                    queryHql += " and (f.title like :title escape '\' or f.detailedInfo.description like :desc escape '\') "
                    hqlPara.put("title", "%\\" + kwd + "%")
                    hqlPara.put("desc", "%\\" + kwd + "%")
                }
            } else if ("1".equals(params.keywordFlag)) {
                if ("1".equals(params.condition)) {
                    queryHql += " or f.title like :title escape '\' "
                    hqlPara.put("title", "%\\" + kwd + "%")
                }
                if ("0".equals.(params.condition)) {
                    queryHql += " or (f.title like :title escape '\' or f.detailedInfo.description like :desc escape '\') "
                    hqlPara.put("title", "%\\" + kwd + "%")
                    hqlPara.put("desc", "%\\" + kwd + "%")
                }
            } else {
                queryHql += " and f.title like :title escape '\' "
                hqlPara.put("title", "%\\" + kwd + "%")
            }
        }

        if (params.cnvdId) {
            if ("0".equals(params.cnvdIdFlag)) {
                queryHql += " and (f.number = :num or f.oldNumber = :oldNumber) "
                hqlPara.put("num", params.cnvdId)
                hqlPara.put("oldNumber", params.cnvdId)
            } else if ("1".equals(params.cnvdIdFlag)) {
                queryHql += " or (f.number = :num or f.oldNumber = :oldNumber) "
                hqlPara.put("num", params.cnvdId)
                hqlPara.put("oldNumber", params.cnvdId)
            }
        }

        if (params.baseinfoBeanFlag && "0".equals(params.baseinfoBeanFlag)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd")
            if (params.baseinfoBeanbeginTime) {
                queryHql += " and f.openTime >= :oT1 "
                hqlPara.put("oT1", format.parse(params.baseinfoBeanbeginTime))
            }
            if (params.baseinfoBeanendTime) {
                queryHql += " and f.openTime < :oT2 "
                hqlPara.put("oT2", format.parse(params.baseinfoBeanendTime) + 1)
            }
        } else if (params.baseinfoBeanFlag && "1".equals(params.baseinfoBeanFlag)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd")
            if (params.baseinfoBeanbeginTime && params.baseinfoBeanendTime) {
                queryHql += " or (f.openTime >= :oT1 and openTime < :oT2)"
                hqlPara.put("oT1", format.parse(params.baseinfoBeanbeginTime))
                hqlPara.put("oT2", format.parse(params.baseinfoBeanendTime) + 1)
            }
            if (params.baseinfoBeanbeginTime && !params.baseinfoBeanendTime) {
                queryHql += " or f.openTime >= :oT1 "
                hqlPara.put("oT1", format.parse(params.baseinfoBeanbeginTime))
            }
            if (params.baseinfoBeanendTime && !params.baseinfoBeanbeginTime) {
                queryHql += " or f.openTime < :oT2 "
                hqlPara.put("oT2", format.parse(params.baseinfoBeanendTime) + 1)
            }
        }

        if (params.kwd) {
            queryHql += " and f.title like :tt "
            hqlPara.put("tt", "%" + params.kwd?.decodeHTML().decodeHTML() + "%")
        }

        queryHqlRs = queryHqlRs + queryHql
        queryHqlCo = queryHqlCo + queryHql

        if (params.field) {
            /*queryHqlRs += " order by :field :order "
			 hqlPara.put("field",params.field)
			 hqlPara.put("order",params.order)*/
            queryHqlRs += " order by " + params.field + " " + params.order
        } else {
            queryHqlRs += " order by openTime desc"
        }
        def flawInstanceList = Flaw.executeQuery(queryHqlRs, hqlPara, [max: params.max, offset: params.offset])
        def count = Flaw.executeQuery(queryHqlCo, hqlPara)[0]
        render(view: "list", model: [flawInstanceList: flawInstanceList, flawInstanceTotal: count])
    }
    def listBak = {
        def start = System.currentTimeMillis()
        params.max = Math.min(params.max ? params.int('max') : 20, 100)
        params.offset = params.offset ? params.int('offset') : 0

        def queryHqlRs = "from Flaw f where f.openTime<=:openTime and f.status = 9 and f.enable = 1 and f.isOpen=1 and f.parentFlaw is null "
        def queryHqlCo = "select count(f.id) from Flaw f where f.openTime<=:openTime and f.status = 9 and f.enable = 1 and f.isOpen=1 and f.parentFlaw is null"
        def queryHql = ""
        def queryPara = new ArrayList()
        def hqlPara = new HashMap()
        hqlPara.put("openTime", new Date())

        if (params.number && !"请输入精确编号".equals(params.number)) {
            queryHql += " and (f.number = :number or f.oldNumber = :oldNumber) "
            hqlPara.put("number", params.number)
            hqlPara.put("oldNumber", params.number)
        }
        if (params.startDate) {
            queryHql += " and f.openTime >= :openTime1 "
            hqlPara.put("openTime1", new SimpleDateFormat("yyyy-MM-dd").parse(params.startDate))
        }
        if (params.endDate) {
            queryHql += " and f.openTime < :openTime2 "
            hqlPara.put("openTime2", new SimpleDateFormat("yyyy-MM-dd").parse(params.endDate) + 1)
        }

        if (params.refenceInfo) {
            if (!"-1".equals(params.referenceScope)) {
                queryHql += " and exists(select flaw.id from ReferenceInfo where flaw.id=f.id and referenceNumber like :referenceNumber and referenceType.id = :referId )"
                hqlPara.put("referenceNumber", "%" + params.refenceInfo + "%")
                hqlPara.put("referId", params.long('referenceScope'))
            } else {
                queryHql += " and exists(select flaw.id from ReferenceInfo where flaw.id=f.id and referenceNumber like :referenceNumber )"
                hqlPara.put("referenceNumber", "%" + params.refenceInfo + "%")
            }
        } else if (!"-1".equals(params.referenceScope) && params.referenceScope != null) {
            queryHql += " and exists(select flaw.id from ReferenceInfo where flaw.id=f.id and referenceType.id = :referId )"
            hqlPara.put("referId", params.long('referenceScope'))
        }

        if (params.manufacturerId && !"-1".equals(params.manufacturerId)) {
            def manufacturer = Manufacturer.get(params.manufacturerId)
            if (params.categoryId && !"-1".equals(params.categoryId)) {
                def category = ProductCategory.get(params.categoryId)
                queryHql += " and exists(select flaw.id from FlawProduct where flaw.id=f.id and exists(select id from ProductInfo where product_id=id and name like :proName escape '\' ))"
                if (params.editionId && !"-1".equals(params.editionId)) {
                    hqlPara.put("proName", "%\\" + manufacturer.name + " " + category.name + " " + params.editionId + "%")
                } else {
                    hqlPara.put("proName", "%\\" + manufacturer.name + " " + category.name + "%")
                }
            } else {
                queryHql += " and f.manufacturer = :manu "
                hqlPara.put("manu", Manufacturer.get(params.manufacturerId))
            }
        }

        if (params.causeIdStr) {
            def str = params.causeIdStr.substring(0, params.causeIdStr.length() - 1)
            def strArr = str.split(",")
            def list = new ArrayList()
            for (int i = 0; i < strArr.length; i++) {
                list.add(Integer.parseInt(strArr[i]))
            }
            queryHql += " and f.causeId in (:causeIds) "
            hqlPara.put("causeIds", list)
        }
        if (params.threadIdStr) {
            def str = params.threadIdStr.substring(0, params.threadIdStr.length() - 1)
            def strArr = str.split(",")
            def list = new ArrayList()
            for (int i = 0; i < strArr.length; i++) {
                list.add(Integer.parseInt(strArr[i]))
            }
            queryHql += " and f.threadId in (:threadIds)"
            hqlPara.put("threadIds", list)
        }
        if (params.serverityIdStr) {
            def str = params.serverityIdStr.substring(0, params.serverityIdStr.length() - 1)
            def strArr = str.split(",")
            def list = new ArrayList()
            for (int i = 0; i < strArr.length; i++) {
                list.add(Integer.parseInt(strArr[i]))
            }
            queryHql += " and f.serverityId in (:serverityIds)"
            hqlPara.put("serverityIds", list)
        }
        if (params.positionIdStr) {
            def str = params.positionIdStr.substring(0, params.positionIdStr.length() - 1)
            def strArr = str.split(",")
            def list = new ArrayList()
            for (int i = 0; i < strArr.length; i++) {
                list.add(Integer.parseInt(strArr[i]))
            }
            queryHql += " and f.positionId in (:positionIds)"
            hqlPara.put("positionIds", list)
        }
        if (params.keyword) {
            def kwd = URLDecoder.decode(params.keyword, "UTF-8")
            if ("0".equals(params.keywordFlag)) {
                if ("1".equals(params.condition)) {
                    queryHql += " and f.title like :title escape '\' "
                    hqlPara.put("title", "%\\" + kwd + "%")
                }
                if ("0".equals(params.condition)) {
                    queryHql += " and (f.title like :title escape '\' or f.detailedInfo.description like :desc escape '\') "
                    hqlPara.put("title", "%\\" + kwd + "%")
                    hqlPara.put("desc", "%\\" + kwd + "%")
                }
            } else if ("1".equals(params.keywordFlag)) {
                if ("1".equals(params.condition)) {
                    queryHql += " or f.title like :title escape '\' "
                    hqlPara.put("title", "%\\" + kwd + "%")
                }
                if ("0".equals.(params.condition)) {
                    queryHql += " or (f.title like :title escape '\' or f.detailedInfo.description like :desc escape '\') "
                    hqlPara.put("title", "%\\" + kwd + "%")
                    hqlPara.put("desc", "%\\" + kwd + "%")
                }
            } else {
                queryHql += " and f.title like :title escape '\' "
                hqlPara.put("title", "%\\" + kwd + "%")
            }
        }

        if (params.cnvdId) {
            if ("0".equals(params.cnvdIdFlag)) {
                queryHql += " and (f.number = :num or f.oldNumber = :oldNumber) "
                hqlPara.put("num", params.cnvdId)
                hqlPara.put("oldNumber", params.cnvdId)
            } else if ("1".equals(params.cnvdIdFlag)) {
                queryHql += " or (f.number = :num or f.oldNumber = :oldNumber) "
                hqlPara.put("num", params.cnvdId)
                hqlPara.put("oldNumber", params.cnvdId)
            }
        }

        if (params.baseinfoBeanFlag && "0".equals(params.baseinfoBeanFlag)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd")
            if (params.baseinfoBeanbeginTime) {
                queryHql += " and f.openTime >= :oT1 "
                hqlPara.put("oT1", format.parse(params.baseinfoBeanbeginTime))
            }
            if (params.baseinfoBeanendTime) {
                queryHql += " and f.openTime < :oT2 "
                hqlPara.put("oT2", format.parse(params.baseinfoBeanendTime) + 1)
            }
        } else if (params.baseinfoBeanFlag && "1".equals(params.baseinfoBeanFlag)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd")
            if (params.baseinfoBeanbeginTime && params.baseinfoBeanendTime) {
                queryHql += " or (f.openTime >= :oT1 and openTime < :oT2)"
                hqlPara.put("oT1", format.parse(params.baseinfoBeanbeginTime))
                hqlPara.put("oT2", format.parse(params.baseinfoBeanendTime) + 1)
            }
            if (params.baseinfoBeanbeginTime && !params.baseinfoBeanendTime) {
                queryHql += " or f.openTime >= :oT1 "
                hqlPara.put("oT1", format.parse(params.baseinfoBeanbeginTime))
            }
            if (params.baseinfoBeanendTime && !params.baseinfoBeanbeginTime) {
                queryHql += " or f.openTime < :oT2 "
                hqlPara.put("oT2", format.parse(params.baseinfoBeanendTime) + 1)
            }
        }

        if (params.kwd) {
            queryHql += " and f.title like :tt "
            hqlPara.put("tt", "%" + params.kwd?.decodeHTML().decodeHTML() + "%")
        }

        queryHqlRs = queryHqlRs + queryHql
        queryHqlCo = queryHqlCo + queryHql

        if (params.field) {
            /*queryHqlRs += " order by :field :order "
			 hqlPara.put("field",params.field)
			 hqlPara.put("order",params.order)*/
            queryHqlRs += " order by " + params.field + " " + params.order
        } else {
            queryHqlRs += " order by openTime desc"
        }
        def flawInstanceList = Flaw.executeQuery(queryHqlRs, hqlPara, [max: params.max, offset: params.offset])
        def count = Flaw.executeQuery(queryHqlCo, hqlPara)[0]
        render(view: "list", model: [flawInstanceList: flawInstanceList, flawInstanceTotal: count])
    }

    //漏洞管理列表-new
    def list = {
        Date d1 = new Date()
        String sd1 = DateUtil.get4yMdHms(d1)
        String actionName = "漏洞管理-前台"
        def ip = flawService.getIpAddr(request);
        log.info("|" + sd1 + "|" + actionName + "|webCrawler|url=flaw/list|ip=" + ip);
        if (ip != null) {
            boolean bool = flawService.reptile(ip, "flaw/list");
            log.info("|" + sd1 + "|webCrawler|url=flaw/list|ip=" + ip + "|bool=" + bool);
            if (!bool) {
                render(view: "/error");
                return;
            }
        }
        params.numPerPage = Math.min(params.max ? params.int('max') : 10, 100)
        params.offset = params.offset ? params.int('offset') : 0
        Integer actionType = 0 //0-漏洞管理 1-一级审核 2-二级审核 3-三级审核

        //设置默认值
        params.keywordFlag = "0";
        params.cnvdIdFlag = "0";
        params.baseinfoBeanFlag = "0";

        //log.info("----------" + sd1 + "|"+actionName+"查询数据start----------ip="+ip)

        log.info("|" + sd1 + "|" + actionName + "参数params|" + params.toString() + "|ip=" + ip);
        List<Map<String, Object>> flawInstanceList = flawService.getList(params, actionType) //获取list集合
        Long flawInstanceTotal = flawService.getCount(params, actionType) //获取总记录数

        Date d2 = new Date()
        double differDate = DateUtil.getDifTwoTime(d2.getTime(), d1.getTime(), "S")
        log.info("|" + sd1 + "|" + actionName + "查询一共用时=" + differDate + "|ip=" + ip)

        Map<String, Integer> flawDataMap = flawService.getFlawCount(params, actionType) //获取漏洞处理信息记录数

        //查询完之后的操作
        render(view: "list", model: [flawInstanceList: flawInstanceList, flawInstanceTotal: flawInstanceTotal, flawDataMap: flawDataMap, params: params])
    }

    /**
     * 漏洞分类列表
     */
    def typelist = {}

    def typeResult = {
        def start = System.currentTimeMillis()
        params.max = Math.min(params.max ? params.int('max') : 20, 100)
        params.offset = params.offset ? params.int('offset') : 0
        def hql = " from Flaw where status =?  and enable = ? and openTime<=? and softStyleId = ? and isOpen=1 and parentFlaw is null order by "
        if (params.field) {
            hql += params.field + " " + params.order
        } else {
            hql += "openTime desc"
        }
        def coutHql = "select count(id) as count from Flaw where status =?  and enable = ? and openTime<=? and softStyleId = ? and isOpen=1 and parentFlaw is null"
        def flawInstanceList = Flaw.executeQuery(hql, [
                9,
                1,
                new Date(),
                params.int('typeId')
        ], [max: params.max, offset: params.offset])
        def count = Flaw.executeQuery(coutHql, [
                9,
                1,
                new Date(),
                params.int('typeId')
        ])[0]
        [flawInstanceList: flawInstanceList, flawInstanceTotal: count]
    }

    def save = {
        //token
        withForm {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            SimpleDateFormat sd = new SimpleDateFormat("yyyy")
            //println params
            def ss = params.title
            if (!session.user) {
                session.originalRequestParams = [controller: controllerName, action: actionName]
                return redirect(controller: "TUser", action: "login")
            }
            if (!params.foundTimes) {
                def foundTime_error = "请输入发现日期"
                render(view: "create", model: [foundTime_error: foundTime_error])
                return
            }
            if (!params.discovererName) {
                def discovererName_error = "请输入发现者"
                render(view: "create", model: [discovererName_error: discovererName_error])
                return
            }
            if (!params.title || !params.titlel) {
                def title_error = "请输入规范的漏洞名称"
                render(view: "create", model: [title_error: title_error])
                return
            }

            /*    if(!params.url){
                def url_error = "请输入漏洞Url"
                render(view:"create",model:[url_error:url_error])
                return
            }*/

            /*   if(!params.description1){
                def description1_error = "请输入漏洞描述"
                render(view:"create",model:[description1_error:description1_error])
                return
            }*/

            /*   if(!params.tempWay){
                def tempWay_error = "请输入临时解决方案"
                render(view:"create",model:[tempWay_error:tempWay_error])
                return
             }*/

            if (!params.myCode) {
                def myCode_error = "请输入验证码"
                render(view: "create", model: [myCode_error: myCode_error])
                return
            }
            if (params.myCode.toLowerCase() != session.validateCode) {
                def myCode_error = '验证码不正确'
                render(view: "create", model: [myCode_error: myCode_error])
                return
            }

            //事件型漏洞和通用型漏洞的校验
            if ("1".equals(params.isEvent)) {
                if (!params.param_province) {

                    def param_province_error = "请选择漏洞所在省份"
                    render(view: "create", model: [param_province_error: param_province_error])
                    return
                }
                if (!params.param_city) {
                    def param_city_error = "请选择漏洞所在城市"
                    render(view: "create", model: [param_city_error: param_city_error])
                    return
                }

                if (!params.unitName) {
                    def unitName_error = "请选择漏洞的所属单位"
                    render(view: "create", model: [unitName_error: unitName_error])
                    return
                }

            } else {
                if (!params.description) {
                    def description1_error = "请输入漏洞描述"
                    render(view: "create", model: [description1_error: description1_error])
                    return
                }
                /*  if(!params.formalWay){
                    def formalWay_error = "请输入正式解决方案"
                    render(view:"create",model:[formalWay_error:formalWay_error])
                    return
                }*/
                if (!params.manuName) {
                    def manuName_error = "请输入厂商信息"
                    render(view: "create", model: [manuName_error: manuName_error])
                    return
                }
                if (!params.productCategoryName) {
                    def productCategoryName_error = "请输入产品信息"
                    render(view: "create", model: [productCategoryName_error: productCategoryName_error])
                    return
                }
                if (!params.edition) {
                    def edition_error = "请输入产品版本"
                    render(view: "create", model: [edition_error: edition_error])
                    return
                }
            }
            //创建漏洞

            def flawInstance = new Flaw()
            flawInstance.isOriginal = 0 //默认原创设为是
            flawInstance.isEvent = params.getInt("isEvent")
            flawInstance.isProductComponents = params.get("isProductComponents", "0")
            /*  String event = params.isEvent
		    if(event.contains(",")){
				String[] isEvent_currencyType = event.split(",")
				flawInstance.isEvent = 0
				flawInstance.currencyType = java.lang.Integer.parseInt(isEvent_currencyType[1])
			}else{
				flawInstance.isEvent = 1
			}*/

            if (params.url) {
                Calendar c = Calendar.getInstance();
                c.setTime(new Date());
                c.add(Calendar.YEAR, -1);
                Date y = c.getTime();
                String year = sd.format(y);
                String errStr = ""
                int urlFlag = params.url.trim().indexOf(",")
                if (urlFlag != -1) {
                    String[] urlArr = params.url.trim().split(",")
                    int i = 0;
                    for (String url : urlArr) {
                        //判断url是否以http和https开头
                        def regEx = /^http:\/\/.*/;    //https网址正则
                        def _regEx = /^https:\/\/.*/;    //http
                        // 编译正则表达式
                        Pattern pattern = Pattern.compile(regEx);
                        Matcher matcher = pattern.matcher(url.trim());
                        Pattern _pattern = Pattern.compile(_regEx);
                        Matcher _matcher = _pattern.matcher(url.trim());
                        // 字符串是否与正则表达式相匹配
                        boolean rs = matcher.matches();
                        boolean _rs = _matcher.matches();
                        if (!rs && !_rs) {
                            def url_error = "本条漏洞的【" + url + "】url不符合上报规范，无法重复填写。"
                            if (flawInstance.isEvent == 0) {
                                render(view: "create", model: [url1_error: url_error])
                            } else {
                                render(view: "create", model: [url_error: url_error])
                            }

                            return
                        }
                        if (!url.trim().equals("")) {
                            //先判断是否为事件型漏洞
                            if ("1".equals(params.isEvent)) {
                                FlawUrl flawUrl = FlawUrl.find("from FlawUrl where url = :url ", [url: url.trim()]);
                                if (flawUrl && flawUrl.flaw.dateCreated > y && flawUrl.flaw.status != -1 && flawUrl.flaw.status != -2 && params.getInt("isEvent") == flawUrl.flaw.isEvent) {
                                    errStr += url.trim()
                                    /*if(i<urlArr.length-1){
										errStr+=","
									}*/
                                    flawInstance.isRepeat = 1;
                                }
                            } else {
                                def flawUrlList = FlawUrl.findAll("from FlawUrl where url = :url ", [url: url.trim()]);
                                if (flawUrlList) {
                                    for (FlawUrl flawUrl : flawUrlList) {
                                        if (flawUrl.flaw.status != -1 && flawUrl.flaw.status != -2 && params.getInt("isEvent") == flawUrl.flaw.isEvent) {
                                            /*errStr+=url.trim()
											if(i<urlArr.length-1){
												errStr+=","
											}*/
                                            flawInstance.isRepeat = 1;
                                        }
                                    }
                                }
                            }
                        }
                        i++;
                    }
                } else {
                    //判断url是否以http和https开头
                    def regEx = /^http:\/\/.*/;    //https网址正则
                    def _regEx = /^https:\/\/.*/;    //http
                    // 编译正则表达式
                    Pattern pattern = Pattern.compile(regEx);
                    Matcher matcher = pattern.matcher(params.url.trim());
                    Pattern _pattern = Pattern.compile(_regEx);
                    Matcher _matcher = _pattern.matcher(params.url.trim());
                    // 字符串是否与正则表达式相匹配
                    boolean rs = matcher.matches();
                    boolean _rs = _matcher.matches();
                    if (!rs && !_rs) {
                        def url_error = "本条漏洞的【" + params.url.trim() + "】url不符合上报规范，无法重复填写。"
                        if (flawInstance.isEvent == 0) {
                            render(view: "create", model: [url1_error: url_error])
                        } else {
                            render(view: "create", model: [url_error: url_error])
                        }
                        return
                    }
                    if ("1".equals(params.isEvent)) {
                        FlawUrl flawUrl = FlawUrl.find("from FlawUrl where url = :url ", [url: params.url.trim()]);
                        if (flawUrl && flawUrl.flaw.dateCreated > y && flawUrl.flaw.status != -1 && flawUrl.flaw.status != -2 && params.getInt("isEvent") == flawUrl.flaw.isEvent) {
                            //errStr = params.url.trim()
                            flawInstance.isRepeat = 1;
                        }
                    } else {
                        FlawUrl flawUrl = FlawUrl.find("from FlawUrl where url = :url ", [url: params.url.trim()]);
                        if (flawUrl) {
                            if (flawUrl.flaw.status != -1 && flawUrl.flaw.status != -2 && params.getInt("isEvent") == flawUrl.flaw.isEvent) {
                                //errStr = params.url.trim()
                                flawInstance.isRepeat = 1;
                            }
                        }
                    }
                }
                /*	if(!"".equals(errStr)){
					def url_error = "本条漏洞的【"+errStr+"】url已存在，无法重复上报。"
					if (flawInstance.isEvent==0){
						render(view:"create",model:[url1_error:url_error])
					}else{
						render(view:"create",model:[url_error:url_error])
					}
					return
				}*/
            }


            //公共属性
            flawInstance.foundTime = sdf.parse(params.foundTimes)
            flawInstance.submitTime = new Date()
            /**
             * 添加上漏洞上报人的信息
             */
            flawInstance.user = session.user
            flawInstance.discovererName = params.discovererName
            String titleStr = ""
            //查询漏洞类型,不为null添加进去漏洞ylx
            def flawTypesValueId = params.titlel
            FlawTypes flawTypes = FlawTypes.findByValueId(flawTypesValueId)
            if (flawTypes != null) {
                flawInstance.flawTypes = flawTypes
                titleStr = flawTypes.name
            }
            /*	def ff = params.titlel
                switch(params.titlel) {
                    case '0':titleStr = 'SQL注入'; break;
                    case '1':titleStr = 'XSS'; break;
                    case '2':titleStr = 'CSRF'; break;
                    case '3':titleStr = 'SSRF'; break;
                    case '4':titleStr = '智能硬件'; break;
                    case '5':titleStr = '弱口令'; break;
                    case '6':titleStr = '文件上传'; break;
                    case '7':titleStr = '信息泄露'; break;
                    case '8':titleStr = '越权访问'; break;
                    case '9':titleStr = '逻辑缺陷'; break;
                    case '10':titleStr = '文件包含'; break;
                    case '11':titleStr = '代码执行'; break;
                    case '12':titleStr = '命令执行'; break;
                    case '13':titleStr = '解析漏洞'; break;
                    case '14':titleStr = '目录遍历'; break;
                    case '15':titleStr = '任意文件下载'; break;
                    case '16':titleStr = '任意文件读取'; break;
                    case '17':titleStr = 'xml实体注入'; break;
                    case '18':titleStr = '拒绝服务'; break;
                    default:titleStr = '其他'; break;
                }*/


            String title = EncryptUtils.aesDecrypt(params.title, KEY) + "存在" + titleStr;//y123
            //String title =params.title  + "存在" + titleStr;//y123
            if (!"漏洞".equals(titleStr.substring(titleStr.length() - 2, titleStr.length()))) {
                title = title + "漏洞";
            }
            println "title|" + title
            flawInstance.title = title
            //添加漏洞所属省份
            flawInstance.province = params.param_province
            //添加漏洞所属城市
            flawInstance.city = params.param_city
            //添加漏洞所属IP
            flawInstance.flowIP = params.flowIP
            //添加事件型漏洞的类型
            flawInstance.flowType = params.flowType
            //添加漏洞单位
            flawInstance.unitName = params.unitName
            if (params.unitName) {
                Manufacturer manufact = Manufacturer.findByName(params.unitName)
                if (manufact != null && StringUtils.isNotBlank(manufact.Unit)) {
                    //添加漏洞单位的行业
                    flawInstance.ministriesName = manufact.Unit
                }
            }

            //设置默认项
            flawInstance.status = 1
            flawInstance.isAttShow = 0

            //默认公开，公开时间默认 创建时间加45天
            if (params.int("isOpen") == 1 && params.int("isEvent") == 0) {
                flawInstance.isOpen = 1
                Calendar c2 = Calendar.getInstance();
                c2.setTime(new Date());
                c2.add(Calendar.DATE, 45);
                Date y2 = c2.getTime();
                flawInstance.openTime = y2
                flawInstance.intendOpenTime = y2
            } else {
                flawInstance.isOpen = 0
                Calendar c1 = Calendar.getInstance();
                c1.setTime(new Date());
                c1.add(Calendar.YEAR, 10);
                Date y1 = c1.getTime();
                flawInstance.openTime = y1
                flawInstance.intendOpenTime = y1
            }


            flawInstance.positionId = 21  //漏洞利用的攻击位置 21:远程
            //flawInstance.softStyleId = 29  //漏洞影响对象类型 29:WEB应用漏洞
            flawInstance.softStyleId = java.lang.Integer.parseInt(params.softStyleId) //漏洞影响对象类型666

            flawInstance.threadId = 13 //漏洞引发的威胁 13:未授权的信息泄露
            //上传漏洞附件并设置flawInstance的attachment属性
            def file = request.getFile("flawAttFile")
            if (file != null && !"".equals(file.getOriginalFilename())) {
                //判断上传附件的格式
                def flag = attachmentService.checkFile(file)
                if (flag) {
                    //附件符合格式
                    String filePath = "${grailsApplication.config.filePath.flawAttFilePath}"
                    String realName = CommentsUtil.getCurrentTime() //文件的真实文件名
                    def attachment = attachmentService.uploadFile(file, filePath, realName)
                    flawInstance.attachment = attachment
                } else {
                    //附件不符合格式
                    def fileLimit_error = "附件类型不正确或附件大小为0,请重新上传！"
                    if (flawInstance.isEvent == 0) {
                        render(view: 'create', model: [flawAttFile1_error: fileLimit_error])
                        return
                    } else {
                        render(view: 'create', model: [flawAttFile_error: fileLimit_error])
                        return
                    }

                }
            }
            String remarks = ""
            String manuUrl = params.changshang
            ProductInfo productInfo;
            if (manuUrl) {
                remarks = manuUrl + "\r\n"
            }

            //事件型漏洞与通用型漏洞不同的属性
            if ("1".equals(params.isEvent)) {
                def urlValue = "";
                //url录入
                int urlFlag = params.url.trim().indexOf(",")
                if (urlFlag != -1) {
                    String[] urlArr = params.url.trim().split(",")
                    int i = 0;
                    for (String url : urlArr) {
                        if (!"".equals(url.trim())) {
                            if ("".equals(urlValue)) {
                                urlValue = url.trim();
                            } else {
                                urlValue = urlValue + "\r\n" + url.trim();
                            }
                        }
                    }
                } else {
                    if (!"".equals(params.url.trim())) {
                        urlValue = params.url.trim();
                    }
                }
                //事件型漏洞
                def d = new DetailedInfo()
                d.description = EncryptUtils.aesDecrypt(params.description, KEY)
                if (!"".equals(urlValue)) {
                    d.description = d.description + "\r\n" + urlValue;
                }
                if (params.weakPasswordAccountNumber) {
                    d.description = d.description + "\r\n" + "弱口令账号：" + params.weakPasswordAccountNumber.trim();
                }
                if (params.weakPasswordPwd) {
                    d.description = d.description + "\r\n" + "弱口令密码：" + params.weakPasswordPwd.trim();
                }
                d.tempWay = params.tempWay
                flawInstance.detailedInfo = d
            } else {
                //通用型漏洞
//				flawInstance.url = params.url
                def d = new DetailedInfo()
                d.description = EncryptUtils.aesDecrypt(params.description, KEY)
                //d.description=params.description
                d.tempWay = params.tempWay
                d.formalWay = params.formalWay
                flawInstance.detailedInfo = d
                //通用型漏洞的资产绑定
                //厂商产品版本的选择
                String manufacturerTxtarea = params.manuName
                String categoryTxtarea = params.productCategoryName
                String editionTxtarea = params.edition

                String manufacturerId = params.manuId
                String categoryId = params.productCategoryId
                String editionId = params.editionId

                Manufacturer manufacturer = Manufacturer.get(manufacturerId)
                ProductCategory productCategory = ProductCategory.get(categoryId)
                if (editionId) {
                    productInfo = ProductInfo.get(editionId)
                    if (!Tools.cleanXSS(productInfo.edition).equals(editionTxtarea)) {
                        productInfo = ProductInfo.findByProductCategoryAndEdition(productCategory, editionTxtarea)
                    }
                } else {
                    productInfo = ProductInfo.findByProductCategoryAndEdition(productCategory, editionTxtarea)
                }
                //备注属性，如果资产信息不符合数据库，则用于审核人员作参考
                //判断资产信息是否与库中一致
                if (!productInfo) {
                    remarks += "参考资产信息为：\r\n" + manufacturerTxtarea + "\r\n" + categoryTxtarea + "\r\n" + editionTxtarea
                } else {
                    if (Tools.cleanXSS(productInfo.manufacturer.name).equals(manufacturerTxtarea) && Tools.cleanXSS(productInfo.productCategory.name).equals(categoryTxtarea) && Tools.cleanXSS(productInfo.edition).equals(editionTxtarea)) {
                        remarks += ""
                    } else {
                        remarks += "参考资产信息为：\r\n" + manufacturerTxtarea + "\r\n" + categoryTxtarea + "\r\n" + editionTxtarea
                    }
                }
                flawInstance.remark = remarks
            }
            try {


                if (flawInstance.save(flush: true)) {

                    flawInstance.parentId = flawInstance.id
                    flawInstance.save(flush: true)


                    def dictionaryInfo = DictionaryInfo.findById(Long.valueOf(params.softStyleId))
                    if (dictionaryInfo?.name == '区块链公链' || dictionaryInfo?.name == '区块链联盟链' || dictionaryInfo?.name == '区块链外围系统') {

                        net.sf.json.JSONObject jsonImp = new net.sf.json.JSONObject()
                        jsonImp.put("time", DateUtil.get4yMdHms(new Date()))
                        def array = new net.sf.json.JSONArray()
                        flawInstance.setPush(1)
                        flawInstance.save(true)
                        flawAbout(flawInstance, array, 0, 0, 0)
                        sendMsg(flawInstance, jsonImp, array)

                        // 推送附件
                        /*try {
							String attachPath = flawInstance.attachment.path
							String subUrl = "${grailsApplication.config.filePath.flawAttachPushUrl}"
							HttpUtilNew.inputStreamUpload(subUrl,attachPath)
						} catch (Exception e) {
							//异常继续执行，保持原有逻辑
							e.printStackTrace();
						}*/

                    }


                    if (flawInstance != null && flawInstance.push == 2) {

                        def str = '{"message":"推送失败","statusCode":"200"}';
                        render(text: str, contentType: "text/plain", encoding: "UTF-8")

                    }

                    //漏洞-漏洞类型-漏洞类型参数中间表的添加

                    if (params.sqlInjectionVulnerabilityType) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("sqlInjectionVulnerabilityType")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.sqlInjectionVulnerabilityType
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.sqlInjectionVulnerabilityUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("sqlInjectionVulnerabilityUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.sqlInjectionVulnerabilityUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.sqlInjectionVulnerabilitySqlMap) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("sqlInjectionVulnerabilitySqlMap")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.sqlInjectionVulnerabilitySqlMap
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.xmlEntityInjectionUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("xmlEntityInjectionUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.xmlEntityInjectionUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.xssVulnerabilityUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("xssVulnerabilityUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.xssVulnerabilityUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.xssVulnerabilityPayload) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("xssVulnerabilityPayload")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.xssVulnerabilityPayload
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.ssrfVulnerabilityUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("ssrfVulnerabilityUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.ssrfVulnerabilityUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.weakPasswordUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("weakPasswordUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.weakPasswordUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.weakPasswordAccountNumber) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("weakPasswordAccountNumber")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.weakPasswordAccountNumber
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.weakPasswordPwd) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("weakPasswordPwd")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.weakPasswordPwd
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.fileUploadVulnerabilityUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("fileUploadVulnerabilityUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.fileUploadVulnerabilityUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.informationLeakageUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("informationLeakageUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.informationLeakageUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.unauthorizedAccessUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("unauthorizedAccessUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.unauthorizedAccessUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.logicalDefectsName) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("logicalDefectsName")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.logicalDefectsName
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.logicalDefectsUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("logicalDefectsUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.logicalDefectsUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.fileContainsUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("fileContainsUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.fileContainsUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.remoteCommandExecutionType) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("remoteCommandExecutionType")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.remoteCommandExecutionType
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.remoteCommandExecutionMiddleware) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("remoteCommandExecutionMiddleware")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.remoteCommandExecutionMiddleware
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }


                    if (params.remoteCommandExecutionFlaw) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("remoteCommandExecutionFlaw")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.remoteCommandExecutionFlaw
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.remoteCommandExecutionS2) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("remoteCommandExecutionS2")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.remoteCommandExecutionS2
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.remoteCommandExecutionUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("remoteCommandExecutionUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.remoteCommandExecutionUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.remoteCommandExecutionTool) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("remoteCommandExecutionTool")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.remoteCommandExecutionTool
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.directoryTraversalUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("directoryTraversalUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.directoryTraversalUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.downloadAnyFilesUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("downloadAnyFilesUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.downloadAnyFilesUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.anyFileReadUrl) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("anyFileReadUrl")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.anyFileReadUrl
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.denialOfServicePoc) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("denialOfServicePoc")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.denialOfServicePoc
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.denialOfServiceTriggerProcess) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("denialOfServiceTriggerProcess")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.denialOfServiceTriggerProcess
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.binaryVulnerabilityVersion) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("binaryVulnerabilityVersion")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.binaryVulnerabilityVersion
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.binaryVulnerabilityTriggerPosition) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("binaryVulnerabilityTriggerPosition")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.binaryVulnerabilityTriggerPosition
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.binaryVulnerabilityPoc) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("binaryVulnerabilityPoc")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.binaryVulnerabilityPoc
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.deviceVulnerabilityTriggerPosition) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("deviceVulnerabilityTriggerPosition")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.deviceVulnerabilityTriggerPosition
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.deviceVulnerabilityPoc) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("deviceVulnerabilityPoc")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.deviceVulnerabilityPoc
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.otherPoc) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("otherPoc")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.otherPoc
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    if (params.otherOther) {
                        FlawTypesParam flawTypesParam = FlawTypesParam.findByValueId("otherOther")
                        FlawTypesParamMiddle flawTypesParamMiddle = new FlawTypesParamMiddle()
                        flawTypesParamMiddle.Flaw = flawInstance
                        flawTypesParamMiddle.flawTypes = flawTypes
                        flawTypesParamMiddle.flawTypesParam = flawTypesParam
                        flawTypesParamMiddle.param_values = params.otherOther
                        if (flawTypesParamMiddle.save(flush: true)) {
                        } else {
                            flawTypesParamMiddle.errors.allErrors.each { println it }
                            render(view: "create", model: [flawTypesParamMiddle: flawTypesParamMiddle])
                        }
                    }

                    //---------------------------------ylx---------------------------------
                    //资产的录入
                    if (productInfo) {
                        FlawProduct flawProduct = new FlawProduct()
                        flawProduct.flaw = flawInstance
                        flawProduct.product = productInfo
                        if (!flawProduct.save(flush: true)) {
                            flawProduct.errors.allErrors.each { println it }
                        }
                    }
                    //url录入
                    int urlFlag = params.url.trim().indexOf(",")
                    if (urlFlag != -1) {
                        String[] urlArr = params.url.trim().split(",")
                        int i = 0;
                        for (String url : urlArr) {
                            if (!"".equals(url.trim())) {
                                //多条url循环入库
                                FlawUrl fu = new FlawUrl()
                                fu.flaw = flawInstance
                                fu.url = url.trim()
                                if (!fu.save(flush: true)) {
                                    fu.errors.allErrors.each { println it }
                                }
                                String urls = url.toLowerCase();
                                //保存漏洞url到redis
                                flawService.setFlawUrl(flawInstance.getId(), urls.trim())
                            }
                        }
                    } else {
                        if (!"".equals(params.url.trim())) {
                            //单条url入库
                            FlawUrl fu = new FlawUrl()
                            fu.flaw = flawInstance
                            fu.url = params.url.trim()
                            if (!fu.save(flush: true)) {
                                fu.errors.allErrors.each { println it }
                            }

                            String url = params.url.toLowerCase();
                            //保存漏洞url到redis
                            flawService.setFlawUrl(flawInstance.getId(), url.trim())
                        }
                    }

                    //把上报的漏洞信息发送邮件到*******************
                    def flawSendEmail = "${grailsApplication.config.grails.mail.flawSendEmail}"
//				def flawSendEmail = "<EMAIL>"
                    def dateCreated = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(flawInstance?.dateCreated)
                    new Mail(fromEmail: null, toEmail: "${flawSendEmail}",
                            title: "用户${flawInstance?.user?.nickName}在${dateCreated}上报的漏洞(${flawInstance?.title})基本信息",
                            content: "type#:reportFlaw#,flawId#:${flawInstance?.id}", isMust: 1, isHandled: 0, createDate: new Date()).save()

                    def flawUpdatedInstance = new FlawUpdated()
                    flawUpdatedInstance.flaw = flawInstance
                    flawUpdatedInstance.type = 0
                    flawUpdatedInstance.save(flush: true)
                    redirect(controller: "user", action: "reportManage", id: flawInstance.id)
                } else {
                    flawInstance.errors.allErrors.each { println it }
                    render(view: "create", model: [flawInstance: flawInstance])
                }
            } catch (Exception e) {
                e.printStackTrace()
            }
        }.invalidToken {
            println 88888
            render(view: "/error")
        }
    }

    /**
     * 验证码校验
     *
     */
    def validateCode = {
        def myCode_error = true
        if (params.myCode.toLowerCase() != session.validateCode) {
            myCode_error = false
            render myCode_error
            return
        } else {
            render myCode_error
            return
        }
    }

    /**
     * 验证url是否重复
     *
     */
    def checkUrl = {
        SimpleDateFormat sd = new SimpleDateFormat("yyyy")
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
        def url = params.url.trim()
        def isEvent = params.isEvent
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.YEAR, -1);
        Date y = c.getTime();
        String year = sdf.format(y);
        if ("".equals(url)) {
            render 1
            return
        }
        if (params.int('isEvent') == 1) {
            def flawUrlList = FlawUrl.findAll("from FlawUrl where url = :url ", [url: url]);
            if (!flawUrlList) {
                render 1
                return
            }
            for (FlawUrl fu : flawUrlList) {
                if (fu.flaw.dateCreated) {
                    Date f_date = fu.flaw.dateCreated
                    if (f_date >= y && fu.flaw.status != -2 && fu.flaw.status != -1) {
                        render url
                        return
                    } else {
                        render 1
                        return
                    }
                }
            }
        } else {
            FlawUrl flawUrl = FlawUrl.find("from FlawUrl where url = :url ", [url: url]);
            if (flawUrl && flawUrl.flaw.status != -2 && flawUrl.flaw.status != -1) {
                render url
                return
            } else {
                render 1
                return
            }
        }
    }

    def newNumShow = {
        def start = System.currentTimeMillis()
        session.originalRequestParams = [controller: controllerName, action: actionName, id: params.id]
        def flawInstance
        if (params.id.startsWith("CNVD")) {
            flawInstance = Flaw.findByNumber(params.id)
        } else {
            flawInstance = Flaw.get(params.int('id'))
        }
        if (!flawInstance || flawInstance.status != 9 || flawInstance.isOpen != 1
                || flawInstance.enable != 1 || new Date().compareTo(flawInstance.openTime) < 0
                || flawInstance.parentFlaw) {
            render(view: "/error")
        } else {
            /**
             * 查询出当前漏洞的所有评论的集合
             */
            def time1 = System.currentTimeMillis()
            String hql = " from FlawComments where flaw = ? and status = ? order by dateCreated desc "
            def flawCommentList = FlawComments.executeQuery(hql, [flawInstance, 0])

            /**
             *  查询影响此漏洞影响产品的厂商的漏洞前10条，按照open_time desc排序
             */
            def time2 = System.currentTimeMillis()
            def flawProductList = FlawProduct.findAllByFlaw(flawInstance)//漏洞影响的所有产品
            def time3 = System.currentTimeMillis()
            def manufacturer
            def res
            if (flawProductList) {
                manufacturer = flawProductList[0].product.manufacturer
                def sql = "select title,number from flaw f, " +
                        "(select distinct flaw_id from flaw_product fp, product_info pi where fp.product_id = pi.id and pi.manufacturer_id = ? and flaw_id <> ?) f2 " +
                        "where f.id = f2.flaw_id and f.status=9 and f.enable=1 and f.open_time<? " +
                        "and f.is_open=1 and f.parent_flaw_id is null order by f.open_time desc limit 10"
                def paramList = new ArrayList()
                paramList.add(manufacturer.id)
                paramList.add(flawInstance.id)
                paramList.add(new Date())
                res = new groovy.sql.Sql(dataSource).rows(sql, paramList)
            }
            def time4 = System.currentTimeMillis()
            def clickNum = (flawInstance.clickNum ? flawInstance.clickNum : 0) + 1
            def sql = "update flaw set click_num = ? where id = ?"
            new groovy.sql.Sql(dataSource).executeUpdate(sql, [clickNum, flawInstance?.id])
            render(view: "show", model: [flawInstance: flawInstance, flawCommentList: flawCommentList, flawProductList: flawProductList, res: res])
        }
    }

    /**
     * 漏洞上报
     * <AUTHOR>
     */
    def create = {
        /**
         * 先判断是否有用户登录，如果有的话，则跳转到漏洞上报页面，不然跳转到登录页面
         */
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            return redirect(controller: "TUser", action: "login")
        }
        def flawInstance = new Flaw()
        return [flawInstance: flawInstance, obj: params]
    }

    def findUnitName = {
        if (!session.user) {
            render ""
            return
        }
        def jsonList = new JSONArray()
        List<Manufacturer> manufacturerList = new ArrayList<Manufacturer>()
        manufacturerList = Manufacturer.createCriteria().list {
        }
        List<Map<String, String>> resList = new ArrayList<Map<String, String>>(10);
        if (manufacturerList != null && manufacturerList.size() > 0) {
            for (int i = 0; i < manufacturerList.size(); i++) {
                Map<String, String> item = new HashMap<String, String>();
                item.put("name", manufacturerList.get(i).getName());
                resList.add(item);
            }
        }


        /*List<Manufacturer> manufacturerListRedis = RedisUtil.getList("manufacturerList",Manufacturer.class)
		*//*如果redis中manufacturerList不为空，并且有值，就用redis中的数据
		  否则，就从数据库中查，并且添加到redis中*//*
		if(manufacturerListRedis!=null && manufacturerListRedis.size()>0){
			manufacturerList = manufacturerListRedis
		}else{
			manufacturerList = Manufacturer.createCriteria().list{
			}
			RedisUtil.setList("manufacturerList",manufacturerList)
		}*/
        def string = JSONArray.toJSONString(resList)
        render string
        return
    }

    def findIndustryName = {

        def json = new JSONObject()
        def jsonList = new JSONArray()
        def list = FlawIndustry.createCriteria().list {
            and { eq("enable", 1) }
        }
        for (FlawIndustry flawIndustry : list) {
            json = new JSONObject()
            json.put("id", flawIndustry.id + "")
            json.put("name", flawIndustry.name)
            jsonList.add(json)
        }
        render jsonList.toString()
        return
    }

    def findMinistriesName = {

        def json = new JSONObject()
        def jsonList = new JSONArray()
        def list = FlawMinistries.createCriteria().list {
            and { eq("enable", 1) }
        }
        for (FlawMinistries flawMinistries : list) {
            json = new JSONObject()
            json.put("id", flawMinistries.id + "")
            json.put("name", flawMinistries.name)
            jsonList.add(json)
        }
        render jsonList.toString()
        return
    }

    def findFlawType = {

        def json = new JSONObject()
        def jsonList = new JSONArray()
        def list = FlawType.createCriteria().list {
            and { eq("enable", 1) }
        }
        for (FlawType flawType : list) {
            json = new JSONObject()
            json.put("id", flawType.id + "")
            json.put("name", flawType.name)
            jsonList.add(json)
        }
        render jsonList.toString()
        return
    }


    def show = {
        Date d1 = new Date()
        String sd1 = DateUtil.get4yMdHms(d1)
        def ip = flawService.getIpAddr(request);
        log.info("|" + sd1 + "|webCrawler|url=flaw/show/" + params.id + "|ip=" + ip);
        if (ip != null && params.id != null && params.id.startsWith("CNVD")) {
            boolean bool = flawService.reptile(ip, "flaw/show/" + params.id);
            log.info("|" + sd1 + "|webCrawler|url=flaw/show/" + params.id + "|ip=" + ip + "|bool=" + bool);
            if (!bool) {
                render(view: "/error");
            }
        }
        def start = System.currentTimeMillis()
        session.originalRequestParams = [controller: controllerName, action: actionName, id: params.id]
        //String ip=Constants.getClientIp(request)
        //2016.8.2与拦截器一同注掉
//		Map map=session.ip
//		if(map.get("isd")){
//			if(!params.myCode){
//				render(view:"/flaw/vcode",model:[id:params.id]);
//				return;
//			}else{
//				try{
//					if (!jcaptchaService.validateResponse("imageCaptcha", session.id, params.myCode)){
//						flash.message = '验证码不正确'
//						render(view:"/flaw/vcode",model:[id:params.id]);
//						return
//					}
//				}catch(Exception e){
//					flash.message = '验证码失效'
//					render(view:"/flaw/vcode",model:[id:params.id]);
//					return
//				}
//			}
//		}
        def flawInstance
        if (params.id.startsWith("CNVD")) {
//			flawInstance = Flaw.findByNumberOrOldNumber(params.id,params.id)
            flawInstance = Flaw.findByNumber(params.id)
        } else {
            flawInstance = Flaw.get(params.int('id'))
        }
        String date = DateUtil.getTomorrowStrDefault();
        if (!flawInstance || flawInstance.status != 9 || flawInstance.isOpen != 1
                || flawInstance.enable != 1 || DateUtil.parseDate(date, "yyyy-MM-dd").compareTo(flawInstance.openTime) < 0
                || DateUtil.parseDate(date, "yyyy-MM-dd").compareTo(flawInstance.openTime) == 0
                || flawInstance.parentFlaw) {
            render(view: "/error")
        } else {
            /**
             * 查询出当前漏洞的所有评论的集合
             */
            def time1 = System.currentTimeMillis()
            String hql = " from FlawComments where flaw = ? and status = ? order by dateCreated desc "
            def flawCommentList = FlawComments.executeQuery(hql, [flawInstance, 0])
            /**
             *  查询影响此漏洞影响产品的厂商的漏洞前10条，按照open_time desc排序
             */
            def time2 = System.currentTimeMillis()
            def flawProductList = FlawProduct.findAllByFlaw(flawInstance)//漏洞影响的所有产品
            def time3 = System.currentTimeMillis()
            def manufacturer
            def res
            if (flawProductList) {
                manufacturer = flawProductList[0].product.manufacturer
                def sql = "select title,number from flaw f, " +
                        "(select distinct flaw_id from flaw_product fp, product_info pi where fp.product_id = pi.id and pi.manufacturer_id = ? and flaw_id <> ?) f2 " +
                        "where f.id = f2.flaw_id and f.status=9 and f.enable=1 and f.open_time<? " +
                        "and f.is_open=1 and f.parent_flaw_id is null order by f.open_time desc limit 10"
                def paramList = new ArrayList()
                paramList.add(manufacturer.id)
                paramList.add(flawInstance.id)
                paramList.add(new Date())
                res = new groovy.sql.Sql(dataSource).rows(sql, paramList)
            }
            def time4 = System.currentTimeMillis()
            def clickNum = (flawInstance.clickNum ? flawInstance.clickNum : 0) + 1
            def sql = "update flaw set click_num = ? where id = ?"
            new groovy.sql.Sql(dataSource).executeUpdate(sql, [clickNum, flawInstance?.id])
            Date d2 = new Date()
            double differDate = DateUtil.getDifTwoTime(d2.getTime(), d1.getTime(), "S")
            log.info("|" + sd1 + "|查询漏洞详情|url=flaw/show/" + params.id + "|查询耗时=" + differDate + "|ip=" + ip);
            [flawInstance: flawInstance, flawCommentList: flawCommentList, flawProductList: flawProductList, res: res]
        }
    }

    /**
     * 下载附件
     */
    def downloadAttachment = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            redirect(controller: "TUser", action: "login")
            return
        }
        def flawInstance = Flaw.get(params.flawId)
        def attId = params.id
        def att = Attachment.get(params.id)
        attachmentService.downloadAttFlaw(session.user, flawInstance, att, request, response)
        //attachmentService.downloadAtt(att, request, response)
    }

    def edit = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            return redirect(controller: "TUser", action: "login")
        }
        def flawInstance = Flaw.get(params.long('id'))

        //判断此漏洞是否所属于当前登录用户
        if (!flawInstance || session.user?.id == new Long(params.id)) {
            render(view: "/error")
        }

        if (session.user.id != flawInstance.user.id) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            return redirect(controller: "TUser", action: "login")
        }


        if (flawInstance?.status != -1 && flawInstance?.status != 7) {
            flash.operationErr = "操作错误,请重新操作"
            redirect(controller: "user", action: "reportManage")
            return;
        }
        def flawUrls = FlawUrl.findAllByFlaw(flawInstance)
        def urlStr = ""
        for (int i = 0; i < flawUrls.size(); i++) {
            if (i == 0) {
                urlStr += flawUrls.get(i).url
            } else {
                urlStr += ("," + flawUrls.get(i).url)
            }
        }
        return [flawInstance: flawInstance, flawUrls: urlStr]
    }

    def update = {
        if (!session.user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            return redirect(controller: "TUser", action: "login")
        }

        def flawInstance = Flaw.get(params.id)
        //判断此漏洞是否所属于当前登录用户
        if (!flawInstance || (flawInstance.status != -1 && flawInstance.status != 7) || session.user?.id == new Long(params.id)) {
            render(view: "/error")
        }
        if (session.user.id != flawInstance.user.id) {
            render(view: "/error")
        }
        //flawInstance.properties = params
        if (flawInstance.status != -1 && flawInstance.status != 7) {
            flash.operationErr = "操作错误,请重新操作"
            redirect(controller: "user", action: "reportManage")
            return;
        }

        if (!jcaptchaService.validateResponse("imageCaptcha", session.id, params.myCode)) {
            flash.captcha_error = '验证码不正确'
            redirect(action: "edit", id: params.id)
            return
        }
        if (params.url) {
            SimpleDateFormat sd = new SimpleDateFormat("yyyy")
            Calendar c = Calendar.getInstance();
            c.setTime(new Date());
            c.add(Calendar.YEAR, -1);
            Date y = c.getTime();
            String year = sd.format(y);
            String errStr = ""
            int urlFlag = params.url.trim().indexOf(",")
            if (urlFlag != -1) {
                String[] urlArr = params.url.trim().split(",")
                int i = 0;
                for (String url : urlArr) {
                    //判断url是否以http和https开头
                    def regEx = /^http:\/\/.*/;    //https网址正则
                    def _regEx = /^https:\/\/.*/;    //http
                    // 编译正则表达式
                    Pattern pattern = Pattern.compile(regEx);
                    Matcher matcher = pattern.matcher(url.trim());
                    Pattern _pattern = Pattern.compile(_regEx);
                    Matcher _matcher = _pattern.matcher(url.trim());
                    // 字符串是否与正则表达式相匹配
                    boolean rs = matcher.matches();
                    boolean _rs = _matcher.matches();
                    if (!rs && !_rs) {
                        def url_error = "【" + url + "】不符合上报规范。"
                        render(view: "edit", model: [flawInstance: flawInstance, url_error: url_error, flawUrls: params.url])
                        return
                    }
                    if (!url.trim().equals("")) {
                        //先判断是否为事件型漏洞
                        if ("1".equals(params.isEvent)) {
                            FlawUrl flawUrl = FlawUrl.find("from FlawUrl where url = :url ", [url: url.trim()]);
                            if (flawUrl && flawUrl.flaw.dateCreated > y && flawUrl.flaw.status != -1 && flawUrl.flaw.status != -2 && params.getInt("isEvent") == flawUrl.flaw.isEvent) {
                                errStr += url.trim()
                                flawInstance.isRepeat = 1;
                            }
                        } else {
                            def flawUrlList = FlawUrl.findAll("from FlawUrl where url = :url ", [url: url.trim()]);
                            if (flawUrlList) {
                                for (FlawUrl flawUrl : flawUrlList) {
                                    if (flawUrl.flaw.status != -1 && flawUrl.flaw.status != -2 && params.getInt("isEvent") == flawUrl.flaw.isEvent) {
                                        flawInstance.isRepeat = 1;
                                    }
                                }
                            }
                        }
                    }
                    i++;
                }
            } else {
                //判断url是否以http和https开头
                def regEx = /^http:\/\/.*/;    //https网址正则
                def _regEx = /^https:\/\/.*/;    //http
                // 编译正则表达式
                Pattern pattern = Pattern.compile(regEx);
                Matcher matcher = pattern.matcher(params.url.trim());
                Pattern _pattern = Pattern.compile(_regEx);
                Matcher _matcher = _pattern.matcher(params.url.trim());
                // 字符串是否与正则表达式相匹配
                boolean rs = matcher.matches();
                boolean _rs = _matcher.matches();
                if (!rs && !_rs) {
                    def url_error = "【" + params.url.trim() + "】不符合上报规范。"
                    render(view: "edit", model: [flawInstance: flawInstance, url_error: url_error, flawUrls: params.url])
                    return
                }
                if ("1".equals(params.isEvent)) {
                    FlawUrl flawUrl = FlawUrl.find("from FlawUrl where url = :url ", [url: params.url.trim()]);
                    if (flawUrl && flawUrl.flaw.dateCreated > y && flawUrl.flaw.status != -1 && flawUrl.flaw.status != -2 && params.getInt("isEvent") == flawUrl.flaw.isEvent) {
                        //errStr = params.url.trim()
                        flawInstance.isRepeat = 1;
                    }
                } else {
                    FlawUrl flawUrl = FlawUrl.find("from FlawUrl where url = :url ", [url: params.url.trim()]);
                    if (flawUrl) {
                        if (flawUrl.flaw.status != -1 && flawUrl.flaw.status != -2 && params.getInt("isEvent") == flawUrl.flaw.isEvent) {
                            //errStr = params.url.trim()
                            flawInstance.isRepeat = 1;
                        }
                    }
                }
            }
        }

        /**
         * 日期格式转换
         */
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
        flawInstance.foundTime = sdf.parse(params.foundTimes)

        //上传漏洞附件并设置flawInstance的attachment属性
        def file = request.getFile("flawAttFile")
        if (file != null && !"".equals(file.getOriginalFilename())) {
            if (flawInstance.attachment) {
                flash.message = "附件已存在，不能多次上传"
                redirect(action: "edit", id: params.id)
                return
            } else {
                def flag = attachmentService.checkFile(file)
                if (flag) {
                    //附件符合格式及大小限制
                    String filePath = "${grailsApplication.config.filePath.flawAttFilePath}"
                    String realName = CommentsUtil.getCurrentTime() //文件的真实文件名
                    def attachment = attachmentService.uploadFile(file, filePath, realName)
                    flawInstance.attachment = attachment
                } else {
                    //附件不符合格式或大小限制
                    def fileLimit_error = "附件类型不正确或大小超出10MB"
                    return render(view: 'edit', model: [flawInstance: flawInstance, fileLimit_error: fileLimit_error])
                }
            }
        }

        //驳回重复提交，新增
        flawInstance.discovererName = params.discovererName
        flawInstance.title = params.title

        //如果是事件型漏洞
        /*if(params.int("isEvent")==1) {
			//漏洞新标题
			*//*if (!"未知".equals(params.province) && !"未知".equals(params.city)) {
				flawInstance.title = params.province + params.city + params.title
				println "newTitle|" + flawInstance.title
			}*//*
		}*/
        //漏洞详细信息
        DetailedInfo detailedInfo = flawInstance.detailedInfo
        detailedInfo.description = params.description
        detailedInfo.tempWay = params.tempWay
        detailedInfo.formalWay = params.formalWay
        detailedInfo.save(flush: true)
        flawInstance.detailedInfo = detailedInfo
        if (flawInstance.isEvent == 0 && flawInstance.isOpen == 1) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.DATE, 45);
            Date openTime = calendar.getTime();
            flawInstance.openTime = openTime
        }
        flawInstance.status = 1
        flawInstance.submitTime = new Date()
        if (flawInstance.save(flush: true)) {
            def flawUpdatedInstance = new FlawUpdated()
            flawUpdatedInstance.flaw = flawInstance
            flawUpdatedInstance.type = 1
            flawUpdatedInstance.save(flush: true)
            int urlFlag = params.url.trim().indexOf(",")
            //清除原来的漏洞url信息
            FlawUrl.executeUpdate("delete from FlawUrl where flaw = ?", [flawInstance])
            if (urlFlag != -1) {
                String[] urlArr = params.url.trim().split(",")
                int i = 0;
                for (String url : urlArr) {
                    if (!"".equals(url.trim())) {
                        //多条url循环入库
                        FlawUrl fu = new FlawUrl()
                        fu.flaw = flawInstance
                        fu.url = url.trim()
                        if (!fu.save(flush: true)) {
                            fu.errors.allErrors.each { println it }
                        }
                        //保存漏洞url到redis
                        flawService.setFlawUrl(flawInstance.getId(), url.trim().toLowerCase())
                    }
                }
            } else {
                if (!"".equals(params.url.trim())) {
                    //单条url入库
                    FlawUrl fu = new FlawUrl()
                    fu.flaw = flawInstance
                    fu.url = params.url.trim()
                    if (!fu.save(flush: true)) {
                        fu.errors.allErrors.each { println it }
                    }
                    //保存漏洞url到redis
                    flawService.setFlawUrl(flawInstance.getId(), params.url.trim().toLowerCase())
                }
            }
            redirect(controller: "user", action: "reportManage", id: flawInstance.id)
        } else {
            flawInstance.errors.allErrors.each {
                println it
            }
            render(view: "edit", model: [flawInstance: flawInstance])
        }
    }


    /**
     * 根据flag的不同值去生成不同的柱状图并返还给客户端
     * flag "weekCol" 周 柱状图
     * 		"monthCol" 月 柱状图
     * 		"year" 年 柱状图
     */
    def unitMemberCol = {
        if (params.flag == "weekCol") {
            // 计算出上一周的开始时间和结束时间
            def startDateStr = DateTimeUtil.getPrevWeekDateStr("start")
            def endDateStr = DateTimeUtil.getPrevWeekDateStr("end")
            render(view: "/column/weekColumn", model: [startDateStr: startDateStr, endDateStr: endDateStr])
        } else if (params.flag == "monthCol") {
            // 计算出上个月的开始时间和结束时间
            def startDateStr = DateTimeUtil.getPrevMonthDateStr("start")
            def endDateStr = DateTimeUtil.getPrevMonthDateStr("end")
            render(view: "/column/monthColumn", model: [startDateStr: startDateStr, endDateStr: endDateStr])
        } else if (params.flag == "yearCol") {
            // 计算出上一年的开始时间和结束时间
            def startDateStr = DateTimeUtil.getPrevYearDateStr("start")
            def endDateStr = DateTimeUtil.getPrevYearDateStr("end")
            render(view: "/column/yearColumn", model: [startDateStr: startDateStr, endDateStr: endDateStr])
        }
    }

    /**
     * 根据flag的不同来选择返回不同的趋势柱状图
     */
    def trendCol = {
        if (params.flag == "weekTrendCol") {
            // 计算出上一周的开始时间和结束时间
            def startDateStr = DateTimeUtil.getPrevWeekDateStr("start")
            def endDateStr = DateTimeUtil.getPrevWeekDateStr("end")
            render(view: "/column/weekTrendColumn", model: [startDateStr: startDateStr, endDateStr: endDateStr])
        } else if (params.flag == "monthTrendCol") {
            // 计算出上个月的开始时间和结束时间
            def startDateStr = DateTimeUtil.getPrevMonthDateStr("start")
            def endDateStr = DateTimeUtil.getPrevMonthDateStr("end")
            render(view: "/column/monthTrendColumn", model: [startDateStr: startDateStr, endDateStr: endDateStr])
        } else if (params.flag == "yearTrendCol") {
            // 计算出上一年的开始时间和结束时间
            def startDateStr = DateTimeUtil.getPrevYearDateStr("start")
            def endDateStr = DateTimeUtil.getPrevYearDateStr("end")
            render(view: "/column/yearTrendColumn", model: [startDateStr: startDateStr, endDateStr: endDateStr])
        }
    }

    /**
     * 下载漏洞的附件,只能下载已归档的漏洞
     */
    def download = {
        def flawInstance = Flaw.findByDownCode(params.cd)
        def att = flawInstance?.attachment
        if (!flawInstance || !att || flawInstance.status != 9 || flawInstance.isOpen != 1
                || flawInstance.enable != 1 || new Date().compareTo(flawInstance.openTime) < 0
                || flawInstance.parentFlaw != null || flawInstance.isAttShow == 0) {
            render(view: "/error")
            return
        }
        attachmentService.downloadAttFlaw(null, flawInstance, att, request, response)
    }
    def deleteAtt = {
        if (!session.user) {
            return redirect(controller: "TUser", action: "login")
        }
        def flawInstance = Flaw.get(params.flawId)
        //判断该漏洞是否属于当前用户
        if (flawInstance && flawInstance.user.id == session.user.id && (flawInstance?.status == -1 || flawInstance?.status == 7)) {
            flawInstance.attachment = null
            flawInstance.save(flush: true)
            render(text: "success", contentType: "text/plain", encoding: "UTF-8")
        } else {
            render(view: "/error")
            return
        }

    }

    /**
     * 上报漏洞发送邮件中的附件下载方法
     */
    def attDown = {
        def flawInstance = Flaw.findByDownCode(params.cd)
        def att = flawInstance?.attachment
        if (!flawInstance || !att) {
            render(view: "/error")
            return
        }
        //2021-09-12 解决漏洞暂时处理
        attachmentService.downloadAttFlaw(null, flawInstance, att, request, response)
    }

    /**
     * 下载漏洞的附件
     */
    def downloadself = {
        def flawInstance = Flaw.get(params.flawId)
        def att = Attachment.get(params.id)

        //判断漏洞附件是否存在
        if (!session.user) {
            //不存在
            //判断漏洞是否是公开漏洞
            if (!flawInstance || !att || flawInstance.status != 9 || flawInstance.isOpen != 1
                    || flawInstance.enable != 1 || new Date().compareTo(flawInstance.openTime) < 0
                    || flawInstance.parentFlaw != null || flawInstance.isAttShow == 0) {
                //不是
                render(view: "/error")
                return
            } else {
                //是
                attachmentService.downloadAttFlaw(session.user, flawInstance, att, request, response)
                return
            }
        }
        def isShow = false
        if (!(!flawInstance || !att || flawInstance.status != 9 || flawInstance.isOpen != 1
                || flawInstance.enable != 1 || new Date().compareTo(flawInstance.openTime) < 0
                || flawInstance.parentFlaw != null || flawInstance.isAttShow == 0)) {
            isShow = true
        }
        if (flawInstance?.user?.id == session?.user?.id || isShow) {
            attachmentService.downloadAttFlaw(session.user, flawInstance, att, request, response)
            return
        } else {
            render(view: "/error")
            return
        }

    }
    def flawTrack = {
        if (!session.user) {
            return redirect(controller: "TUser", action: "login")
        } else {
            redirect(controller: "user", action: "reportManage")
            return
        }
    }

    def track = {
        if (!session.user) {
            render "error"
        }
        def flawInstance = Flaw.get(params.int('flawId'))
        if (flawInstance && flawInstance?.user?.id == session?.user?.id) {
            def logList = new ArrayList()
            def exmaineHistoryList = ExmaineHistory.findAllByFlaw(flawInstance)
            logList.addAll(exmaineHistoryList)
            //flawProcessList=FlawProcess.findAllByFlaw(flawInstance)
            def flawProcessList = FlawProcess.executeQuery(" from FlawProcess where flaw=? and processInfo.id in(1,2,3,4,8) order by date_created asc", [flawInstance])
            logList.addAll(flawProcessList)

            Collections.sort(logList, new Comparator<Object>() {
                public int compare(Object flaw1, Object flaw2) {
                    return flaw1.dateCreated.compareTo(flaw2.dateCreated)
                }
            });
            def exploitInstanceTemp = flawInstance.getExploit()
            def suggestion = ""
            if (flawInstance?.isv == 1) {
                suggestion = exploitInstanceTemp.suggestion
            }
            def patchInstanceTemp = flawInstance.getPatchInfo()
            def patchDescription = ""
            if (flawInstance?.ivp == 1) {
                patchDescription = patchInstanceTemp.patchDescription
            }
            [logList: logList, "patchDescription": patchDescription, "suggestion": suggestion]
            //[exmaineHistoryList:exmaineHistoryList,flawProcessList:flawProcessList]
        } else {
            render "err"
        }
    }

    def statistic = {}

    def changeFlawDis = {
        //查询dicrionary_info表中type为1(causeId) 2(threadId) 3(serverityId) 4(positionId) 5(softStyleId)的各个分类的漏洞数量
        def sql = ""
        def paramList = new ArrayList()
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd")
        def startTime = System.currentTimeMillis()
        if (params.int('type') == 1) {
            sql = "select di.name as name, count(f.cause_id) as count from flaw f,dictionary_info di where di.type = ? and f.status = 9 and f.is_event=0 and f.soft_style_id not in (34,35,36) and f.enable = 1 and f.parent_flaw_id is null and f.cause_id = di.id ";
            paramList.add(params.int('type'))
            if (params.startDate) {
                sql += " and storage_time >= ? "
                paramList.add(format.parse(params.startDate))
            }
            if (params.endDate) {
                sql += " and storage_time < ? "
                paramList.add(format.parse(params.endDate) + 1)
            }
            sql += "group by f.cause_id"
        } else if (params.int('type') == 2) {
            sql = "select di.name as name, count(f.thread_id) as count from flaw f,dictionary_info di where di.type = ? and f.status = 9 and f.is_event=0 and f.soft_style_id not in (34,35,36) and f.enable = 1 and f.parent_flaw_id is null and f.thread_id = di.id ";
            paramList.add(params.int('type'))
            if (params.startDate) {
                sql += " and storage_time >= ? "
                paramList.add(format.parse(params.startDate))
            }
            if (params.endDate) {
                sql += " and storage_time < ? "
                paramList.add(format.parse(params.endDate) + 1)
            }
            sql += " group by f.thread_id"
        } else if (params.int('type') == 3) {
            sql = "select di.name as name, count(f.serverity_id) as count from flaw f,dictionary_info di where di.type = ? and f.status = 9 and f.is_event=0 and f.soft_style_id not in (34,35,36) and f.enable = 1 and f.parent_flaw_id is null and f.serverity_id = di.id ";
            paramList.add(params.int('type'))
            if (params.startDate) {
                sql += " and storage_time >= ? "
                paramList.add(format.parse(params.startDate))
            }
            if (params.endDate) {
                sql += " and storage_time < ? "
                paramList.add(format.parse(params.endDate) + 1)
            }
            sql += "group by f.serverity_id"
        } else if (params.int('type') == 4) {
            sql = "select di.name as name, count(f.position_id) as count from flaw f,dictionary_info di where di.type = ? and f.status = 9 and f.is_event=0 and f.soft_style_id not in (34,35,36) and f.enable = 1 and f.parent_flaw_id is null and f.position_id = di.id ";
            paramList.add(params.int('type'))
            if (params.startDate) {
                sql += " and storage_time >= ? "
                paramList.add(format.parse(params.startDate))
            }
            if (params.endDate) {
                sql += " and storage_time < ? "
                paramList.add(format.parse(params.endDate) + 1)
            }
            sql += " group by f.position_id"
        } else if (params.int('type') == 7) {
            sql = "select di.name as name, count(f.soft_style_id) as count from flaw f,dictionary_info di where di.type = ? and f.status = 9 and f.is_event=0 and f.soft_style_id not in (34,35,36) and f.enable = 1 and f.parent_flaw_id is null and f.soft_style_id = di.id ";
            paramList.add(params.int('type'))
            if (params.startDate) {
                sql += " and storage_time >= ? "
                paramList.add(format.parse(params.startDate))
            }
            if (params.endDate) {
                sql += " and storage_time < ? "
                paramList.add(format.parse(params.endDate) + 1)
            }
            sql += "group by f.soft_style_id"
        }

        def content = ""
        def res = new groovy.sql.Sql(dataSource).rows(sql, paramList)
        res.each {
            if ("".equals(content)) {
                content = it.name + ";" + it.count;
            } else {
                content += "\\n" + it.name + ";" + it.count
            }
        }
        render(view: "flawDisPie", model: [content: content])
    }

    def changeLevelTrend = {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd")
        def content = ""
        def endDate
        def startDate
        /**
         * 判断选择的“结束时间”和prevYearToday之间的时间差
         * 如果>1年 则按年份进行group by
         * 如果<1年 但是>1个月 则按照月份进行group by
         * 如果在同一个月份之内 则按照日期进行group by
         * 如果“结束时间”在prevYearToday日期前 则返回没有数据
         */
        if (!params.startDate && !params.endDate) {
            //表示用户没有选择“开始时间”和“结束时间”,没有指定startDate和endDate的情况下，获取今天的日期和去年的今天的日期
            endDate = new Date()
            startDate = DateTimeUtil.getPrevYearToday()
        } else if (!params.endDate) {
            //表示用户没有选择“结束时间”，只选择了"开始时间"
            startDate = format.parse(params.startDate)
            endDate = new Date()
        } else if (!params.startDate) {
            //表示用户没有选择“开始时间”，只选择了"结束时间"
            endDate = format.parse(params.endDate)
            startDate = DateTimeUtil.getPrevYearToday()
        } else {
            //表示用户既选择了“开始时间”，又选择了“结束时间”
            startDate = format.parse(params.startDate)
            endDate = format.parse(params.endDate)
        }
        content = FlawTrendLineUtil.generateContent(params.level, startDate, endDate)

        render(view: "flawTrendLine", model: [content: content])
    }

    /**
     * 查询影响某个厂商产品的漏洞
     */
    def flawListByManu = {
        params.max = Math.min(params.max ? params.int('max') : 20, 100)
        params.offset = params.offset ? params.int('offset') : 0

        def manufacturer = Manufacturer.get(params.id)
        def flawListPrefix = "select * from flaw f, "
        def countPrefix = "select count(*) as cnt from flaw f, "
        def sql = "(select distinct flaw_id from flaw_product fp, product_info pi where fp.product_id = pi.id and pi.manufacturer_id = ? ) f2 " +
                "where f.id = f2.flaw_id and f.status=9 and f.enable=1 and f.open_time<? " +
                "and f.is_open=1 and f.parent_flaw_id is null "
        def flawListSql = flawListPrefix + sql + "order by f.open_time limit ?,?"
        def countSql = countPrefix + sql
        def paramList = new ArrayList()
        paramList.add(manufacturer?.id)
        paramList.add(new Date())
        def countRes = new groovy.sql.Sql(dataSource).rows(countSql, paramList)
        paramList.add(params.offset)
        paramList.add(params.max)
        def flawListRes = new groovy.sql.Sql(dataSource).rows(flawListSql, paramList)
        [manufacturer: manufacturer, flawInstanceList: flawListRes, flawInstanceTotal: countRes[0].cnt]
    }

    def dialog = {

    }

    def urlAnalyze = {

        if ("bdgg_preview.htm".equals(params.url)) {
            //补丁详情链接
            redirect(controller: "patchInfo", action: "show", params: [id: params.tid])
        } else if ("ldgg_preview.htm".equals(params.url)) {
            //漏洞详情链接
            redirect(action: "show", params: [id: params.tid])
        } else if ("index".equals(params.url)) {
            redirect(action: "list", params: [flag: "true"])
        }
    }

    def findByKwd = {
        def start = System.currentTimeMillis()
        def kwd = URLDecoder.decode(URLDecoder.decode(params.kwd, "UTF-8"), "UTF-8")
        def flawInstanceList = Flaw.executeQuery(" from Flaw where title like ? and openTime<=? and status = 9 and enable = 1 and isOpen=1 and parentFlaw is null", [
                '%' + params.kwd.trim() + '%',
                new Date()
        ])

        def str = "["
        for (def flaw : flawInstanceList) {
            def title = flaw.title.replaceAll("\"", "\'")
            def number = flaw.number
            def serverityId = flaw.serverityId
            def description = flaw.detailedInfo.description ? flaw.detailedInfo.description.replaceAll("\"", "\'").replaceAll("\\n", "").replaceAll("\\r", "") : null
            def formalWay = flaw.detailedInfo.formalWay ? flaw.detailedInfo.formalWay.replaceAll("\"", "\'").replaceAll("\\n", "").replaceAll("\\r", "") : null
            def flawStr = ""
            flawStr += "{\"title\":\"" + title + "\",\"number\":\"" + number + "\",\"serverityId\":\"" + serverityId + "\",\"description\":\"" + description + "\",\"formalWay\":\"" + formalWay + "\"}"
            str += flawStr + ","
        }
        str = str.substring(0, str.length() - 1)
        str += "]"
        render str
    }

    def testRedis = {
        def flawInstance
        if (params.id.startsWith("CNVD")) {
            flawInstance = Flaw.findByNumberOrOldNumber(params.id, params.id)
        } else {
            flawInstance = Flaw.get(params.int('id'))
        }
        RedisUtil.setObj("cnvd110", flawInstance)

        Flaw fl = RedisUtil.getObj("cnvd110", Flaw.class)
        println fl.title
    }


    /**
     *
     */

    public void flawAbout(Flaw flawInstance, net.sf.json.JSONArray array, int operate, int isPush, int isLoophole) {

        net.sf.json.JSONObject jsonTemp = new net.sf.json.JSONObject()

        net.sf.json.JSONObject jsonTemp1 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json1 = toJsonObjectService.toJsonObject(flawInstance.getUser())
        jsonTemp1.put("operate", operate)
        jsonTemp1.put("isPush", isPush)
        jsonTemp1.put("flawId", flawInstance.getId())
        jsonTemp1.put("isLoophole", isLoophole)
        jsonTemp1.put("entity", json1.toString())
        array.add(jsonTemp1)


        net.sf.json.JSONObject jsonTemp2 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json2 = toJsonObjectService.toJsonObject(flawInstance.getParentFlaw())
        jsonTemp2.put("operate", operate)
        jsonTemp2.put("isPush", isPush)
        jsonTemp2.put("flawId", flawInstance.getId())
        jsonTemp2.put("isLoophole", isLoophole)
        jsonTemp2.put("entity", json2.toString())
        array.add(jsonTemp2)

        net.sf.json.JSONObject jsonTemp3 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json3 = toJsonObjectService.toJsonObject(flawInstance.getBasemetric())
        jsonTemp3.put("operate", operate)
        jsonTemp3.put("isPush", isPush)
        jsonTemp3.put("flawId", flawInstance.getId())
        jsonTemp3.put("isLoophole", isLoophole)
        jsonTemp3.put("entity", json3.toString())
        array.add(jsonTemp3)


        net.sf.json.JSONObject jsonTemp4 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json4 = toJsonObjectService.toJsonObject(flawInstance.getTemporalMetric())
        jsonTemp4.put("operate", operate)
        jsonTemp4.put("isPush", isPush)
        jsonTemp4.put("flawId", flawInstance.getId())
        jsonTemp4.put("isLoophole", isLoophole)
        jsonTemp4.put("entity", json4.toString())
        array.add(jsonTemp4)

        net.sf.json.JSONObject jsonTemp5 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json5 = toJsonObjectService.toJsonObject(flawInstance.getEnvironmentalMetric())
        jsonTemp5.put("operate", operate)
        jsonTemp5.put("isPush", isPush)
        jsonTemp5.put("flawId", flawInstance.getId())
        jsonTemp5.put("isLoophole", isLoophole)
        jsonTemp5.put("entity", json5.toString())
        array.add(jsonTemp5)

        net.sf.json.JSONObject jsonTemp6 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json6 = toJsonObjectService.toJsonObject(flawInstance.getManufacturer())
        jsonTemp6.put("operate", operate)
        jsonTemp6.put("isPush", isPush)
        jsonTemp6.put("isLoophole", isLoophole)
        jsonTemp6.put("flawId", flawInstance.getId())
        jsonTemp6.put("entity", json6.toString())
        array.add(jsonTemp6)

        net.sf.json.JSONObject jsonTemp7 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json7 = toJsonObjectService.toJsonObject(flawInstance.getAttachment())
        jsonTemp7.put("operate", operate)
        jsonTemp7.put("isPush", isPush)
        jsonTemp7.put("isLoophole", isLoophole)
        jsonTemp7.put("flawId", flawInstance.getId())
        jsonTemp7.put("entity", json7.toString())
        array.add(jsonTemp7)


        net.sf.json.JSONObject jsonTemp8 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json8 = toJsonObjectService.toJsonObject(flawInstance.getDetailedInfo())
        jsonTemp8.put("operate", operate)
        jsonTemp8.put("isPush", isPush)
        jsonTemp8.put("isLoophole", isLoophole)
        jsonTemp8.put("flawId", flawInstance.getId())
        jsonTemp8.put("entity", json8.toString())
        array.add(jsonTemp8)

        net.sf.json.JSONObject jsonTemp9 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json9 = toJsonObjectService.toJsonObject(flawInstance.getBatchFlaw())
        jsonTemp9.put("operate", operate)
        jsonTemp9.put("isPush", isPush)
        jsonTemp9.put("isLoophole", isLoophole)
        jsonTemp9.put("flawId", flawInstance.getId())
        jsonTemp9.put("entity", json9.toString())
        array.add(jsonTemp9)


        net.sf.json.JSONObject jsonTemp10 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json10 = toJsonObjectService.toJsonObject(flawInstance.getFlawTypes())
        jsonTemp10.put("operate", operate)
        jsonTemp10.put("isPush", isPush)
        jsonTemp10.put("isLoophole", isLoophole)
        jsonTemp10.put("flawId", flawInstance.getId())
        jsonTemp10.put("entity", json10.toString())
        array.add(jsonTemp10)


        net.sf.json.JSONObject json = toJsonObjectService.toJsonObject(flawInstance)
        jsonTemp.put("operate", operate)
        jsonTemp.put("isPush", isPush)
        jsonTemp.put("flawId", flawInstance.getId())
        jsonTemp.put("isLoophole", isLoophole)
        jsonTemp.put("entity", json.toString())

        println json.toString()

        array.add(jsonTemp)


        net.sf.json.JSONObject jsonTemp31 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json31 = toJsonObjectService.toJsonObject(FlawUpdated.findByFlaw(flawInstance))
        jsonTemp31.put("operate", operate)
        jsonTemp31.put("isPush", isPush)
        jsonTemp31.put("isLoophole", isLoophole)
        jsonTemp31.put("entity", json31.toString())
        array.add(jsonTemp31)


        net.sf.json.JSONObject jsonTemp41 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json41 = toJsonObjectService.toJsonObject(FlawProcess.findByFlaw(flawInstance))
        jsonTemp41.put("operate", operate)
        jsonTemp41.put("isPush", isPush)
        jsonTemp41.put("isLoophole", isLoophole)
        jsonTemp41.put("entity", json41.toString())
        array.add(jsonTemp41)

        net.sf.json.JSONObject jsonTemp42 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json42 = toJsonObjectService.toJsonObject(Certificate.findByFlaw(flawInstance))
        jsonTemp42.put("operate", operate)
        jsonTemp42.put("isPush", isPush)
        jsonTemp42.put("isLoophole", isLoophole)
        jsonTemp42.put("entity", json42.toString())
        array.add(jsonTemp42)


        def exmaineHistoryList = ExmaineHistory.findAllByFlaw(flawInstance)

        for (int i = 0; i < exmaineHistoryList.size(); i++) {

            net.sf.json.JSONObject jsonTemp43 = new net.sf.json.JSONObject()
            net.sf.json.JSONObject json43 = toJsonObjectService.toJsonObject(exmaineHistoryList.get(i))
            jsonTemp43.put("operate", operate)
            jsonTemp43.put("isPush", isPush)
            jsonTemp43.put("isLoophole", isLoophole)
            jsonTemp43.put("entity", json43.toString())
            array.add(jsonTemp43)

        }


        net.sf.json.JSONObject jsonTemp44 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json44 = toJsonObjectService.toJsonObject(Exploit.findByFlaw(flawInstance))
        jsonTemp44.put("operate", operate)
        jsonTemp44.put("isPush", isPush)
        jsonTemp44.put("isLoophole", isLoophole)
        jsonTemp44.put("entity", json44.toString())
        array.add(jsonTemp44)

        if (Exploit.findByFlaw(flawInstance) != null) {

            def exploitAttachmentList = ExploitAttachment.findAllByExploit(Exploit.findByFlaw(flawInstance))

            if (exploitAttachmentList != null && !exploitAttachmentList.isEmpty()) {

                for (int i = 0; i < exploitAttachmentList.size(); i++) {

                    if (exploitAttachmentList.get(i).getAttachment() != null) {


                        net.sf.json.JSONObject jsonTemp53 = new net.sf.json.JSONObject()
                        net.sf.json.JSONObject json53 = toJsonObjectService.toJsonObject(exploitAttachmentList.get(i).getAttachment())
                        jsonTemp53.put("operate", operate)
                        jsonTemp53.put("isPush", isPush)
                        jsonTemp53.put("isLoophole", isLoophole)
                        jsonTemp53.put("entity", json53.toString())
                        array.add(jsonTemp53)

                        net.sf.json.JSONObject json54 = toJsonObjectService.toJsonObject(exploitAttachmentList.get(i))
                        jsonTemp53.put("entity", json54.toString())
                        array.add(jsonTemp53)
                        AttachmentPdf AttachmentPdf = AttachmentPdf.findByAttachmentId(exploitAttachmentList.get(i).getAttachment().getId())

                        if (AttachmentPdf != null) {

                            net.sf.json.JSONObject json55 = toJsonObjectService.toJsonObject(AttachmentPdf)
                            jsonTemp53.put("entity", json55.toString())
                            array.add(jsonTemp53)

                            net.sf.json.JSONObject json56 = toJsonObjectService.toJsonObject(ExploitAttachmentPdf.findByAttachmentPdf(AttachmentPdf))
                            jsonTemp53.put("entity", json56.toString())
                            array.add(jsonTemp53)
                        }
                    }
                }
            }
        }


        def productList = FlawProduct.findAllByFlaw(flawInstance)


        for (int i = 0; i < productList.size(); i++) {

            net.sf.json.JSONObject jsonTemp46 = new net.sf.json.JSONObject()
            net.sf.json.JSONObject json46 = toJsonObjectService.toJsonObject(productList.get(i))
            jsonTemp46.put("operate", operate)
            jsonTemp46.put("isPush", isPush)
            jsonTemp46.put("isLoophole", isLoophole)
            jsonTemp46.put("entity", json46.toString())
            array.add(jsonTemp46)

        }


        net.sf.json.JSONObject jsonTemp47 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json47 = toJsonObjectService.toJsonObject(FlawTypesParamMiddle.findByFlaw(flawInstance))
        jsonTemp47.put("operate", operate)
        jsonTemp47.put("isPush", isPush)
        jsonTemp47.put("isLoophole", isLoophole)
        jsonTemp47.put("entity", json47.toString())
        array.add(jsonTemp47)


        net.sf.json.JSONObject jsonTemp48 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json48 = toJsonObjectService.toJsonObject(FlawTypesParamMiddle.findByFlaw(flawInstance))
        jsonTemp48.put("operate", operate)
        jsonTemp48.put("isPush", isPush)
        jsonTemp48.put("isLoophole", isLoophole)
        jsonTemp48.put("entity", json48.toString())
        array.add(jsonTemp48)


        net.sf.json.JSONObject jsonTemp49 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json49 = toJsonObjectService.toJsonObject(FlawUrl.findByFlaw(flawInstance))
        jsonTemp49.put("operate", operate)
        jsonTemp49.put("isPush", isPush)
        jsonTemp49.put("isLoophole", isLoophole)
        jsonTemp49.put("entity", json49.toString())
        array.add(jsonTemp49)


        /*   JSONObject jsonTemp50 = new JSONObject()
           JSONObject json50=toJsonObjectService.toJsonObject(FlawUrl.findByFlaw(flawInstance))
           jsonTemp50.put("operate",operate)
           jsonTemp50.put("isPush",isPush)
           jsonTemp50.put("isLoophole",isLoophole)
           jsonTemp50.put("entity",json50.toString())
           array.add(jsonTemp50)*/


        def referenceInfoList = ReferenceInfo.findAllByFlaw(flawInstance)

        for (int i = 0; i < referenceInfoList.size(); i++) {

            net.sf.json.JSONObject jsonTemp51 = new net.sf.json.JSONObject()
            net.sf.json.JSONObject json51 = toJsonObjectService.toJsonObject(referenceInfoList.get(i))
            jsonTemp51.put("operate", operate)
            jsonTemp51.put("isPush", isPush)
            jsonTemp51.put("isLoophole", isLoophole)
            jsonTemp51.put("entity", json51.toString())
            array.add(jsonTemp51)

        }


        net.sf.json.JSONObject jsonTemp52 = new net.sf.json.JSONObject()
        net.sf.json.JSONObject json52 = toJsonObjectService.toJsonObject(PatchInfo.findByFlaw(flawInstance))
        jsonTemp52.put("operate", operate)
        jsonTemp52.put("isPush", isPush)
        jsonTemp52.put("isLoophole", isLoophole)
        jsonTemp52.put("entity", json52.toString())
        array.add(jsonTemp52)


        if (PatchInfo.findByFlaw(flawInstance) != null) {

            def patchInfoAttachmentList = PatchInfoAttachment.findAllByPatchInfo(PatchInfo.findByFlaw(flawInstance))

            if (patchInfoAttachmentList != null && !patchInfoAttachmentList.isEmpty()) {

                for (int i = 0; i < patchInfoAttachmentList.size(); i++) {

                    if (patchInfoAttachmentList.get(i).getAttachment() != null) {

                        net.sf.json.JSONObject jsonTemp53 = new net.sf.json.JSONObject()
                        net.sf.json.JSONObject json53 = toJsonObjectService.toJsonObject(patchInfoAttachmentList.get(i).getAttachment())
                        jsonTemp53.put("operate", operate)
                        jsonTemp53.put("isPush", isPush)
                        jsonTemp53.put("isLoophole", isLoophole)
                        jsonTemp53.put("entity", json53.toString())
                        array.add(jsonTemp53)

                        net.sf.json.JSONObject json54 = toJsonObjectService.toJsonObject(patchInfoAttachmentList.get(i))
                        jsonTemp53.put("entity", json54.toString())
                        array.add(jsonTemp53)
                    }

                }

            }

        }


        def taskList = Task.findAllByFlaw(flawInstance)
        for (int i = 0; i < taskList.size(); i++) {

            if (taskList.get(i).getAttachment() != null) {

                net.sf.json.JSONObject jsonTemp53 = new net.sf.json.JSONObject()
                net.sf.json.JSONObject json53 = toJsonObjectService.toJsonObject(taskList.get(i).getAttachment())
                jsonTemp53.put("operate", operate)
                jsonTemp53.put("isPush", isPush)
                jsonTemp53.put("isLoophole", isLoophole)
                jsonTemp53.put("entity", json53.toString())
                array.add(jsonTemp53)

            }

        }


        for (int i = 0; i < taskList.size(); i++) {

            net.sf.json.JSONObject jsonTemp53 = new net.sf.json.JSONObject()
            net.sf.json.JSONObject json53 = toJsonObjectService.toJsonObject(taskList.get(i))

            jsonTemp53.put("operate", operate)
            jsonTemp53.put("isPush", isPush)
            jsonTemp53.put("isLoophole", isLoophole)
            jsonTemp53.put("entity", json53.toString())
            array.add(jsonTemp53)

        }


    }


    void sendMsg(Flaw flaw, net.sf.json.JSONObject jsonImp, net.sf.json.JSONArray array) {


        if (flaw.push == 1) {

            String kafkaTopic = IsToPush.isRead(flaw.getSoftStyleId().toString());
            if (kafkaTopic != null) {
                jsonImp.put("date", array.toString())
                try {
                    new SendMsgProducer(jsonImp, kafkaIp, kafkaTopic).start()
                } catch (Exception e) {
                    def synchroErrData = new SynchroErrData();
                    synchroErrData.errorData = jsonImp.toString();
                    synchroErrData.errorReason = e.message.toString();
                    synchroErrData.status = 0;
                    synchroErrData.topic = kafkaTopic;
                    synchroErrData.save(flush: true)
                }
            }
        }


    }

    //查询关联产品
    def viewProduct = {
        def flawInstance = Flaw.get(params.int('flawId'))
        if (!flawInstance) {
            render ResultUtil.failResult("操作失败")
        }
        params.numPerPage = Math.min(params.numPerPage ? params.int('numPerPage') : 20, 100)
        params.pageNum = params.pageNum ? params.int('pageNum') : 1
        def offset = (params.int('pageNum') - 1) * params.int('numPerPage')

        def hql = "from ProductInfo p where 1=1"
        def hqlCount = "select count(p.id) from ProductInfo p where 1=1"
        def hqlPara = new HashMap()
        if (params.manufacturer) {
            Manufacturer manufacturer = Manufacturer.get(params.manufacturer)
            hql += " and  p.manufacturer=:manufacturer "
            hqlCount += " and  p.manufacturer=:manufacturer "
            hqlPara.put("manufacturer", manufacturer)
        }
        if (params.productCategory) {
            ProductCategory productCategory = ProductCategory.get(params.productCategory)
            hql += "and p.productCategory=:productCategory "
            hqlCount += " and  p.productCategory=:productCategory "

            hqlPara.put("productCategory", productCategory)
        }
        hql += " and  exists(select fp.id from FlawProduct fp where fp.flaw=:flawInstance and fp.product=p)"
        hqlCount += " and  exists(select fp.id from FlawProduct fp where fp.flaw=:flawInstance and fp.product=p)"
        hqlPara.put("flawInstance", flawInstance)
        def productInfoInstanceList = ProductInfo.executeQuery(hql, hqlPara, [max: params.numPerPage, offset: offset])
        def productInfoInstanceTotal = ProductInfo.executeQuery(hqlCount, hqlPara,)

        render(view: 'viewProduct', model: [flawInstance: flawInstance, productInfoInstanceList: productInfoInstanceList, productInfoInstanceTotal: productInfoInstanceTotal[0]])

    }


    /**
     * 取消漏洞关联产品
     * 取消漏洞关联产品时判断此漏洞是否只通过此产品与某个行业进行关联
     * 如果是的话则取消此漏洞与行业之间的关联关系
     */
    def delProduct = {

        def flawInstance = Flaw.get(params.int('flawId'))
        if (!flawInstance) {
            render ResultUtil.failResult("操作失败")
        }

        net.sf.json.JSONObject jsonImp = new net.sf.json.JSONObject()
        jsonImp.put("time", DateUtil.get4yMdHms(new Date()))
        def array = new net.sf.json.JSONArray()
        net.sf.json.JSONObject jsonTemp = new net.sf.json.JSONObject()
        jsonTemp.put("operate", 2)
        jsonTemp.put("isPush", 1)
        jsonTemp.put("isLoophole", 0)
        jsonTemp.put("flawId", flawInstance.getId())
        //def productIds = params.list('productIds')
        String str = params.productIds
        //后端处理字符串
        def productIds = str.split(",")
        productIds.each {
            def productInstance = ProductInfo.get(it)
            def manufacturer = productInstance.manufacturer
            def productCategory = productInstance.productCategory
            def editionStr = productInstance.edition

            def flawProduct = FlawProduct.findByFlawAndProduct(flawInstance, productInstance)
            if (flawProduct) {
                flawProduct.delete(flush: true)

                /*def flawProductSynchro = new FlawProductSynchro()
                flawProductSynchro.dateCreated=new Date()
                flawProductSynchro.synchroId=0
                flawProductSynchro.status=2
                flawProductSynchro.flawId=flawInstance.id
                flawProductSynchro.flawProductId=flawProduct.id*/


                jsonTemp.put("table", "flaw_product")
                jsonTemp.put("entity", flawProduct.getId())
                array.add(jsonTemp)

            }
        }

        //删除industry_flaw和flaw_industry_product表中的flaw是flawInstance记录
        IndustryFlaw.executeUpdate("delete from IndustryFlaw where flaw=? and type=0", [flawInstance])
        FlawIndustryProduct.executeUpdate("delete from FlawIndustryProduct where flaw = ?", [flawInstance])

        jsonTemp.put("table", "industry_flaw")
        jsonTemp.put("column", "flaw_id")
        jsonTemp.put("entity", flawInstance.getId())
        array.add(jsonTemp)

        jsonTemp.put("table", "flaw_industry_product")
        jsonTemp.put("column", "flaw_id")
        jsonTemp.put("entity", flawInstance.getId())
        array.add(jsonTemp)


        def productList = FlawProduct.executeQuery("select fp.product from FlawProduct fp where fp.flaw = ?", [flawInstance])
        //println "productList="+productList
        productList.each {
            def productInstance = it
            //根据产品查询行业实现漏洞与行业的关联
            def manufacturer = productInstance?.manufacturer
            def productCategory = productInstance?.productCategory
            def editionStr = productInstance?.edition
            def hql = " from CorporationProduct cp where manufacturer = ? and productCategory is null and edition is null " +
                    " or manufacturer=? and productCategory = ? and edition is null" +
                    " or manufacturer = ? and productCategory = ? and edition = ?"
            def cpList = CorporationProduct.executeQuery(hql, [
                    manufacturer,
                    manufacturer,
                    productCategory,
                    manufacturer,
                    productCategory,
                    editionStr
            ])
            //println "cpList="+cpList
            cpList.each {
                //def inf = IndustryFlaw.findAllByIndustryAndFlaw(it.corporation?.industry,flawInstance)
                def inf = IndustryFlaw.executeQuery(" from IndustryFlaw where industry=? and flaw=? and type=0", [
                        it.corporation?.industry,
                        flawInstance
                ])
                if (!inf) {
                    def industryFlaw = new IndustryFlaw()
                    industryFlaw.flaw = flawInstance
                    industryFlaw.creater = session.user
                    industryFlaw.type = 0
                    industryFlaw.industry = it.corporation?.industry
                    if (industryFlaw.save(flush: true)) {
                        net.sf.json.JSONObject jsonTemp1 = new net.sf.json.JSONObject()
                        net.sf.json.JSONObject json1 = toJsonObjectService.toJsonObject(industryFlaw)
                        jsonTemp1.put("operate", 0)
                        jsonTemp1.put("isPush", 1)
                        jsonTemp1.put("isLoophole", 0)
                        jsonTemp1.put("flawId", flawInstance.getId())
                        jsonTemp1.put("entity", json1.toString())
                        array.add(jsonTemp1)
                    }
                }
                def flawIndustryProduct = new FlawIndustryProduct()
                flawIndustryProduct.flaw = flawInstance
                flawIndustryProduct.industry = it.corporation?.industry
                flawIndustryProduct.corporationProduct = it
                flawIndustryProduct.creator = session.user
                flawIndustryProduct.type = 1
                if (flawIndustryProduct.save(flush: true)) {

                    net.sf.json.JSONObject jsonTemp1 = new net.sf.json.JSONObject()
                    net.sf.json.JSONObject json1 = toJsonObjectService.toJsonObject(flawIndustryProduct)
                    jsonTemp1.put("operate", 0)
                    jsonTemp1.put("isPush", 1)
                    jsonTemp1.put("isLoophole", 0)
                    jsonTemp1.put("flawId", flawInstance.getId())
                    jsonTemp1.put("entity", json1.toString())
                    array.add(jsonTemp1)

                }
            }
        }

        sendMsg(flawInstance, jsonImp, array)

/*
        if (flawInstance.push == 1) {
            //flawProductSynchro.save()

            try {
                jsonImp.put("date",array.toString())
                new SendMsgProducer(jsonImp,kafkaIp,kafkaTopic).start()
            }catch (Exception e) {
                def synchroErrData = new SynchroErrData();
                synchroErrData.errorData=jsonImp.toString();
                synchroErrData.errorReason=e.message.toString();
                synchroErrData.status=0;
                synchroErrData.topic=kafkaTopic;
                synchroErrData.save(flush: true)
            }

        }*/
        render ResultUtil.failResult("操作成功")
    }

    def synchronization = {
        def user = session.user
        println "user的值为:" + user.id
        String sql = "SELECT m.id as '厂商id',pi.id as '产品版本id',pc.id as '产品id',m.`name` as '厂商名称',pc.`name` as '产品名称',pi.`name` as '版本号' \n" +
                "from flaw_product fp LEFT JOIN flaw f ON fp.flaw_id = f.id LEFT JOIN product_info pi ON pi.id = fp.product_id\n" +
                "LEFT JOIN manufacturer m ON pi.manufacturer_id = m.id LEFT JOIN product_category pc ON pc.id = pi.product_category_id\n" +
                "WHERE f.number = ? and f.is_open =1 and f.`enable` = 1 and f.user_id = ?"
        def list = new ArrayList<Object>()
        List<Map<String, Object>> dataResult = null;

        if (params.cnvdNumber) {
            list.add(params.cnvdNumber.trim())

        }
        if (!user) {
            session.originalRequestParams = [controller: controllerName, action: actionName]
            return redirect(controller: "TUser", action: "login")
        } else {
            list.add(user.id)
        }
        dataResult = SQLUtil.getResult(sql, list)
        println "dataresult的值为:" + dataResult

        def listResult = ["dataResult": dataResult]

        render listResult as JSON
    }

    /**
     * 根据CVE编号查询漏洞
     */
    def cveNumberSearch = {
        if (!session.user) {
            // 去登录
            session.originalRequestParams = [controller: controllerName, action: actionName]
            return redirect(controller: "TUser", action: "login")
        }
        def flawInstance
        if (params.cveNumber) {
            def cveList = ReferenceInfo.findAllByReferenceNumberAndReferenceType(params.cveNumber.trim(), ReferenceType.get(1))
            if (cveList) {
                // 按照id升序排序并获取第一个元素（id最小的）
                def sortedCveList = cveList.sort { it.id }
                flawInstance = cveList[0].flaw
            }
        }

        [flawInstance: flawInstance]
    }

    def calScore = {

        def accessVector = MetricInfo.get(params.int('accessVector.id'))  //攻击途径
        def accessComplexity = MetricInfo.get(params.int('accessComplexity.id'))    //攻击复杂度
        def availabilityImpact = MetricInfo.get(params.int('availabilityImpact.id'))    //可用性
        def authentication = MetricInfo.get(params.int('authentication.id'))        //认证
        def confidentialityImpact = MetricInfo.get(params.int('confidentialityImpact.id'))    //机密性
        def integrityImpact = MetricInfo.get(params.int('integrityImpact.id'))    //完整性
        def baseMetricScore;
        /**
         * BaseScore =｛(0.6×Impact)+(0.4×Exploitability)–1.5｝×f(Impact)
         round_to_1_decimal(四舍五入到小数点后一位)
         基本度量值={(0.6×影响值)+(0.4×可用性值) –1.5}×f(影响值)
         Impact = 10.41×｛1–(1–ConfImpact) ×(1–IntegImpact) ×(1–AvailImpact) ｝
         影响值=10.41×{1–(1–机密性) ×(1–完整性) ×(1–可用性)}Exploitability = 20× AccessVector×AccessComplexity×Authentication
         可利用性值=20×攻击途径×攻击复杂度×认证
         f(impact)= 0 if Impact=0,     f(impact)= 1.176 otherwise
         */
        def yxz = 10.41 * (1 - (1 - confidentialityImpact.value) * (1 - integrityImpact.value) * (1 - availabilityImpact.value))

        def kyx = 20 * accessComplexity.value * accessVector.value * authentication.value
        def cc = 0
        if (yxz > 0) {
            cc = 1.176
        }
        baseMetricScore = ((0.6 * yxz) + (0.4 * kyx) - 1.5) * cc

        baseMetricScore = (double) Math.round(baseMetricScore * 10) / 10;

        def resStr
        if (baseMetricScore < 4) {
            resStr='低'
        } else if (baseMetricScore < 7) {
            resStr='中'
        } else if (baseMetricScore <= 10) {
            resStr='高'
        }

        render(text: '{"data": "' + resStr + '"}', contentType: "application/json")
        return
    }


    /**
     * 根据flaw_id查询漏洞
     */
    def flawDetailSearch = {
        if (!session.user) {
            // 去登录
            session.originalRequestParams = [controller: controllerName, action: actionName]
            return redirect(controller: "TUser", action: "login")
        }
        params.numPerPage = Math.min(params.numPerPage ? params.int('numPerPage') : 20, 100)
        params.pageNum = params.pageNum ? params.int('pageNum') : 1
        def offset = (params.int('pageNum') - 1) * params.int('numPerPage')
        def flawInstance = Flaw.get(params.int('flawId'))
        if (!flawInstance) {
            render ResultUtil.failResult("操作失败")
        }

        //影响产品
        def sql1 = "SELECT b.name,b.id from flaw_product a LEFT JOIN product_info b on a.product_id=b.id  where  a.flaw_id=" + flawInstance.id;
        def res1 = new groovy.sql.Sql(dataSource).rows(sql1)
        StringBuffer productName = new StringBuffer()
        StringBuffer productIds = new StringBuffer()
        for (int j = 0; j < res1.size(); j++) {
            if (j == 0) {
                productName.append(res1.get(j).get("name"));
                productIds.append(res1.get(j).get("id"));
            } else {
                productName.append(";" + res1.get(j).get("name"));
                productIds.append("," + res1.get(j).get("id"));
            }
        }


        //获取漏洞类型
        def flawTypesParamMiddle = FlawTypesParamMiddle.findAllByFlaw(flawInstance)
        //验证类型
        def exploitInstance
        def taskExploitId
        def baseHql = "from Task as b where b.targetTUser=:targetTUser and b.type=:type and b.enable=1 and b.flaw=:flaw"
        def queryParams = [targetTUser: session.user, type: Constants.FLAW_EXPOIT,flaw: flawInstance]
        def taskInstance = Task.executeQuery(baseHql, queryParams)[0]
        if (taskInstance) {
            if (taskInstance?.status == Constants.TASK_ING || taskInstance?.status == Constants.TASK_BACK || taskInstance?.status == Constants.TASK_SUBMIT) {
                if (taskInstance.compDateStr1() == '未超时' || taskInstance.compDateStr1() == '超时提交' || taskInstance.compDateStr1() == '准时提交') {
                    exploitInstance = taskInstance.exploit
                    taskExploitId = taskInstance.id
                }else if (session.user.timeoutEditor == 1 && flawInstance.isEvent == 0) {
                }else{
                    exploitInstance = taskInstance.exploit
                    taskExploitId = taskInstance.id
                }
            }
        }

        //处置信息
        def disposalInstance
        def taskDisposalId
        baseHql = "from Task as b where b.targetTUser=:targetTUser and b.type=:type and b.enable=1 and b.isExpoitPatch is null and b.flaw=:flaw"
        queryParams = [targetTUser: session.user, type: Constants.FLAW_PATCH,flaw: flawInstance]
        taskInstance = Task.executeQuery(baseHql, queryParams)[0]
        if (taskInstance) {
            if (taskInstance?.status == Constants.TASK_ING || taskInstance?.status == Constants.TASK_BACK || taskInstance?.status == Constants.TASK_SUBMIT) {
                disposalInstance = taskInstance.disposalInfo
                taskDisposalId = taskInstance.id
            }
        }
        //补丁信息
        def patchInfoInstance
        def patchInfoInstanceList = PatchInfo.createCriteria().list {
            eq("tuser", session.user)
            eq("enable", 1)
            eq("isDisposalTaskPatch", 0)
            eq("flaw", flawInstance)
            order('lastUpdated', 'desc')
            maxResults(1)
        }
        patchInfoInstance = patchInfoInstanceList.max { it.lastUpdated }
        if (!(patchInfoInstance?.status == Constants.PATCH_NOT_SUBMIT || patchInfoInstance?.status == Constants.PATCH_WAIT || patchInfoInstance?.status == Constants.PATCH_FAIL)) {
            patchInfoInstance = null;
        }
        //漏洞编辑时间
        def flawUseLogList = FlawWebUseLog.createCriteria().list {
            eq("flawId", flawInstance.getId())
            order('lastUpdated', 'desc')
            maxResults(1)
        }
        def latestLog = flawUseLogList.max { it.lastUpdated }
        //附件
        def attId = flawInstance?.attachment?.id
        def attachmentPdfInstance
        if (attId) {
            attachmentPdfInstance = AttachmentPdf.findAllByAttachmentId(flawInstance?.attachment?.id)
        }
        params.taskExploitId = taskExploitId
        params.taskDisposalId = taskDisposalId

        [flaw                : flawInstance, attPdfList: attachmentPdfInstance,
         flawTypesParamMiddle: flawTypesParamMiddle, exploitInstance: exploitInstance,
         disposalInstance    : disposalInstance, patchInfoInstance: patchInfoInstance,
         flawUseLog          : latestLog, productName: productName, productIds: productIds,
         obj                 : params]
    }
}
