package cnvd

import java.util.Date;

import com.cnvd.TUser;
import com.cnvd.User;

/**
 * 全量接口申请类
 * <AUTHOR>
 *
 */
class TotalFlawApi {
	TUser tuser  //前台申请
	User user //审核人员
	Date dateCreated //创建时间
	Date lastUpdated //审核时间
	Integer status = 0  //状态  1.审核通过  0.待审核  2.审核不通过
	static constraints = {
		tuser(nullable:true)
		user(nullable:true)
		dateCreated(nullable:true)
		lastUpdated(nullable:true)
		status(nullable:true)
	}
}
