package com.cnvd

import com.cnvd.productInfo.Manufacturer
/**
 * 
 * <AUTHOR>
 *
 */
class TUser {
	String email    //邮箱
	String nickName //用户昵称
	String address  //地址
	String work		//工作
	String password //密码
	String description //个人描述
	Integer gender=1200 //性别
	Date dateCreated //创建时间
	Date registTime //注册时间
	Date lastUpdated //更新时间
	Integer userType //用户类别(100200:个人 100201:支撑单位 100202:合作伙伴 100203:企业用户)
	String phoneNumber //移动电话
	Integer status	//用户状态 100300待审核 100301未激活 100304正常 100302停用
	String activeCode //激活码
	Integer integValue=0 //积分值
	String url	
	String userName //用户的真实姓名，如果是支撑单位则表示单位的全称
	String workplace //用户的工作单位(只适用于个人用户)

	/*String  contacts //联系人*/

	Integer enable=1

	Integer isTimeoutEditor //是否超时不能编辑

	Integer flawCount

	Manufacturer manufacturer //企业用户的厂商绑定
	Float honorValue =1 //荣誉值
	static mapping = {
		manufacturer lazy : false
	}
	static transients=['flawCount']
	//字段约束
	static constraints = {
		email(nullable: true, unique: true, email:true)
		nickName(nullable:true,unique:true)
		lastUpdated(nullable: true)
		address(nullable: true)
		/*contacts(nullable: true)*/
		work(nullable: true)
		password(blank: false)
		description(nullable:true)
		userType(nullable:true)
		gender(nullable: true)
		status(nullable:true)
		activeCode(nullable:true)
		phoneNumber(nullable: true)
		integValue(nullable:true)
		url(nullable:true)
		userName(nullable:true)
		workplace(nullable:true)
		manufacturer(nullable:true)
		enable(nullable:true)
		registTime(nullable:true)
		honorValue(nullable:true)
		isTimeoutEditor(nullable:true)
	}
	
}
