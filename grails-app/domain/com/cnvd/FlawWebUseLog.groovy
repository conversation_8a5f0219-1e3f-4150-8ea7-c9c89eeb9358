package com.cnvd

import com.cnvd.common.Attachment

class FlawWebUseLog {
	String title // 标题
	String number // 漏洞编号
	String formalWay // 漏洞解决方案
	String patchName // 补丁标题
	String patchDescription // 补丁描述
	String function // 补丁原理
	String patchUrl // 补丁链接
	Attachment attachment // 补丁附件
	Integer serverityId //漏洞严重程度

/*	Integer isStatus=1  //1一级审核2二级审核3三级审核9已经归档 -2作废 -1驳回 4补录漏洞未提交 5补录待审核漏洞 -9已使用*/
	Integer status=1  //1一级审核2二级审核3三级审核9已经归档 -2作废 -1驳回 4补录漏洞未提交 5补录待审核漏洞 -9已使用
	String content//漏洞所属IP
	Long flawId
	Long tUserId
	String referenceLink
	String description
	String productInfoId
	Date lastUpdated //更新日期
	Date dateCreated //创建日期
	String productName;
	static transients = ['productName']


	static constraints = {
		title(nullable:true)
		number(nullable:true)
		formalWay(nullable:true)
		serverityId(nullable:true)
		patchName(nullable:true)
		patchDescription(nullable:true)
		function(nullable:true)
		patchUrl(nullable:true)
		attachment(nullable:true)
		status(nullable:true)
		content(nullable:true)
		flawId(nullable:true)
		tUserId(nullable:true)
		referenceLink(nullable:true)
		lastUpdated(nullable:true)
		dateCreated(nullable:true)
	}
}
