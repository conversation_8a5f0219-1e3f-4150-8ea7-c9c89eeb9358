package com.cnvd

import com.cnvd.common.Attachment
import com.cnvd.flawInfo.BaseMetric
import com.cnvd.flawInfo.EnvironmentalMetric
import com.cnvd.flawInfo.TemporalMetric
import com.cnvd.productInfo.Manufacturer
import com.cnvd.productInfo.ProductCategory
import com.cnvd.productInfo.ProductInfo
import com.cnvd.utils.Constants
import com.cnvd.utils.MD5
import com.cnvd.utils.SQLUtil
import jxl.write.Blank

class FlawPoc {

    TUser user //创建人
    String flawName //漏洞名称
    Integer flawType //漏洞类型 0通用，1事件
    Integer flawLevel //漏洞等级 18高，19中，20低
    String flawSource; //漏洞来源
    String number //poc编号
    String cnvdNumber //cnvd编号
    String cve //cve编号
    String bid //bid编号
    String officialWebsite //厂商官网
    Integer softStyleId; //漏洞影响对象类型
    String description //poc漏洞描述
    String formalWay //官方修复建议
    String refLink //参考链接
    String ksRun //运行平台
    Integer appType //应用类型 1: 'http', 2: 'https', 3: 'other'
    String appPort //应用端口
    String environmentPath //环境路径
    String testUrl //测试url
    String pocScript //poc脚本
    Attachment attachment //poc文件
    Integer status=1; //漏洞poc状态 (1.一审、2.二审、3.三审、-1.驳回、-2.作废、4.归档)
    Date createTime //创建时间
    Date updateTime //更新时间
    Date findTime; //发现日期
    Integer isZeroDay //是否零日(1是、0否)
    Integer searchRules //搜索规则(0空、1fofa搜索语句、2钟馗之眼搜索语句、3谷歌语法搜索语句)
    String searchStatement //搜索语句
    Integer pathType//环境路径类型的值
    String pathName //搜索语句
    static transients=['pathType','pathName']//瞬时属性
    BaseMetric basemetric
    TemporalMetric temporalMetric
    EnvironmentalMetric environmentalMetric
    Float coefficient //加分系数
    Float added //额外积分
    Float total //总分
    Integer lastAction =1 //审核结果(1通过、2驳回)
    static mapping = {
    }

    static constraints = {
        user(nullable: true)
        flawName(nullable: true)
        flawType(nullable: true)
        flawLevel(nullable: true)
        number(nullable: true)
        cve(nullable: true)
        bid(nullable: true)
        description(nullable: true)
        formalWay(nullable: true)
        refLink(nullable: true)
        ksRun(nullable: true)
        appType(nullable: true)
        appPort(nullable: true)
        environmentPath(nullable: true)
        testUrl(nullable: true)
        pocScript(nullable: true)
        attachment(nullable: true)
        status(nullable: true)
        createTime(nullable: true)
        updateTime(nullable:true)
        isZeroDay(nullable: true)
        cnvdNumber(nullable: true)
        basemetric(nullable:true)
        temporalMetric(nullable:true)
        environmentalMetric(nullable:true)
        coefficient(nullable:true)
        added(nullable:true)
        total(nullable:true)
        lastAction(nullable: false)
    }

    //插入之前，系统自动生成poc编号
    def beforeInsert= {
//        isu = 0
        Calendar cal = Calendar.getInstance();//使用日历类
        int year = cal.get(Calendar.YEAR);//得到年
        def tempNumSql = "select poc_nextval('flaw_poc_id_" + year + "') as num"
        def tempNumRes = SQLUtil.getResult(tempNumSql, [])
        System.out.println("tempNumRes=="+tempNumRes);
        def maxTempNumStr=1
        if(tempNumRes == null || tempNumRes[0] == null || tempNumRes[0].num==null){
            String sql = "INSERT INTO cnvd_flawpoc_sequence(NAME,current_value,_increment) VALUES('flaw_poc_id_"+year+"',1,1)"
            SQLUtil.execute(sql)
        }else{
            maxTempNumStr = tempNumRes[0].num
        }
        def numberInt = year
        number = "CNVDGZ-C-" + year + "-" + String.format("%05d", maxTempNumStr);
    }
}