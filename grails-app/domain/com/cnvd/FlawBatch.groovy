package com.cnvd

import com.cnvd.common.Attachment

class FlawBatch {
	TUser user //上报人
	Date submitTime //上报时间
	Attachment attachment//上传文件
	String title // 漏洞标题
	String oldTitle // 原来漏洞标题
	Integer num //条数
	FlawTypes flawTypes;//漏洞成因分类
	Integer isEvent = 0 //0：通用型 1：事件型
	String productEdition //影响产品及版本
	String discovererName //发现者姓名
	String unitName //发现者单位
	Attachment pocAttachment //POC验证文件
	Integer status;//审核状态（0：待审核、1：待审通过 2：驳回 3：驳回待审）
	Date auditingTime;//审核时间
	Date foundTime //发现时间
	String cveId;
	String cnvdId;

	static constraints = {
		user(nullable: false)
		attachment(nullable: true)
		num(nullable: true)
		flawTypes(nullable: true)
		isEvent(nullable: true)
		productEdition(nullable: true)
		discovererName(nullable: true)
		unitName(nullable: true)
		pocAttachment(nullable: true)
		auditingTime(nullable: true)
		cveId(nullable: true)
		cnvdId(nullable: true)
		oldTitle(nullable: true)
	}
}
