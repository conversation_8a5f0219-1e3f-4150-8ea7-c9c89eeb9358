package com.cnvd

import java.util.Date;
import com.cnvd.TUser
import com.sun.xml.internal.org.jvnet.mimepull.Data;

/**
 * 权限申请表
 * <AUTHOR>
 *
 */
class Permission {

	TUser tuser  //前台申请
	User user //审核人员
	Integer mapping  //对应权限映射编号  1.扫描网站  2.验证环境  3.全量接口
	Date dateCreated //创建时间
	Date lastUpdated //审核时间
	Integer status = 0  //状态  1.审核通过  0.待审核  2.审核不通过
	static constraints = {
		tuser(nullable:true)
		user(nullable:true)
		mapping(nullable:true)
		dateCreated(nullable:true)
		lastUpdated(nullable:true)
		status(nullable:true)
	}
}
