package com.cnvd

import java.util.Date;

/**
 * 验证环境创建虚拟机返回的参数类
 * <AUTHOR>
 *
 */
class EnvironmentReceive {

	String cpuNum
	String cupSpeed
	String createDate
	String description
	String imageUuid
	String instanceOfferingUuid
	String memorySize
	String name
	String platform
	String state
	String uuid
	String ip
	String vncurl  //vnc地址
	String receiveState //接收状态
	String remark //备注
	Date dateCreated //录入时间
	Date lastUpdated //修改时间
	
	static mapping = {
		vncurl type : 'text'
		remark type : 'text'
	}
    static constraints = {
		cpuNum(nullable:true)
		cupSpeed(nullable:true)
		createDate(nullable:true)
		description(nullable:true)
		imageUuid(nullable:true)
		instanceOfferingUuid(nullable:true)
		memorySize(nullable:true)
		name(nullable:true)
		platform(nullable:true)
		state(nullable:true)
		uuid(nullable:true)
		ip(nullable:true)
		vncurl(nullable:true)
		receiveState(nullable:true)
		remark(nullable:true)
    }
}
