package com.cnvd.asset

import com.cnvd.TUser
import com.cnvd.cer.CA

/**
 * 资产信息导出漏洞信息接口中资产任务实体类
 * <AUTHOR>
 *
 */
class AssetTask {
	TUser user  //任务发起人
	Date dateCreated  //查询时间
	Date lastUpdated
	Integer status  //任务状态 0 未开始任务 1 任务进行中 2任务已完成
	Date startDate  //查询任务开始时间
	Date endDate  //查询任务结束时间
	CA ca
	
    static constraints = {
    }
	
	def getStatusStr(){
		def statusStr = ""
		switch(status){
			case 0:
				statusStr = "任务未开始"
				break
			case 1:
				statusStr = "任务进行中"
				break
			case 2:
				statusStr = "任务已完成"
				break
			default:
				statusStr = "任务未开始"
		}
		return statusStr
	}
}
