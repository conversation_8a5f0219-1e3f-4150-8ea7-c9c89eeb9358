package com.cnvd.asset

/**
 * 资产信息导出漏洞信息接口中资产信息实体类
 * <AUTHOR>
 *
 */
class AssetInfo {
	
	String equipmentId  //设备id
	String equipmentName  //设备名称
	String equipmentModel  //设备型号
	String equipmentManu   //设备厂商
	String equipmentType  //设备类型
	String osName   //操作系统名称
	String osVersion  //操作系统版本
	String manufacturerName  //软件厂商
	String productCategoryName  //软件名称
	String edition	//软件厂商
	Integer taskId //任务id  标识
	
    static constraints = {
		equipmentId(nullable:true)
		equipmentName(nullable:true)
		equipmentModel(nullable:true)
		equipmentManu(nullable:true)
		equipmentType(nullable:true)
		osName(nullable:true)
		osVersion(nullable:true)
		manufacturerName(nullable:true)
		productCategoryName(nullable:true)
		edition(nullable:true)
    }
}
