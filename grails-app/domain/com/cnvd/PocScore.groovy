package com.cnvd

class PocScore {
    FlawPoc flawPoc //漏洞poc
    Float scopeCoefficient //时效性和知悉范围
    Integer scopeCoefficientType //时效性和知悉范围类型
    Float standardCoefficient //编写规范和执行效率
    Integer standardCoefficientType  //编写规范和执行效率类型
    Float completionCoefficient //信息完整度
    Integer completionCoefficientType //信息完整度类型
    Float featureCoefficient //网络监测特征情况
    Integer featureCoefficientType //网络监测特征情况类型

    static mapping = {
    }

    static constraints = {
        flawPoc(nullable: true)
        scopeCoefficient(nullable: true)
        standardCoefficient(nullable: true)
        completionCoefficient(nullable: true)
        featureCoefficient(nullable: true)
        scopeCoefficientType(nullable: true)
        standardCoefficientType(nullable: true)
        completionCoefficientType(nullable: true)
        featureCoefficientType(nullable: true)
    }
}
