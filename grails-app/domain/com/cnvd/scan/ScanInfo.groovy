package com.cnvd.scan

import java.util.Date;

import com.cnvd.TUser;
/**
 * 扫描漏洞后，返回结果信息
 * <AUTHOR>
 *
 */
class ScanInfo {
	TUser user //URL新建人
	String scanTaskId  //扫描漏洞任务ID
	String entry	//扫描漏洞的入口
	Date createtime //任务创建时间
	Date starttime  //任务启动时间
	Date canceltime //任务取消时间，如果该任务未取消，则值为空
	Date finishtime //任务完成时间
	Integer progress  //扫描进度
	String error //扫描错误时，发生错误的原因
	String reportUrl //扫描报告的转发地址。
	String level // 安全 不安全
	Integer	high	//高危漏洞数量
	Integer	middle	//中危漏洞数量
	Integer	low		//低危漏洞数量
	Integer	vulsCount	//漏洞数量
	Integer	noticeCount		//提示数量
	
	static constraints = {
		user(nullable:true)
		scanTaskId(nullable:true)
		entry(nullable:true)
		createtime(nullable:true)
		starttime(nullable:true)
		canceltime(nullable:true)
		finishtime(nullable:true)
		progress(nullable:true)
		error(nullable:true)
		reportUrl(nullable:true)
		level(nullable:true)
		high(nullable:true)
		middle(nullable:true)
		low(nullable:true)
		vulsCount(nullable:true)
		noticeCount(nullable:true)
		
	}
}
