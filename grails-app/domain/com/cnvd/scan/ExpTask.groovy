package com.cnvd.scan

import java.util.Date;

import com.cnvd.TUser;
import com.cnvd.User

class ExpTask {
	User creator  //后台上传管理员
	TUser user //任务创建人
	String name //任务名称，必填，4-24个字符。
	String targets  //验证地址，必填，可填多个，换行符分割。
	String tags  //关键词，非必填，可填多个，换行符分割
	Integer level=0  //任务优先级，默认优先为0。  0低级 1中级 2高级 3极高
	Integer enable=1  //1可用 0删除 --软删除
	Integer status=1  // 0扫描失败  1扫描中 2扫描完成 3审核失败 4审核通过 
	String code  //提交任务后返回的代码
	String msg  //提交任务后返回的描述
	String task_id  //返回的任务id
	AttExpPdf expPdf  //任务完成后返回的pdf
	AttExpHtml expHtml  //任务完成后返回的html
	
	Date dateCreated //创建日期
	Date lastUpdated //更新日期
	
	static mapping = {
		targets type:'text'
		tags type:'text'
	}
    static constraints = {
		tags(nullable:true)
		code(nullable:true)
		msg(nullable:true)
		task_id(nullable:true)
		expPdf(nullable:true)
		expHtml(nullable:true)
		creator(nullable:true)
	}
}
