package com.cnvd.industryLibrary

import com.cnvd.TUser
import com.cnvd.productInfo.Manufacturer
import com.cnvd.productInfo.ProductCategory

/**
 * 行业单位资产domain类
 * <AUTHOR>
 *
 */
class CorporationProduct {
	
	Corporation corporation //行业单位
	Manufacturer manufacturer //产品单位
	ProductCategory productCategory //产品类别
	String edition //版本 
	Date dateCreated
	Date lastUpdated
	TUser creater //创建者
    static constraints = {
		productCategory(nullable:true)
		edition(nullable:true)
    }
}
