package com.cnvd

import com.cnvd.common.Attachment
import com.cnvd.flawInfo.*
import com.cnvd.patchInfo.PatchInfo
import com.cnvd.points.Points
import com.cnvd.productInfo.Manufacturer
import com.cnvd.utils.Constants
import com.cnvd.utils.MD5
import com.cnvd.utils.SQLUtil

class Flaw {
	String title // 标题
	String number // 漏洞编号
	Integer isFirst //是否是首次公开
	Integer isZero //是否零日漏洞
	Date foundTime //发现时间
	Date dateCreated //创建日期
	Date lastUpdated //更新日期
	Integer rank=0
	Integer status=1  //1一级审核2二级审核3三级审核9已经归档 -2作废 -1驳回 4补录漏洞未提交 5补录待审核漏洞 -9已使用
	Integer isu  //是否前台上报 1否 0是
	Integer isg=0//是否可以直接归档
	Integer isv=0 //是否验证  1验证完成 0 未验证 2验证中
	Integer ivp=0 //是否处置  1已处置 0未处置 2处置中
	Integer isp=0 //是否补丁  1已收录补丁 0 添加补丁 2收录补丁中
	Integer isHot=0 //是否是热点漏洞 1是 0不是
	Integer causeId  // 漏洞产生原因
	Integer threadId  //漏洞引发的威胁（11:管理员访问权限获取，12:普通用户访问权限获取，13:未授权的信息泄露，14:未授权的信息修改，15:拒绝服务，16:其它，17:未知）
	Integer serverityId //漏洞严重程度
	Integer positionId //漏洞利用的攻击位置（21:远程，22:本地，23:其他）
	Integer softStyleId //漏洞影响对象类型（27:操作系统漏洞，28:应用程序漏洞，29:WEB应用漏洞，30:数据库漏洞，31:网络设备（交换机、路由器等网络端设备），31:智能设备（物联网终端设备）漏洞，33:安全产品漏洞）
	Integer viewCount=0  //漏洞查看次数
	Integer enable=1  //1可用 0删除
	Attachment attachment
	Integer isAttShow // 漏洞附件是否显示标志位 0否 1是
	Manufacturer manufacturer  //所属厂商
	Date storageTime  //归档时间
	Date submitTime //漏洞报送时间
	Date openTime//公开时间，为漏洞页面的公开时间，程序修改，人工不能修改。
	Date intendOpenTime//拟公开时间，拟公开时间（初始值是目前公开时间），可以人工修改
	TUser user //漏洞上报人
	BaseMetric basemetric //基本度量评分
	TemporalMetric temporalMetric //时间度量评分
	EnvironmentalMetric environmentalMetric //环境度量评分
	DetailedInfo detailedInfo //漏洞详细信息
	String discovererName //发现者姓名
	String referenceLink //漏洞参考链接
	Integer isOpen=0 //是否公开0否1是
	Flaw parentFlaw //关联漏洞
	Integer isEvent=0 //是否为事件型漏洞 0否 1是
	Integer currencyType //通用型漏洞的类型
	Integer clickNum=0 //漏洞点击数
	Integer commentCount=0 //评论数量
	Integer concernCount=0 //漏洞关注数量
	String downCode //下载附件时参数
	BatchFlaw batchFlaw //批量上报漏洞id号，用于标识此漏洞是否为批量上传
	Integer isOriginal //是否原创 0是 1不是 2未知
	String oldNumber //记录原CNVD编号
	String tempNumber //记录漏洞的临时编号
	Integer isAdditional=0  //是否为补录漏洞0 否 1 是
	String fromWhere	//漏洞信息的来源	(后加字段)
	Points points //原创漏洞积分奖金 zp
	String url   //
	String titlel
	String remark //备注，用于审核人员参考资产信息
    String province//漏洞所属省份
    String city//漏洞所属城市
    String flowIP//漏洞所属IP
	String flowType//事件型漏洞的类型
    String unitName//漏洞单位
	String ministriesName//漏洞单位的行业
	Integer isCertificate=2 //是否制作证书
	FlawTypes flawTypes;//漏洞类型
	Integer czff=0 //是否处置分发  0待处置分发 1已处置分发
	Integer push//是否推送 1:是 0:否
	Integer parentId //排序标识
	Integer isRepeat = 0 //按照 url+漏洞类型 组合判断一条漏洞数据是否跟库里重复 {0不重复 1重复}
	String isProductComponents = "0" // 是否产品组件漏洞 0否 1 是
	Integer lastAction = 1
	static mapping = { manufacturer lazy : false }
	//static transients=['concernCount']
	static constraints = {
        province(nullable:true)
        city(nullable:true)
		number(nullable:true)
        flowIP(nullable:true)
		rank(nullable:true)
		isFirst (nullable:true)
		isZero(nullable:true)
		user(nullable:false)
		isu(nullable:true)
		isg(nullable:true)
		isv(nullable:true)
		ivp(nullable:true)
		isp(nullable:true)
		isHot(nullable:true)
		causeId  (nullable:true)
		threadId (nullable:true)
		serverityId (nullable:true)
		positionId (nullable:true)
		softStyleId (nullable:true)
		viewCount(nullable:true)
		attachment(nullable:true)
		manufacturer(nullable:true)
		storageTime(nullable:true)
		openTime(nullable:true)
		intendOpenTime(nullable:true)
		basemetric(nullable:true)
		temporalMetric(nullable:true)
		environmentalMetric(nullable:true)
		discovererName(nullable:true)
		referenceLink(nullable:true)
		isOpen(nullable:true)
		parentFlaw(nullable:true)
		isEvent(nullable:true)
		currencyType(nullable:true)
		clickNum(nullable:true)
		commentCount(nullable:true)
		concernCount(nullable:true)
		downCode(nullable:true)
		batchFlaw(nullable:true)
		isOriginal(nullable:true)
		isAttShow(nullable:true)
		oldNumber(nullable:true)
		tempNumber(nullable:true)
		isAdditional(nullable:true)
		fromWhere(nullable:true)
		points(nullable:true)	//原创漏洞积分奖金 zp
		url(nullable:true)
		titlel(nullable:true)
		remark(nullable:true)
		province(nullable:true)
		city(nullable:true)
		flowIP(nullable:true)
		flowType(nullable:true)
		unitName(nullable:true)
		ministriesName(nullable:true)
		isCertificate(nullable:true)
		flawTypes(nullable:true)
		czff(nullable:true)
		push(nullable:true)
		parentId(nullable:true)
		isRepeat(nullable:true)
		isProductComponents(nullable:true)
	}
	//def beforeInsert={ isu=0 }
	//插入之前，系统自动生成漏洞编号
	def beforeInsert={
		isu=0
		Calendar cal=Calendar.getInstance();//使用日历类
		int year = cal.get(Calendar.YEAR);//得到年
		def tempNumSql = "select _nextval('flaw_id_" + year + "') as num"
		def tempNumRes = SQLUtil.getResult(tempNumSql,[])
		def maxTempNumStr=1
		if(tempNumRes == null || tempNumRes[0] == null || tempNumRes[0].num==null){
			String sql = "INSERT INTO cnvd_flaw_sequence(NAME,current_value,_increment) VALUES('flaw_id_"+year+"',1,1)"
			SQLUtil.execute(sql)
		}else{
			maxTempNumStr = tempNumRes[0].num
		}
		tempNumber = "CNVD-C-" + year + "-" + String.format("%05d", maxTempNumStr);
		downCode = MD5.getMD5Str(MD5.getMD5Str(String.valueOf(id))+MD5.getMD5Str(Constants.getRandomNumber(6).toString()));
		//this.save()

		/*
		 Calendar cal=Calendar.getInstance();//使用日历类
		 int year=cal.get(Calendar.YEAR);//得到年
		 def lockSql = "lock table flaw write;"
		 SQLUtil.execute(lockSql);
		 def tempNumSql = "select max(temp_number) maxTN from flaw where temp_number like 'CNVD-C-"+year+"%'"
		 def tempNumRes = SQLUtil.getResult(tempNumSql,[])
		 println "tempNumRes="+tempNumRes
		 def maxTempNumStr = tempNumRes[0].maxTN
		 println "maxTempNumStr="+maxTempNumStr
		 if(maxTempNumStr == null){
		 tempNumber = "CNVD-C-"+year+"-"+ String.format("%05d", 1);
		 }else{
		 def numberInt = Integer.parseInt(maxTempNumStr.substring(maxTempNumStr.length()-5,maxTempNumStr.length()))+1
		 tempNumber="CNVD-C-"+year+"-"+ String.format("%05d", numberInt);
		 }
		 downCode = MD5.getMD5Str(MD5.getMD5Str(String.valueOf(id))+MD5.getMD5Str(Constants.getRandomNumber(6).toString()));
		 this.save()*/

		/*def unlockSql = "unlock tables;"
		 SQLUtil.execute(unlockSql);*/

	}
	def getProduct(){
		def products=FlawProduct.findAllByFlaw(this)
	}
	def getReference(){
		def referenceList=ReferenceInfo.findAllByFlaw(this)
	}
	def getCVEReference(){
		def referenceList=ReferenceInfo.findAllByFlawAndReferenceType(this,ReferenceType.get(1))
	}
	def getBIDReference(){
		def referenceList=ReferenceInfo.findAllByFlawAndReferenceType(this,ReferenceType.get(2))
	}
	def getOtherReference(){
		def referenceList=ReferenceInfo.findAllByFlawAndReferenceType(this,ReferenceType.get(3))
	}
	def getFlawUrl(){
		def flawUrl=FlawUrl.findByFlaw(this)
	}
	def getPatchInfo(){
		return PatchInfo.findByFlawAndEnable(this,1)
	}
	def getExploit(){
		return Exploit.findByFlawAndEnable(this,1)
	}
}
