package com.cnvd

/**
 * 
 * <AUTHOR>
 * @description 邮件
 */
class Mail {

	String fromEmail
	String toEmail
	String title
	String content
	Integer isMust=1
	Integer isHandled
	Date createDate

	static mapping = { version false }

	static constraints = {
		fromEmail(nullable: true)
		toEmail(nullable: true)
		isMust(nullable: true)
		title(nullable: true)
		content(nullable: true)
		createDate(nullable: true)
	}
}
