package com.cnvd.common

import com.cnvd.Flaw
import com.cnvd.TUser
import com.cnvd.User
import com.cnvd.flawInfo.Exploit
import com.cnvd.patchInfo.DisposalInfo
import com.cnvd.patchInfo.PatchInfo
import com.cnvd.utils.Constants

class Task {
    String info //任务描述
    String title //任务标题
    Integer enable = 1 //任务可用 0任务不可用
    User creater  //任务创建者
    User targetUser //指定任务完成人 后台
    TUser targetTUser  //前台用户
    TUser targetTUserDisposal  //前台用户  处置
    Integer targetType //1后台 2前台
    Integer status = 1 //1进行中 2完成 3取消 4已提交 5驳回 6待处置下发 7已处置下发
    Date completeDate //任务最后完成时间
    Integer isCompleteDateSyn = 0 //超时时间是否和创建时间同步
    Date dateCreated
    Date lastUpdated //更新日期
    Date realCompleteDate //任务真实完成时间
    Integer type //任务类型 1验证 2利用 3处置 4处置分发
    Flaw flaw //关联漏洞
    String completeInfo //任务完成描述
    Date submitDate //任务提交时间
    Date auditDate //任务审核时间
    Attachment attachment
    Exploit exploit
    PatchInfo patchInfo //补丁
    Integer score1
    Integer score2
    Integer score3
    String downCode
    Integer tag = 0 //0表示没有进行推送1代表进行推送了
    DisposalInfo disposalInfo //处置
    Integer isExpoitPatch //是否验证&处置 默认否 1是
    static constraints = {
        realCompleteDate(nullable: true)
        info(nullable: true)
        completeInfo(nullable: true)
        submitDate(nullable: true)
        isCompleteDateSyn(nullable: true)
        auditDate(nullable: true)
        attachment(nullable: true)

        score1(nullable: true)
        score2(nullable: true)
        score3(nullable: true)
        targetTUser(nullable: true)
        targetTUserDisposal(nullable: true)
        targetUser(nullable: true)
        exploit(nullable: true)
        patchInfo(nullable: true)
        downCode(nullable: true)
        tag(nullable: true)
        disposalInfo(nullable: true)
        isExpoitPatch(nullable: true)
    }

    String statusStr() {
        String statusStr = ''
        switch (status) {
            case 1:
                statusStr = '进行中'
                break;
            case 2:
                statusStr = '完成'
                break;
            case 3:
                statusStr = '取消'
                break;
            case 4:
                statusStr = '已提交'
                break;
            case 5:
                statusStr = '驳回'
                break;
            case 6:
                statusStr = '待处理'
                break;
            case 7:
                statusStr = '已处理'
                break;
            default:
                statusStr = '未知'
                break;
        }
        return statusStr
    }

    String compDateStr() {
        if (status == 1) {
            if (new Date().getTime() - completeDate.getTime() > 0) {
                return "已超时"
            } else {
                return "未超时"
            }
        } else if (status == 2) {
            if (submitDate != null) {
                if (submitDate.getTime() - completeDate.getTime() > 0) {
                    return "超时完成"
                } else {
                    return "准时完成"
                }
            } else {
                if (realCompleteDate.getTime() - completeDate.getTime() > 0) {
                    return "超时完成"
                } else {
                    return "准时完成"
                }
            }
        } else if (status == 3) {
            return "已取消"
        } else if (status == 4) {
            if (submitDate.getTime() - completeDate.getTime() > 0) {
                return "超时提交"
            } else {
                return "准时提交"
            }

        } else if (status == 6) {
            if (new Date().getTime() - completeDate.getTime() > 0) {
                return "已超时"
            } else {
                return "未超时"
            }
        } else if (status == 7) {
            if (new Date().getTime() - completeDate.getTime() > 0) {
                return "已超时"
            } else {
                return "未超时"
            }
        }

    }

    String compDateStr1() {
        if (status == 1) {
            if (new Date().getTime() - completeDate.getTime() > 0) {
                return "已超时"
            } else {
                return "未超时"
            }
        } else if (status == 2) {
            if (realCompleteDate.getTime() - completeDate.getTime() > 0) {
                return "超时完成"
            } else {
                return "准时完成"
            }
        } else if (status == 3) {
            return "已取消"
        } else if (status == 4) {
            if (submitDate.getTime() - completeDate.getTime() > 0) {
                return "超时提交"
            } else {
                return "准时提交"
            }

        } else if (status == 5) {
            return "驳回"
        }

    }

    String typeStr() {

        String typeStr = ''
        switch (type) {
            case 1:
                if (isExpoitPatch != null && isExpoitPatch == 1) {
                    typeStr = '验证&处置'
                } else {
                    typeStr = '验证'
                }
                break;
            case 2:
                typeStr = '利用'
                break;
            case 3:
                if (isExpoitPatch != null && isExpoitPatch == 1) {
                    typeStr = '验证&处置'
                } else {
                    typeStr = '处置'
                }
                break;
            case 4:
                typeStr = '处置分发'
                break;
            default:
                typeStr = '未知'
                break;
        }
        return typeStr
    }

    String backStr() {
        TaskLog taskLog = TaskLog.findAllByTaskAndOperating(this, Constants.TASK_BACK, [max: 1, sort: "dateCreated", order: "desc", offset: 0])[0]

        return taskLog.content
    }

    String targetTypeStr() {
        if (targetType == Constants.TASK_TYPE_USER) {
            return "后台"
        } else {
            return "前台"
        }

    }

    String targetUserName() {
        if (targetType == Constants.TASK_TYPE_USER) {
            if (targetUserId == null) {
                return ""
            } else {
                return targetUser.userName
            }
        } else {
            if (targetTUserId == null) {
                return ""
            } else {
                return targetTUser.nickName
            }
        }
    }
}
