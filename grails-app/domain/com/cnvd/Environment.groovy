package com.cnvd


import java.util.Date;

/**
 * 验证环境
 * <AUTHOR>
 *
 */
class Environment {

	Integer number //实例编号
	String name //实例名称
	String instance //实例文件（路径）
	String md5 //MD5值
	Date insDateCreate //实例创建时间
	Date insDateUpdate //实例变更时间
	String edition	//版本控制
	String vulType  //覆盖漏洞类型
	String vulName  //覆盖漏洞名称
	String cnvdNum  //CNVD编号
	String cveNum  //cve编号
	String bidNum  //bid编号
	String position //验证方式
	String permission  //授权权限
	String ise //是否有利用代码
	String expContent  //利用代码文件路径
	String isp  //是否有补丁
	String patchFile  //补丁信息路径
	String introduction //镜像的系统，版本或软件等介绍
	Integer status  //状态  0.无效 1.有效
	Integer enable = 1  //状态  0.删除 1.有效

	Date dateCreated //录入时间
	Date lastUpdated //修改时间

	static mapping = {
		instance type:'text'
		introduction type : 'text'
	}
	static constraints = {
		number(nullable:true)
		name(nullable:true)
		instance(nullable:true)
		md5(nullable:true)
		insDateCreate(nullable:true)
		insDateUpdate(nullable:true)
		edition(nullable:true)
		vulType(nullable:true)
		vulName(nullable:true)
		cnvdNum(nullable:true)
		cveNum(nullable:true)
		bidNum(nullable:true)
		position(nullable:true)
		permission(nullable:true)
		ise(nullable:true)
		expContent(nullable:true)
		isp(nullable:true)
		patchFile(nullable:true)
		introduction(nullable:true)
		status(nullable:true)
		enable(nullable:true)
	}
}
