package com.cnvd.patchInfo

import com.cnvd.Flaw
import com.cnvd.User
import com.cnvd.common.Attachment


class PatchInfoNew {

	String patchId; //补丁编号
	String patchName; //补丁名称
	String patchDescription; //补丁描述
	String patchUrl; //补丁链接
	String patchExurl; //补丁扩展链接
	Attachment attachment; //补丁附件
	Date openTime;  //补丁发布时间
	String status="0"; //0:未提交  1:已提交  2:审核未通过 3:审核通过
	String dealDescription; //审核意见
	long clickNumber=0; //点击数
	String downCodeMd5; //补丁附件MD5值
	Flaw flaw; //漏洞信息
	User user; //后台用户
	Date dateCreated; //创建日期
	Date lastUpdated; //更新日期
	
	static mapping = {
		patchDescription type : 'text'
		dealDescription type : 'text'
	}
	
    static constraints = {
		patchId(nullable: true)
		patchName(nullable: true)
		patchDescription(nullable: true)
		patchUrl(nullable: true)
		patchExurl(nullable: true)
		attachment(nullable: true)
		openTime(nullable: true)
		dealDescription(nullable: true)
		downCodeMd5(nullable: true)
		flaw(nullable: true)
		user(nullable: true)
		lastUpdated(nullable:true)
    }
	
	//插入之前，系统自动生成补丁编号
	def afterInsert={
		Calendar cal=Calendar.getInstance();//使用日历类
		int year=cal.get(Calendar.YEAR);//得到年
		patchId="CNPD-"+year+"-"+ String.format("%05d", id);
		downCodeMd5 = MD5.getMD5Str(MD5.getMD5Str(String.valueOf(id))+ MD5.getMD5Str(Constants.getRandomNumber(6).toString()));
		this.save();
	}
}
