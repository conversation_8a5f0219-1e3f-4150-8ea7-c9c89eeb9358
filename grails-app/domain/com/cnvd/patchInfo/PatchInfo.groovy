package com.cnvd.patchInfo

import com.cnvd.Flaw
import com.cnvd.TUser
import com.cnvd.User
import com.cnvd.common.Attachment
import com.cnvd.utils.Constants
import com.cnvd.utils.MD5


class PatchInfo {
	
	String patchId; //补丁编号
	String patchName; //补丁名称
	String valid; //验证信息
	Flaw flaw; //漏洞信息
	TUser tuser; //贡献者信息
	User user; //后台用户
	String patchDescription; //补丁描述
	String function; //验证原理
	String patchUrl; //补丁链接
	String internalPatchFile; //
	String createSource; //创建来源 1：后台	2：前台	3：后台代前台
	long clickNumber; //点击数
	Date dateCreated; //创建日期
	Date lastUpdated; //更新日期
	String status="0"; //0:未提交  1:已提交  2:审核未通过 3:审核通过
	String dealDescription; //审核意见
	Attachment attachment //补丁的附件
	Integer enable=1 //0 表示禁用 1表示可用
	Integer isDisposalTaskPatch=0 //0 表示补丁信息 1表示处置任务的补丁信息
	String downCode
	String tel
	String remark //备注
	
	static mapping = {
		//autoTimestamp false
		patchDescription type : 'text'
	}
    static constraints = {
		patchId(nullable: true)
		patchName(nullable: true)
		lastUpdated(nullable: true)
		valid(nullable: true)
		patchDescription(nullable: true)
		function(nullable: true)
		patchUrl(nullable: true)
		internalPatchFile(nullable: true)
		clickNumber(nullable: true)
		tuser(nullable: true)
		user(nullable: true)
		dealDescription(nullable: true)
		attachment(nullable:true)
		enable(nullable:true)
		downCode(nullable:true)
		tel(nullable:true)
		remark(nullable:true)
		isDisposalTaskPatch(nullable:true)
    }
	
	//插入之前，系统自动生成补丁编号
	def afterInsert={
		
		Calendar cal=Calendar.getInstance();//使用日历类
		int year=cal.get(Calendar.YEAR);//得到年
		patchId="CNPD-"+year+"-"+ String.format("%05d", id);
		downCode = MD5.getMD5Str(MD5.getMD5Str(String.valueOf(id))+ MD5.getMD5Str(Constants.getRandomNumber(6).toString()));
		this.save();
	}
	def getPatchInfoTypeStr(){
		if(createSource=='1'){
			return "后台处置"
		}else if(createSource=='2'){
			return "前台处置"
		}else {
			return "后台代替前台处置"
		}
	}
}
