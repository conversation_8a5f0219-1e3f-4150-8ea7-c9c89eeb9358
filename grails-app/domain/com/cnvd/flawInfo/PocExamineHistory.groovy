package com.cnvd.flawInfo

import com.cnvd.FlawPoc


class PocExamineHistory {
    FlawPoc flawPoc //漏洞poc
    Integer status //审核结果 1通过、2驳回、3作废
    Integer examineNode //审核节点 1一审、2二审、3三审
    String examineOption //审核意见
    Date examineTime //审核时间
    Date updateTime //修改时间

    static mapping = {
    }

    static constraints = {
        flawPoc(nullable: true)
        status(nullable: true)
        examineNode(nullable: true)
        examineOption(nullable: true)
        examineTime(nullable: true)
        updateTime(nullable: true)
    }

}
