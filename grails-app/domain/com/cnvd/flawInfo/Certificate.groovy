package com.cnvd.flawInfo

import com.cnvd.Flaw
import com.cnvd.TUser
import com.cnvd.common.Attachment

class Certificate {
	
	String c_id //证书编号
	Date awardTime //发证时间
	String awardCompany //发证单位
	Flaw flaw //对应的漏洞
	String leakType //漏洞类型
	TUser tuser //前台漏洞上报者
	Date dateCreated //生成时间
	Attachment pdfAttachment //证书附件(pdf)
	Attachment signatureAttachment //证书签名附件
	byte cerType //用于标识证书的类型 1为报送证书 2为原创证书
	Integer isOld=0
	String downCode
	
    static constraints = {
		flaw(nullable:true)
		leakType(nullable:true)
		pdfAttachment(nullable:true)
		signatureAttachment(nullable:true)
		isOld(nullable:true)
		downCode(nullable:true)
    }
}
