package com.cnvd.flawInfo

import com.cnvd.Flaw
import com.cnvd.TUser
import com.cnvd.common.Attachment
import com.cnvd.utils.Constants
import com.cnvd.utils.MD5

class Exploit {
	Integer exploitType //验证类型 1后台2前台3后台代替前台
	String exploitName //验证名称
	TUser tuser //前台验证用户
	String concept //验证原理
	String poc //验证poc
	String suggestion//验证建议
	Date exploitTime //验证日期
	Date dateCreated //创建日期
	Date lastUpdated //更新日期
	Flaw flaw
	Integer status //验证状态 1通过审核2等待审核3未通过审核4未提交
	String content //前台验证 后台审核意见
	Attachment attachment //验证附件
	Integer enable=1
	String referenceLink //验证信息参考链接
	String downCode
	static constraints = {
		tuser(nullable:true)
		suggestion(nullable:true)
		poc(nullable:true)
		concept(nullable:true)
		exploitTime(nullable:true)
		exploitName(nullable:true)
		content(nullable:true)
		attachment(nullable:true)
		referenceLink(nullable:true)
		downCode(nullable:true)
	}
	def afterInsert = {
		downCode = MD5.getMD5Str(MD5.getMD5Str(String.valueOf(id))+MD5.getMD5Str(Constants.getRandomNumber(6).toString()));
		this.save()
	}
}
