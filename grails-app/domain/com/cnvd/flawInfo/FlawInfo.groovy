package com.cnvd.flawInfo

/**
 * 资产信息导出漏洞信息接口中漏洞信息实体类
 * <AUTHOR>
 *
 */
class FlawInfo {
	
	String number
	String title
	String description
	String formalWay
	String tempWay
	String isFirst
	String referenceLink
	String isZero
	String dateCreated
	String foundTime
	String submitTime
	String storageTime
	String openTime
	String cveStr
	String bidStr
	String cause
	String thread
	String serverity
	String position
	String softStyle
	String reporter
	String isHot
	String isOriginal
	String discovererName
	String isv
	String exploitName
	String exploitTuser
	String exploitConcept
	String exploitPoc
	String exploitSuggestion
	String exploitTime
	String exploitRefer
	String ivp
	String patchId
	String patchName
	String patchInfoTuser
	String patchDescription
	String patchFunction
	String patchUrl
	String metric
	String url  //漏洞CNVD访问链接
	String baseMetric  //基本度量
	String equipmentId  //设备id
	String manufacturer  //所属厂商
	String reflectProduct  //影响产品
	Integer taskId  //任务 task id标识
	
	static mapping = {
		autoTimestamp false
	}
	
    static constraints = {
		number(nullable:true)
		title(nullable:true)
		description(nullable:true)
		formalWay(nullable:true)
		tempWay(nullable:true)
		isFirst(nullable:true)
		referenceLink(nullable:true)
		isZero(nullable:true)
		dateCreated(nullable:true)
		foundTime(nullable:true)
		submitTime(nullable:true)
		storageTime(nullable:true)
		openTime(nullable:true)
		cveStr(nullable:true)
		bidStr(nullable:true)
		cause(nullable:true)
		thread(nullable:true)
		serverity(nullable:true)
		position(nullable:true)
		softStyle(nullable:true)
		reporter(nullable:true)
		isHot(nullable:true)
		isOriginal(nullable:true)
		discovererName(nullable:true)
		isv(nullable:true)
		exploitName(nullable:true)
		exploitTuser(nullable:true)
		exploitConcept(nullable:true)
		exploitPoc(nullable:true)
		exploitSuggestion(nullable:true)
		exploitTime(nullable:true)
		exploitRefer(nullable:true)
		ivp(nullable:true)
		patchId(nullable:true)
		patchName(nullable:true)
		patchInfoTuser(nullable:true)
		patchDescription(nullable:true)
		patchFunction(nullable:true)
		patchUrl(nullable:true)
		metric(nullable:true)
		url(nullable:true)
		baseMetric(nullable:true)
		equipmentId(nullable:true)
		manufacturer(nullable:true)
		reflectProduct(nullable:true)
    }
}
