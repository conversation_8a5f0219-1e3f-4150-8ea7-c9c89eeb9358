package com.cnvd.flawInfo

class BaseMetric {
	MetricInfo accessVector   //攻击途径
	MetricInfo accessComplexity  //攻击复杂度
	MetricInfo authentication  //认证
	MetricInfo confidentialityImpact  //机密性
	MetricInfo integrityImpact  //完整性
	MetricInfo availabilityImpact  //可用性
	Float score
	
	static constraints = {
		score(nullable:true)
	}
	def getCvssStr(){
		def str=""
		str += "AV:"+accessVector.ename+"/"
		str += "AC:"+accessComplexity.ename+"/"
		str += "Au:"+authentication.ename+"/"
		str += "C:"+confidentialityImpact.ename+"/"
		str += "I:"+integrityImpact.ename+"/"
		str += "A:"+availabilityImpact.ename+""
	
		return str
		
//		Example 1: (AV:L/AC:H/Au:N/C:N/I:P/A:C)
//		Example 2: (AV:A/AC:L/Au:M/C:C/I:N/A:P)
//
//		Metric: AV = AccessVector (Related exploit range)
//		Possible Values: L = Local access, A = Adjacent network, N = Network
//
//		Metric: AC = AccessComplexity (Required attack complexity)
//		Possible Values: H = High, M = Medium, L = Low
//
//		Metric: Au = Authentication (Level of authentication needed to exploit)
//		Possible Values: N= None required, S= Requires single instance, M= Requires multiple instances
//
//		Metric: C = ConfImpact (Confidentiality impact)
//		Possible Values: N = None, P = Partial, C = Complete
//
//		Metric: I = IntegImpact (Integrity impact)
//		Possible Values: N = None, P = Partial, C = Complete
//
//		Metric: A = AvailImpact (Availability impact)
//		Possible Values: N = None, P = Partial, C = Complete
		
	}
}
