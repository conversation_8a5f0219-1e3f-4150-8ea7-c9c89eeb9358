package com.cnvd

import com.cnvd.utils.Constants


class IntegralInfo {
	TUser user //积分明细所属人
	Integer integralType //积分类型
	Integer integralValue //积分值
	Date dateCreated
    static constraints = {
		
    }
	def getIntegralTypeStr(){
		if(integralType==Constants.exploitScoreType){
			return "上报验证"
		}else if(integralType==Constants.flawScoreType){
			return "上报漏洞"
		}else if(integralType==Constants.concernedType){
			return "漏洞被关注"
		}else if(integralType==Constants.commentByYouType){
			return "发表评论"
		}else if(integralType == Constants.commentToYouType){
			return "漏洞被评论"
		}
	}
}
