package com.cnvd.productInfo

class Manufacturer {


	String name  //厂商名称
	String description //厂商详细信息
	String email //企业邮箱
	String phoneNum	//企业联系电话
	String address	//企业地址
	String corporation //法人
	String icp	//ICP备案号
	String Keyword	//关键字
	String Unit	//单位行业
	String authorities	//上级主管部门
	String disposal 	//处置方式
	Date dateCreated //创建日期
	Date lastUpdated //更新日期
	String fromWhere	//漏洞信息的来源	(后加字段)

	String contacts //联系人
	
    static constraints = {
		name(blank:false,unique:true)
		description(nullable:true)
		email(nullable:true)
		phoneNum(nullable:true)
		address(nullable:true)
		corporation(nullable:true)
		icp(nullable:true)
		Keyword(nullable:true)
		Unit(nullable:true)
		authorities(nullable:true)
		disposal(nullable:true)
		fromWhere(nullable:true)
		contacts(nullable:true)
	 }
	def beforeDelete = {
		//删除所有厂商下的产品类别\
		executeUpdate 'DELETE FROM FlawProduct WHERE product.id in (select id from ProductInfo where manufacturer=:manufacturer)', [manufacturer: this]
		executeUpdate 'DELETE FROM ProductInfo WHERE manufacturer=:manufacturer', [manufacturer: this]
		executeUpdate 'DELETE FROM ProductEdition WHERE manufacturer=:manufacturer', [manufacturer: this]
		executeUpdate 'DELETE FROM ProductCategory WHERE manufacturer=:manufacturer', [manufacturer: this]
	
		
	}
}
