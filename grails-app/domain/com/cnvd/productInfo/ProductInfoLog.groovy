package com.cnvd.productInfo

import com.cnvd.flawInfo.FlawProduct

class ProductInfoLog {

	Manufacturer manufacturer

/*	ProductCategory productCategory*/

	String edition

	Date dateCreated //创建日期

	Date lastUpdated //更新日期

	String name

	String productCategoryName

	String description

	Long tUserId //前台用户id

	String categoryName //产品类别名称

	String content //审核意见

	Integer status //审核状态

	Integer isSync;//是否更新关键字的漏洞(0：新增或更新未同步 1：已同步 2：删除未同步 )

	Long parentId //需要更新的产品ID


	static hasMany = [flawProducts:FlawProduct]
	

    static constraints = {
		name(blank:false,unique:false)
		description(nullable:true)
		manufacturer(blank:false)
	/*	productCategory(blank:false)*/
		edition(nullable:true)
		content(nullable:true)
		status(nullable:true)
		tUserId(nullable:true)
		categoryName(nullable:true)
		dateCreated(nullable:true)
		lastUpdated(nullable:true)
		productCategoryName(nullable:true)
		parentId(nullable:true)
    }
	def beforeDelete = {
		//删除所有厂商下的产品类别
		executeUpdate 'DELETE FROM FlawProduct WHERE product=:product', [product: this]
	}
}
