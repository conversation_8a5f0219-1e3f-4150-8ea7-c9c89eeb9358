package com.cnvd.productInfo

import java.util.Date;

import com.cnvd.flawInfo.FlawProduct

class ProductInfo {
	Manufacturer manufacturer
	ProductCategory productCategory
	String edition
	Date dateCreated //创建日期
	Date lastUpdated //更新日期
	String name
	String description

	String content //审核意见
	Integer status //审核状态 1 待审核 2 通过 3 驳回

	Integer isSync;//是否同步关联漏洞(0：新增或更新未同步 1：已同步)

	static hasMany = [flawProducts:FlawProduct]
	

    static constraints = {
		name(blank:false,unique:true)
		description(nullable:true)
		manufacturer(blank:false)
		productCategory(blank:false)
		edition(blank:false)

		content(nullable:true)
		status(nullable:true)

    }
	def beforeDelete = {
		//删除所有厂商下的产品类别
		executeUpdate 'DELETE FROM FlawProduct WHERE product=:product', [product: this]
	}

    static def void findByEdition() {}
}
