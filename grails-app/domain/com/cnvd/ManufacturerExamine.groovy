package com.cnvd

class ManufacturerExamine {

	String name  //单位名称
	String description //单位详细信息
	String email //单位邮箱
	String phoneNum	//单位联系电话
	String address	//单位地址
	String corporation //法人
	String contacts //联系人
	String icp	//ICP备案号
	String Keyword	//关键字
	String Unit	//单位行业
	String authorities	//上级主管部门
	String disposal 	//处置方式0:'不处置',1:'邀请注册',2:'前台处置',3:'CSCS',4:'邮件发送',5:'上级单位处置',6:'省分中心处置',7:'CNCERT处置',8:'邮件推送'
	Date dateCreated //创建日期
	Date lastUpdated //更新日期

	Integer status=1  //1未审核 ，2审核通过，3驳回

	Long manufacturerId //单位ID
	String examineOption //审核意见
	TUser user //修改人

    static constraints = {
		name(blank:false)
		description(nullable:true)
		email(nullable:true)
		phoneNum(nullable:true)
		address(nullable:true)
		corporation(nullable:true)
		contacts(nullable:true)
		icp(nullable:true)
		Keyword(nullable:true)
		Unit(nullable:true)
		authorities(nullable:true)
		disposal(nullable:true)
		examineOption(nullable: true)
		user(nullable: true)

	 }
	def beforeDelete = {
		//删除所有单位下的产品类别\
		executeUpdate 'DELETE FROM FlawProduct WHERE product.id in (select id from ProductInfo where manufacturer=:manufacturer)', [manufacturer: this]
		
		executeUpdate 'DELETE FROM ProductInfo WHERE manufacturer=:manufacturer', [manufacturer: this]
		//executeUpdate 'DELETE FROM ProductEdition WHERE manufacturer=:manufacturer', [manufacturer: this]
		executeUpdate 'DELETE FROM ProductCategory WHERE manufacturer=:manufacturer', [manufacturer: this]
	
		
	}


}
