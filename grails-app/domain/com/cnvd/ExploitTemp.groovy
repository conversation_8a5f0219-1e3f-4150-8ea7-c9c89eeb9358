package com.cnvd

import java.util.Date;

import com.cnvd.common.AttachmentExp

/**
 * 由启明星辰提供的利用代码临时存放表
 * <AUTHOR>
 *
 */
class ExploitTemp {

	String number //唯一性编号（进行重复判断）
	String vendor //厂商名称
	String product //产品名称
	String proVersion //影响产品的版本
	String vulnerabillityName //漏洞的名称
	String vulnerabillityDescribe //漏洞的具体描述
	String cve //引用CVE编号
	String bid //引用BID编号
	String cnvd //引用CNVD编号
	String expName //利用代码名称
//	String expFile //利用代码文件 文件链接
	AttachmentExp expFile
	String platform //运行平台
	String type //利用类型
	String dockerType //容器类型
	String expEnvironment //利用代码运行环境说明
	String port //端口号
	String refer //来源
	Integer status = 0  //状态  1.未关联  2.已关联
	Date dateCreated //创建时间
	Date lastUpdated //修改时间
	
	static mapping = {
		// version is set to false, because this isn't available by default for legacy databases
		version false
		vulnerabillityDescribe type:'text'
		type type:'text'
		dockerType type:'text'
		expEnvironment type:'text'
		refer type:'text'
		product type:'text'
		vendor type:'text'
		expName type:'text'
		platform type:'text'
	}
    static constraints = {
		number(nullable:true)
		vendor(nullable:true)
		product(nullable:true)
		proVersion(nullable:true)
		vulnerabillityName(nullable:true)
		vulnerabillityDescribe(nullable:true)
		cve(nullable:true)
		bid(nullable:true)
		cnvd(nullable:true)
		expName(nullable:true)
		expFile(nullable:true)
		platform(nullable:true)
		type(nullable:true)
		dockerType(nullable:true)
		expEnvironment(nullable:true)
		port(nullable:true)
		refer(nullable:true)
    }
}
