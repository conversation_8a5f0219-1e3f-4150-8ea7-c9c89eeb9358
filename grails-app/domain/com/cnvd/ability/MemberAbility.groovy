package com.cnvd.ability

import java.util.Date;
import com.cnvd.User
import com.cnvd.info.Webinfo

/**
 * 支撑单位能力象限
 * <AUTHOR>
 *
 */
class MemberAbility {
	User user					//审核用户
	Webinfo webinfo				//支撑单位栏目信息
	Integer rank				//排名(保留字段)
	Integer enable=0			//0可用  1删除	
	Integer	month				//评审月份
	//公开漏洞
	Integer sumNumber			//漏洞上报总数
	Float sumPoints=0				//公开漏洞积分
	Integer firstNumber			//漏洞首报数
	Integer flawLevel=0			//0未评级  1无  2中 3良 4优
	//事件型漏洞
	Integer eventNumber			//事件型漏洞总数
	Float eventPoints=0			//事件型漏洞积分
	Integer eventLevel=0		//0未评级  1无  2中 3良 4优
	//软硬件漏洞
	Integer noEventFlaw			//软硬件漏洞数量
	Float noEventPoints=0			//软硬件漏洞积分
	Integer noEventLevel=0		//0未评级  1无  2中 3良 4优
	//漏洞技术分析
	Integer analysis			//漏洞技术分析总数
	Integer analysisLevel=0		//0未评级  1无  2中 3良 4优
	//产品能力输出
	Integer support				//支撑产品数量
	Integer supportLevel=0		//0未评级  1无  2中 3良 4优
	
	Date dateCreated 			//创建日期
	Date lastUpdated 			//更新日期

    static constraints = {
		user(nullable:true)
		webinfo(nullable:true)
		rank(nullable:true)
		month(nullable:true)
		sumPoints(nullable:true)
		sumNumber(nullable:true)
		firstNumber(nullable:true)
		eventNumber(nullable:true)
		eventPoints(nullable:true)
		noEventFlaw(nullable:true)
		noEventPoints(nullable:true)
		analysis(nullable:true)
		support(nullable:true)
    }
}
