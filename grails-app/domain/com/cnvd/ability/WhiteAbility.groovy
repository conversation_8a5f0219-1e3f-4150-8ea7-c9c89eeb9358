package com.cnvd.ability

import java.util.Date;

import com.cnvd.TUser

/**
 * 白帽子能力象限信息表
 * <AUTHOR>
 *
 */
class WhiteAbility {
	TUser tuser 				//白帽子用户
	Float webPoints=0			//WEB/CMS漏洞积分
	Integer webSum=0			//WEB/CMS漏洞数量
	Float shebeiPoints=0		//设备漏洞积分
	Integer shebeiSum=0			//设备漏洞数量
	Float eventPoints=0			//事件型漏洞积分
	Integer eventSum=0			//事件型漏洞数量
	Float nixiangPoints=0		//软硬件逆向分析漏洞积分
	Integer nixiangSum=0		//软硬件逆向分析漏洞数量
	Integer rank				//排名
	Integer level=0				//0初始	1优	2良	3中	4无
	Date dateCreated 			//创建日期
	Date lastUpdated 			//更新日期
	
    static constraints = {
		tuser(nullable:true)
		rank(nullable:true)
    }
}
