package com.cnvd
/**
 * 验证环境任务表
 * <AUTHOR>
 *
 */
import java.util.Date;

import com.cnvd.TUser
class EnvironmentTask {

	TUser tuser  //登录用户
	User user //审核人员
	Environment environment //验证环境信息
	Integer status = 0  //状态  0.未审核  -1.审核不通过  1.审核通过，可创建   2.创建失败  3.创建成功，可访问  4.访问成功  5.超时  
	Integer enable = 1  //状态  0.删除 1.有效
	Date dateCreated //录入时间
	Date lastUpdated //修改时间
	EnvironmentReceive environmentReceive
    static constraints = {
		environment(nullable:true)
		tuser(nullable:true)
		user(nullable:true)
		status(nullable:true)
		environmentReceive(nullable:true)
    }
}
