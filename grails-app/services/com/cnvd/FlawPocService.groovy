package com.cnvd

class FlawPocService {

    static transactional = true

    def getNumber() {
        Calendar cal = Calendar.getInstance();//使用日历类
        int year = cal.get(Calendar.YEAR);//得到年
        def hql = "select max(number) from FlawPoc where number like 'POC-" + year + "%'"
        def maxNumberStr = FlawPoc.executeQuery(hql)[0]
        def number = "";
        if (maxNumberStr == null) {
            number = "POC-" + year + "-" + String.format("%05d", 1);
        } else {
            def numberInt = Integer.parseInt(maxNumberStr.substring(maxNumberStr.length() - 5, maxNumberStr.length())) + 1
            number = "POC-" + year + "-" + String.format("%05d", numberInt);
        }
        return number
    }
}

