package com.cnvd

import com.cnvd.*
import com.cnvd.common.*
import com.cnvd.flawInfo.*
import com.cnvd.info.TUserNews
import com.cnvd.patchInfo.*
import com.cnvd.points.Points
import com.cnvd.productInfo.Manufacturer
import com.cnvd.productInfo.ProductCategory
import com.cnvd.productInfo.ProductInfo
import com.cnvd.utils.DateUtil
import net.sf.json.JSONObject

class ToJsonObjectService {

	static JSONObject toJsonObject(Object o) {
		try{
			//判断传过的对应是哪个，然后调用相应方法
			if(o instanceof Flaw){
				return toJson((Flaw)o);
			}else if (o instanceof TUser) {
				return toJson((TUser)o);
			}else if (o instanceof Attachment) {
				return toJson((Attachment)o);
			}else if (o instanceof AttachmentPdf) {
				return toJson((AttachmentPdf)o);
			}else if (o instanceof BaseMetric) {
				return toJson((BaseMetric)o);
			}else if (o instanceof CarbonCopyTask) {
				return toJson((CarbonCopyTask)o);
			}else if (o instanceof DealLog) {
				return toJson((DealLog)o);
			}else if (o instanceof DetailedInfo) {
				return toJson((DetailedInfo)o);
			}else if (o instanceof ExploitAttachment) {
				return toJson((ExploitAttachment)o);
			}else if (o instanceof ExploitAttachmentPdf) {
				return toJson((ExploitAttachmentPdf)o);
			}else if (o instanceof ExploitLog) {
				return toJson((ExploitLog)o);
			}else if (o instanceof FlawProduct) {
				return toJson((FlawProduct)o);
			}else if (o instanceof FlawUrl) {
				return toJson((FlawUrl)o);
			}else if (o instanceof PatchInfoAttachment) {
				return toJson((PatchInfoAttachment)o);
			}else if (o instanceof PatchInfoAttachmentPdf) {
				return toJson((PatchInfoAttachmentPdf)o);
			}else if (o instanceof PatchInfo) {
				return toJson((PatchInfo)o);
			}else if (o instanceof Points) {
				return toJson((Points)o);
			}else if (o instanceof ProcessInfo) {
				return toJson((ProcessInfo)o);
			}else if (o instanceof ProductCategory) {
				return toJson((ProductCategory)o);
			}else if (o instanceof ProductInfo) {
				return toJson((ProductInfo)o);
			}else if (o instanceof ReferenceInfo) {
				return toJson((ReferenceInfo)o);
			}else if (o instanceof Task) {
				return toJson((Task)o);
			}else if (o instanceof TaskLog) {
				return toJson((TaskLog)o);
			}else if (o instanceof User) {
				return toJson((User)o);
			}else if (o instanceof Asset) {
				return toJson((Asset)o);
			}else if (o instanceof FlawUpdated) {
				return toJson((FlawUpdated)o);
			}else if (o instanceof EnvironmentalMetric) {
				return toJson((EnvironmentalMetric)o);
			}else if (o instanceof Manufacturer) {
				return toJson((Manufacturer)o);
			}else if (o instanceof TemporalMetric) {
				return toJson((TemporalMetric)o);
			}else if (o instanceof ExmaineHistory) {
				return toJson((ExmaineHistory)o);
			}else if (o instanceof FlawTypesParamMiddle) {
				return toJson((FlawTypesParamMiddle)o);
			}else if (o instanceof Exploit) {
				return toJson((Exploit)o);
			}else if (o instanceof Certificate) {
				return toJson((Certificate)o);
			}else if (o instanceof FlawProcess) {
				return toJson((FlawProcess)o);
			}else if (o instanceof IntegralInfo) {
				return toJson((IntegralInfo)o);
			}else if (o instanceof TUserNews) {
				return toJson((TUserNews)o);
			}else if (o instanceof BatchFlaw) {
				return toJson((BatchFlaw)o);
			}else if (o instanceof DealDisposalLog) {
				return toJson((DealDisposalLog)o);
			}else if (o instanceof DisposalInfo) {
				return toJson((DisposalInfo)o);
			}else if (o instanceof DisposalInfoAttachment) {
				return toJson((DisposalInfoAttachment)o);
			}else if (o instanceof DisposalInfoAttachmentPdf) {
				return toJson((DisposalInfoAttachmentPdf)o);
			}
		}catch(Exception e){
			e.printStackTrace();
		}
		return null;
    }
/**
 * Flaw对象转换成JSONObject的方法
 * @param o
 * @return
 */
	private static JSONObject toJson(Flaw o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "flaw");
		jsonObject.put("id", o.getId());

		jsonObject.put("version", o.getVersion()==null?"":o.getVersion());
		jsonObject.put("attachment_id",o.getAttachment()==null?"": o.getAttachment().getId());
		jsonObject.put("basemetric_id",  o.getBasemetric()==null?"":o.getBasemetric().getId());
		jsonObject.put("cause_id", o.getCauseId()==null?"":o.getCauseId());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("enable", o.getEnable()==null?"":o.getEnable());
		jsonObject.put("environmental_metric_id", o.getEnvironmentalMetric()==null?"":o.getEnvironmentalMetric().getId());
		jsonObject.put("found_time", DateUtil.get4yMdHmstan(o.getFoundTime()));
		jsonObject.put("is_first", o.getIsFirst()==null?"":o.getIsFirst());
		//jsonObject.put("is_private", o.getisp);
		jsonObject.put("is_zero", o.getIsZero()==null?"": o.getIsZero());
		jsonObject.put("isg", o.getIsg()==null?"": o.getIsg());
		jsonObject.put("isu", o.getIsu()==null?"":o.getIsu());
		jsonObject.put("isv", o.getIsv()==null?"":o.getIsv());
		jsonObject.put("ivp", o.getIvp()==null?"":o.getIvp());
		jsonObject.put("isp", o.getIsp()==null?"":o.getIsp());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("manufacturer_id", o.getManufacturer()==null?"":o.getManufacturer().getId());
		jsonObject.put("number", o.getNumber()==null?"":o.getNumber());
		//jsonObject.put("other_info", o.g);

		jsonObject.put("parent_flaw_id", o.getParentFlaw()==null?"":o.getParentFlaw().getId());
		jsonObject.put("position_id", o.getPositionId()==null?"":o.getPositionId());
		jsonObject.put("rank", o.getRank()==null?"":o.getRank());
		/*jsonObject.put("reference_info_id", o.;*/
		jsonObject.put("serverity_id", o.getServerityId()==null?"":o.getServerityId());
		jsonObject.put("soft_style_id", o.getSoftStyleId()==null?"":o.getSoftStyleId());
		jsonObject.put("status", o.getStatus()==null?"":o.getStatus());
		jsonObject.put("storage_time",DateUtil.get4yMdHmstan(o.getStorageTime()));
		jsonObject.put("submit_time", DateUtil.get4yMdHmstan(o.getSubmitTime()));

		jsonObject.put("temporal_metric_id",o.getTemporalMetric()==null?"":o.getTemporalMetric().getId());
		jsonObject.put("thread_id", o.getThreadId()==null?"":o.getThreadId());
		jsonObject.put("title", o.getTitle()==null?"":o.getTitle());
		jsonObject.put("user_id", o.getUser()==null?"":o.getUser().getId());
		jsonObject.put("view_count", o.getViewCount()==null?"":o.getViewCount());
		jsonObject.put("open_time", DateUtil.get4yMdHmstan(o.getOpenTime()));
		jsonObject.put("is_hot", o.getIsHot()==null?"":o.getIsHot());


		jsonObject.put("detailed_info_id", o.getDetailedInfo()==null?"":o.getDetailedInfo().getId());
		/*jsonObject.put("old_id", o.g);*/
		jsonObject.put("is_original", o.getIsOriginal()==null?"":o.getIsOriginal());

		jsonObject.put("discoverer_name", o.getDiscovererName()==null?"":o.getDiscovererName());
		jsonObject.put("reference_link", o.getReferenceLink()==null?"":o.getReferenceLink());
		jsonObject.put("is_open", o.getIsOpen()==null?"":o.getIsOpen());

		jsonObject.put("is_event", o.getIsEvent()==null?"": o.getIsEvent());
		jsonObject.put("click_num", o.getClickNum()==null?"":o.getClickNum());
		jsonObject.put("comment_count", o.getCommentCount()==null?"":o.getCommentCount());
		jsonObject.put("concern_count", o.getConcernCount()==null?"":o.getConcernCount());
		jsonObject.put("down_code", o.getDownCode()==null?"":o.getDownCode());
		jsonObject.put("batch_flaw_id", o.getBatchFlaw()==null?"":o.getBatchFlaw().getId());
		jsonObject.put("is_att_show", o.getIsAttShow()==null?"":o.getIsAttShow());
		jsonObject.put("old_number", o.getOldNumber()==null?"":o.getOldNumber());
		jsonObject.put("temp_number", o.getTempNumber()==null?"":o.getTempNumber());

		jsonObject.put("is_additional", o.getIsAdditional()==null?"":o.getIsAdditional());
		/*	jsonObject.put("from_where", o.getf);*/
		jsonObject.put("points_id", o.getPoints()==null?"":o.getPoints().getId());
		jsonObject.put("url", o.getUrl()==null?"":o.getUrl());
		jsonObject.put("titlel", o.getTitle()==null?"":o.getTitle());
		jsonObject.put("remark", o.getRemark()==null?"":o.getRemark());

		jsonObject.put("province", o.getProvince()==null?"":o.getProvince());
		jsonObject.put("city", o.getCity()==null?"":o.getCity());
		jsonObject.put("flowip", o.getFlowIP()==null?"": o.getFlowIP());
		jsonObject.put("flow_type", o.getFlowType()==null?"":o.getFlowType());
		jsonObject.put("unit_name", o.getUnitName()==null?"":o.getUnitName());
		jsonObject.put("ministries_name", o.getMinistriesName()==null?"":o.getMinistriesName());
		jsonObject.put("is_certificate", o.getIsCertificate()==null?"":o.getIsCertificate());
		jsonObject.put("flaw_types_id",  o.getFlawTypes()==null?"":o.getFlawTypes().getId());
		jsonObject.put("is_firstis_first", o.getIsFirst()==null?"":o.getIsFirst());
		/*jsonObject.put("currency_type", o.);*/
		jsonObject.put("czff", o.getCzff()==null?"":o.getCzff());
		/*jsonObject.put("flaw_hotspot", o.getflaw` `);
		jsonObject.put("flaw_industry", o.getfla` `);*/
		jsonObject.put("push", o.getPush()==null?"":o.getPush());
		jsonObject.put("is_repeat", o.getIsRepeat());

		return jsonObject;
	}



	/**
	 * Attachment 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(Attachment o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "attachment");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("file_name", o.getFileName());
		jsonObject.put("file_size", o.getFileSize());
		jsonObject.put("file_type", o.getFileType());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("path", o.getPath());
		jsonObject.put("real_name", o.getRealName());
		jsonObject.put("name", o.getRealName());
		//jsonObject.put("original_name", o.g);
		jsonObject.put("size", o.getFileSize());
		//jsonObject.put("suffix", o.g);
		return jsonObject;
	}



	/**
	 * EnvironmentalMetric 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(EnvironmentalMetric o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "environmental_metric");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("availability_require_id",o.getAvailabilityRequire()==null?"":o.getAvailabilityRequire().getId());
		jsonObject.put("collateral_damage_potential_id", o.getCollateralDamagePotential()==null?"":o.getCollateralDamagePotential().getId());
		jsonObject.put("confidentiality_require_id", o.getConfidentialityRequire()==null?"":o.getConfidentialityRequire().getId());
		jsonObject.put("integrity_require_id", o.getConfidentialityRequire()==null?"":o.getConfidentialityRequire().getId());
		jsonObject.put("score", o.getScore()==null?"":o.getScore());
		jsonObject.put("target_distribution_id", o.getTargetDistribution()==null?"":o.getTargetDistribution().getId());

		return jsonObject;
	}


	/**
	 * Manufacturer 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(Manufacturer o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "manufacturer");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion()==null?"":o.getVersion());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("description", o.getDescription()==null?"":o.getDescription());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("name", o.getName()==null?"":o.getName());
		jsonObject.put("address", o.getAddress()==null?"":o.getAddress());
		jsonObject.put("corporation", o.getCorporation()==null?"":o.getCorporation());
		jsonObject.put("email", o.getEmail()==null?"":o.getEmail());
		jsonObject.put("icp", o.getIcp()==null?"":o.getIcp());
		jsonObject.put("phone_num", o.getPhoneNum()==null?"":o.getPhoneNum());
		jsonObject.put("authorities", o.getAuthorities()==null?"":o.getAuthorities());
		jsonObject.put("disposal", o.getDisposal()==null?"":o.getDisposal());
		jsonObject.put("keyword", o.getKeyword()==null?"":o.getKeyword());
		jsonObject.put("unit", o.getUnit()==null?"":o.getUnit());
		jsonObject.put("contacts", o.getContacts()==null?"":o.getContacts());
		return jsonObject;
	}


	/**
	 * TemporalMetric 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(TemporalMetric o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "temporal_metric");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("exploitability_id", o.getExploitability()==null?"":o.getExploitability().getId());
		jsonObject.put("remediation_level_id", o.getRemediationLevel()==null?"":o.getRemediationLevel().getId());
		jsonObject.put("report_confidence_id", o.getReportConfidence()==null?"":o.getReportConfidence().getId());
		jsonObject.put("score", o.getScore());

		return jsonObject;
	}



	/**
	 * TemporalMetric 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(ExmaineHistory o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "exmaine_history");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("action", o.getAction());
		jsonObject.put("content", o.getContent());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("flaw_id", o.getFlaw()==null?"":o.getFlaw().getId());
		jsonObject.put("status", o.getStatus());

		return jsonObject;
	}

	/**
	 * TUser 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(TUser o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "tuser");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("active_code", o.getActiveCode());
		jsonObject.put("address", o.getAddress());

		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("description", o.getDescription());
		jsonObject.put("email", o.getEmail());
		jsonObject.put("honor_value", o.getHonorValue());
		jsonObject.put("gender", o.getGender());
		jsonObject.put("integ_value", o.getIntegValue());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("manufacturer_id", o.getManufacturer()==null?"":o.getManufacturer().getId());
		jsonObject.put("nick_name", o.getNickName());
		jsonObject.put("password", o.getPassword());
		jsonObject.put("phone_number", o.getPhoneNumber());
		jsonObject.put("status", o.getStatus());
		jsonObject.put("url", o.getUrl());
		jsonObject.put("user_name", o.getUserName());
		jsonObject.put("user_type", o.getUserType());
		jsonObject.put("work", o.getWork());
		jsonObject.put("workplace", o.getWorkplace());
		jsonObject.put("enable", o.getEnable());
		jsonObject.put("regist_time", DateUtil.get4yMdHmstan(o.getRegistTime()));
		//jsonObject.put("creator", o.);
		//jsonObject.put("ip", o.getip);
		//jsonObject.put("provider", o.getProperty());
		//jsonObject.put("date_updated", o.getdate);
		//jsonObject.put("type", o.getType());
		jsonObject.put("workspace", o.getWorkplace());
		jsonObject.put("is_timeout_editor", o.getIsTimeoutEditor());
		//jsonObject.put("use_points", o.getusep);
		/*	jsonObject.put("rewards_points", o.getre);
            jsonObject.put("loophole_count", o.getl);*/
		//jsonObject.put("integral_count", o.geti);
		jsonObject.put("create_date", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("enabled", o.getEnable());
		/*jsonObject.put("update_date", o.getup);*/
		return jsonObject;
	}



	/**
	 * FlawTypesParamMiddle 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(FlawTypesParamMiddle o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "flaw_types_param_middle");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("flaw_id", o.getFlaw()==null?"":o.getFlaw().getId());
		jsonObject.put("flaw_types_id", o.getFlawTypes()==null?"":o.getFlawTypes().getId());
		jsonObject.put("flaw_types_param_id", o.getFlawTypesParam()==null?"":o.getFlawTypesParam().getId());
		jsonObject.put("param_values", o.getParam_values());


		return jsonObject;
	}


	/**
	 * FlawUpdated 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(FlawUpdated o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "flaw_updated");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("flaw_id", o.getFlaw()==null?"":o.getFlaw().getId());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("type", o.getType());

		return jsonObject;
	}


	/**
	 * Exploit 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(Exploit o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "exploit");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("attachment_id", o.getAttachment()==null?"":o.getAttachment().getId());
		jsonObject.put("concept", o.getConcept()==null?"":o.getConcept());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("exploit_name", o.getExploitName()==null?"":o.getExploitName());
		jsonObject.put("exploit_time",  DateUtil.get4yMdHmstan(o.getExploitTime()));
		jsonObject.put("exploit_type", o.getExploitType()==null?"":o.getExploitType());
		jsonObject.put("flaw_id", o.getFlaw()==null?"":o.getFlaw().getId());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("poc", o.getPoc()==null?"":o.getPoc());
		jsonObject.put("status", o.getStatus()==null?"":o.getStatus());
		jsonObject.put("suggestion", o.getSuggestion()==null?"":o.getSuggestion());
		jsonObject.put("tuser_id", o.getTuser()==null?"":o.getTuser().getId());
		//jsonObject.put("old_id", o.get1);
		jsonObject.put("enable", o.getEnable());
		jsonObject.put("reference_link", o.getReferenceLink()==null?"":o.getReferenceLink());
		jsonObject.put("down_code", o.getDownCode()==null?"":o.getDownCode());

		return jsonObject;
	}



	/**
	 * Certificate 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(Certificate o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "certificate");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("award_company", o.getAwardCompany());
		jsonObject.put("award_time", o.getAwardTime());
		jsonObject.put("c_id", o.getC_id());
		jsonObject.put("cer_type", o.getCerType());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("flaw_id", o.getFlaw()==null?"":o.getFlaw().getId());
		jsonObject.put("pdf_attachment_id", o.getPdfAttachment()==null?"":o.getPdfAttachment().getId());
		jsonObject.put("signature_attachment_id", o.getSignatureAttachment()==null?"":o.getSignatureAttachment().getId());
		jsonObject.put("tuser_id", o.getTuser()==null?"":o.getTuser().getId());
		//onObject.put("old_id", o.get);
		jsonObject.put("is_old", o.getIsOld());
		jsonObject.put("down_code", o.getDownCode());

		//jsonObject.put("content", o.getco);



		return jsonObject;
	}







	/**
	 * FlawPoint 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(FlawProcess o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "flaw_process");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("info", o.getInfo());
		jsonObject.put("process_info_id", o.getProcessInfo()==null?"":o.getProcessInfo().getId());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("flaw_id", o.getFlaw()==null?"":o.getFlaw().getId());

		return jsonObject;
	}


	/**
	 * PatchInfo 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(PatchInfo o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "patch_info");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("attachment_id", o.getAttachment()==null?"":o.getAttachment().getId());
		jsonObject.put("click_number", o.getClickNumber());
		jsonObject.put("create_source", o.getCreateSource()==null?"":o.getCreateSource());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("deal_description", o.getDealDescription()==null?"":o.getDealDescription());
		jsonObject.put("flaw_id", o.getFlaw()==null?"":o.getFlaw().getId());
		jsonObject.put("function", o.getFunction()==null?"":o.getFunction());
		jsonObject.put("internal_patch_file", o.getInternalPatchFile()==null?"":o.getInternalPatchFile());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("patch_description", o.getPatchDescription()==null?"":o.getPatchDescription());
		jsonObject.put("patch_id", o.getPatchId()==null?"":o.getPatchId());
		jsonObject.put("patch_name", o.getPatchName()==null?"":o.getPatchName());
		jsonObject.put("patch_url", o.getPatchUrl()==null?"":o.getPatchUrl());
		jsonObject.put("status", o.getStatus()==null?"":o.getStatus());
		jsonObject.put("tuser_id", o.getTuser()==null?"":o.getTuser().getId());
		jsonObject.put("user_id", o.getUser()==null?"":o.getUser().getId());
		jsonObject.put("valid", o.getValid()==null?"":o.getValid());
		//jsonObject.put("old_id", o.get);
		jsonObject.put("enable", o.getEnable());
		jsonObject.put("down_code", o.getDownCode()==null?"":o.getDownCode());
		jsonObject.put("tel", o.getTel()==null?"":o.getTel());
		jsonObject.put("remark", o.getRemark()==null?"":o.getRemark());
		//jsonObject.put("attachment_pdf_id", o.ge);
		jsonObject.put("is_disposal_task_patch", o.getIsDisposalTaskPatch()==null?"":o.getIsDisposalTaskPatch());
		//jsonObject.put("block_chain_id", o.ge`t);

		return jsonObject;
	}

	/**
	 * DisposalInfo 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(DisposalInfo o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "disposal_info");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("attachment_id", o.getAttachment()==null?"":o.getAttachment().getId());
		jsonObject.put("click_number", o.getClickNumber());
		jsonObject.put("create_source", o.getCreateSource());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("deal_description", o.getDealDescription()==null?"":o.getDealDescription());
		jsonObject.put("flaw_id", o.getFlaw()==null?"":o.getFlaw().getId());
		jsonObject.put("function", o.getFunction()==null?"":o.getFunction());
		jsonObject.put("internal_patch_file", o.getInternalPatchFile()==null?"":o.getInternalPatchFile());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("patch_description", o.getPatchDescription()==null?"":o.getPatchDescription());
		jsonObject.put("patch_id", o.getPatchId()==null?"":o.getPatchId());
		jsonObject.put("patch_name", o.getPatchName()==null?"":o.getPatchName());
		jsonObject.put("patch_url", o.getPatchUrl()==null?"":o.getPatchUrl());
		jsonObject.put("status", o.getStatus()==null?"":o.getStatus());
		jsonObject.put("tuser_id", o.getTuser()==null?"":o.getTuser().getId());
		jsonObject.put("user_id", o.getUser()==null?"":o.getUser().getId());
		jsonObject.put("valid", o.getValid()==null?"": o.getValid());
		//jsonObject.put("old_id", o.get);
		jsonObject.put("enable", o.getEnable()==null?"":o.getEnable());
		jsonObject.put("down_code", o.getDownCode()==null?"":o.getDownCode());
		jsonObject.put("tel", o.getTel()==null?"":o.getTel());
		jsonObject.put("remark", o.getRemark()==null?"":o.getRemark());
		//jsonObject.put("attachment_pdf_id", o.ge);
		jsonObject.put("is_disposal_task_patch", o.getIsDisposalTaskPatch()==null?"":o.getIsDisposalTaskPatch());
		//jsonObject.put("block_chain_id", o.ge`t);

		return jsonObject;
	}



	/**
	 * AttachmentPdf对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(AttachmentPdf o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "attachment_pdf");
		jsonObject.put("attachment_id", o.getAttachmentId());
		jsonObject.put("date_created",  DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("file_name", o.getFileName());
		jsonObject.put("file_size", o.getFileSize());
		jsonObject.put("file_type", o.getFileType());
		jsonObject.put("flawid", o.getFlawid());
		jsonObject.put("id", o.getId());
		jsonObject.put("last_updated",  DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("path", o.getPath());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("real_name", o.getRealName());
		return jsonObject;
	}


	/**
	 * BaseMetric对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(BaseMetric o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "base_metric");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("access_complexity_id", o.getAccessComplexity().getId());
		jsonObject.put("access_vector_id", o.getAccessVector().getId());
		jsonObject.put("authentication_id", o.getAuthentication().getId());
		jsonObject.put("availability_impact_id", o.getAvailabilityImpact().getId());
		jsonObject.put("confidentiality_impact_id", o.getConfidentialityImpact().getId());
		jsonObject.put("integrity_impact_id", o.getIntegrityImpact().getId());
		jsonObject.put("score", o.getScore());
		return jsonObject;
	}

	/**
	 * CarbonCopyTask对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(CarbonCopyTask o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "carbon_copy_task");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("task_id", o.getTask().getId());
		jsonObject.put("tuser_id", o.getTuser().getId());

		return jsonObject;
	}


	/**
	 * DealLog对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(DealLog o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "deal_log");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("content", o.getContent());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("description", o.getDescription());
		jsonObject.put("patch_id", o.getPatchId());
		jsonObject.put("status", o.getStatus());
		jsonObject.put("tuser_id", o.getTuser()==null?"":o.getTuser().getId());
		jsonObject.put("user_id",  o.getUser()==null?"":o.getUser().getId());
		return jsonObject;
	}

	/**
	 * DealDisposalLog对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(DealDisposalLog o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "deal_disposal_log");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("content", o.getContent());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("description", o.getDescription());
		jsonObject.put("patch_id", o.getPatchId());
		jsonObject.put("status", o.getStatus());
		jsonObject.put("tuser_id", o.getTuser()==null?"":o.getTuser().getId());
		jsonObject.put("user_id",  o.getUser()==null?"":o.getUser().getId());
		return jsonObject;
	}


	/**
	 * DetailedInfo对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(DetailedInfo o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "detailed_info");
		jsonObject.put("id", o.getId()==null?"":o.getId());
		jsonObject.put("version", o.getVersion()==null?"":o.getVersion());
		jsonObject.put("description", o.getDescription()==null?"":o.getDescription());
		jsonObject.put("formal_way", o.getFormalWay()==null?"":o.getFormalWay());
		jsonObject.put("temp_way", o.getTempWay()==null?"":o.getTempWay());
		return jsonObject;
	}

	/**
	 * ExploitAttachment对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(ExploitAttachment o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "exploit_attachment");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("attachment_id", o.getAttachment().getId());
		jsonObject.put("date_created",  DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("exploit_id", o.getExploit().getId());
		jsonObject.put("last_updated",  DateUtil.get4yMdHmstan(o.getLastUpdated()));

		return jsonObject;
	}

	/**
	 * ExploitAttachmentPdf对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(ExploitAttachmentPdf o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "exploit_attachment_pdf");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("attachment_pdf_id", o.getAttachmentPdf().getId());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("exploit_id", o.getExploit().getId());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		return jsonObject;
	}

	/**
	 * ExploitLog对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(ExploitLog o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "exploit_log");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("flaw_id", o.getFlawId());
		jsonObject.put("result", o.getResult());
		jsonObject.put("status", o.getStatus());
		jsonObject.put("tuser_id", o.getTuser()==null?"":o.getTuser().getId());
		jsonObject.put("type", o.getType());

		return jsonObject;
	}







	/**
	 * FlawProduct对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(FlawProduct o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "flaw_product");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("flaw_id", o.getFlaw()==null?"":o.getFlaw().getId());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("product_id", o.getProduct()==null?"":o.getProduct().getId());

		return jsonObject;
	}







	/**
	 * FlawUrl 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(FlawUrl o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "flaw_url");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("flaw_id", o.getFlaw()==null?"":o.getFlaw().getId());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("url", o.getUrl());
		return jsonObject;
	}




	/**
	 * PatchInfoAttachment 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(PatchInfoAttachment o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "patch_info_attachment");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("attachment_id", o.getAttachment()==null?"":o.getAttachment().getId());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("patch_info_id", o.getPatchInfo()==null?"":o.getPatchInfo().getId());

		return jsonObject;
	}



	/**
	 * PatchInfoAttachmentPdf 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(PatchInfoAttachmentPdf o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "patch_info_attachment_pdf");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("attachment_pdf_id", o.getAttachmentPdf()==null?"":o.getAttachmentPdf().getId());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("patch_info_id", o.getPatchInfo()==null?"":o.getPatchInfo().getId());


		return jsonObject;
	}


	/**
	 * DisposalInfoAttachment 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(DisposalInfoAttachment o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "disposal_info_attachment");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("attachment_id", o.getAttachment()==null?"":o.getAttachment().getId());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("disposal_info_id", o.getDisposalInfo()==null?"":o.getDisposalInfo().getId());

		return jsonObject;
	}



	/**
	 * DisposalInfoAttachmentPdf 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(DisposalInfoAttachmentPdf o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "disposal_info_attachment_pdf");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("attachment_pdf_id", o.getAttachmentPdf()==null?"":o.getAttachmentPdf().getId());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("disposal_info_id", o.getDisposalInfo()==null?"":o.getDisposalInfo().getId());


		return jsonObject;
	}


	/**
	 * Points 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(Points o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "points");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("added", o.getAdded());
		jsonObject.put("coefficient", o.getCoefficient());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("flaw_id", o.getFlawId());
		jsonObject.put("total", o.getTotal());
		return jsonObject;
	}

	/**
	 * ProcessInfo 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(ProcessInfo o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "process_info");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("process_name", o.getProcessName());
		jsonObject.put("status", o.getStatus());


		return jsonObject;
	}

	/**
	 * ProductCategory 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(ProductCategory o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "product_category");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion()==null?"":o.getVersion());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("description", o.getDescription()==null?"":o.getDescription());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("manufacturer_id", o.getManufacturer()==null?"":o.getManufacturer().getId());
		jsonObject.put("name", o.getName());

		return jsonObject;
	}


	/**
	 * ProductInfo 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(ProductInfo o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "product_info");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion()==null?"":o.getVersion());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("description", o.getDescription()==null?"":o.getDescription());
		jsonObject.put("edition", o.getEdition()==null?"":o.getEdition());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("manufacturer_id", o.getManufacturer()==null?"":o.getManufacturer().getId());
		jsonObject.put("name", o.getName());
		jsonObject.put("product_category_id", o.getProductCategory()==null?"":o.getProductCategory().getId());
		return jsonObject;
	}






	/**
	 * ReferenceInfo 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(ReferenceInfo o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "reference_info");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("flaw_id", o.getFlaw()==null?"":o.getFlaw().getId());
		jsonObject.put("link_name", o.getLinkName());
		jsonObject.put("link_url", o.getLinkUrl());
		jsonObject.put("reference_number", o.getReferenceNumber());
		jsonObject.put("reference_type_id", o.getReferenceType()==null?"":o.getReferenceType().getId());
		jsonObject.put("link_source", o.getLinkSource());
		return jsonObject;
	}


	/**
	 * Task 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(Task o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "task");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("complete_date", DateUtil.get4yMdHmstan(o.getCompleteDate()));
		jsonObject.put("complete_info", o.getCompleteInfo());
		jsonObject.put("creater_id", o.getCreater()==null?"":o.getCreater().getId());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("enable", o.getEnable());
		jsonObject.put("flaw_id", o.getFlaw()==null?"":o.getFlaw().getId());
		jsonObject.put("info", o.getInfo());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("real_complete_date", DateUtil.get4yMdHmstan(o.getRealCompleteDate()));
		jsonObject.put("status", o.getStatus());
		jsonObject.put("target_user_id", o.getTargetUser()==null?"":o.getTargetUser().getId());
		jsonObject.put("title", o.getTitle());
		jsonObject.put("type", o.getType());
		jsonObject.put("audit_date", DateUtil.get4yMdHmstan(o.getAuditDate()));
		jsonObject.put("submit_date", DateUtil.get4yMdHmstan(o.getSubmitDate()));
		jsonObject.put("attachment_id", o.getAttachment()==null?"":o.getAttachment().getId());
		jsonObject.put("score1", o.getScore1());
		jsonObject.put("score2", o.getScore2());
		jsonObject.put("score3", o.getScore3());
		jsonObject.put("targettuser_id", o.getTargetTUser()==null?"":o.getTargetTUser().getId());
		jsonObject.put("target_type", o.getTargetType());
		jsonObject.put("exploit_id", o.getExploit()==null?"":o.getExploit().getId());
		jsonObject.put("patch_info_id", o.getPatchInfo()==null?"":o.getPatchInfo().getId());
		jsonObject.put("disposal_info_id", o.getDisposalInfo()==null?"":o.getDisposalInfo().getId());
		jsonObject.put("down_code", o.getDownCode());
		jsonObject.put("tag", o.getTag());
		jsonObject.put("is_complete_date_syn", o.getIsCompleteDateSyn());
		jsonObject.put("is_expoit_patch", o.getIsExpoitPatch());

		return jsonObject;
	}


	/**
	 * TaskLog 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(TaskLog o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "task_log");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("content", o.getContent());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("operating", o.getOperating());
		jsonObject.put("task_id", o.getTask()==null?"":o.getTask().getId());

		return jsonObject;
	}


	/**
	 * User 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(User o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "user");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("enable", o.getEnable());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("password", o.getPassword());
		jsonObject.put("user_name", o.getUserName());
		jsonObject.put("sort_priority", o.getSortPriority());
		return jsonObject;
	}


	/**
	 * Asset 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(Asset o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "asset");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("description",o.getDescription());
		jsonObject.put("edition",o.getEdition());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("manufacturer_name",o.getManufacturerName());
		jsonObject.put("name",o.getName());
		jsonObject.put("product_id",o.getProduct()==null?"":o.getProduct().getId());
		jsonObject.put("product_category_name",o.getProductCategoryName());
		jsonObject.put("flag",o.getFlag());
		return jsonObject;
	}
	/**
	 * IntegralInfo 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(IntegralInfo o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "integral_info");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("integral_type", o.getIntegralType());
		jsonObject.put("integral_value", o.getIntegralValue());
		jsonObject.put("user_id", o.getUser()==null?"":o.getUser().getId());

		return jsonObject;
	}


	/**
	 * IntegralInfo 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(TUserNews o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "tuser_news");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("read_time", DateUtil.get4yMdHmstan(o.getReadTime()));
		jsonObject.put("status", o.getStatus());
		jsonObject.put("news_id", o.getNews()==null?"":o.getNews().getId());
		jsonObject.put("tuser_id", o.getTuser()==null?"":o.getTuser().getId());

		return jsonObject;
	}



	/**
	 * BatchFlaw 对象转换成JSONObject的方法
	 * @param o
	 * @return
	 */
	private static JSONObject toJson(BatchFlaw o){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("table", "batch_flaw");
		jsonObject.put("id", o.getId());
		jsonObject.put("version", o.getVersion());
		jsonObject.put("attachment_id", o.getAttachment()==null?"":o.getAttachment().getId());
		jsonObject.put("date_created", DateUtil.get4yMdHmstan(o.getDateCreated()));
		jsonObject.put("down_code", o.getDownCode());
		jsonObject.put("last_updated", DateUtil.get4yMdHmstan(o.getLastUpdated()));
		jsonObject.put("status", o.getStatus());
		jsonObject.put("tuser_id", o.getTuser()==null?"":o.getTuser().getId());


		return jsonObject;
	}

}
