package com.cnvd

import com.cnvd.common.Attachment
import com.cnvd.common.AttachmentExp
import com.cnvd.common.AttachmentPdf
import com.cnvd.patchInfo.PatchInfo
import com.cnvd.scan.AttExpHtml
import com.cnvd.scan.AttExpPdf
import com.cnvd.utils.FileTypeUtil

import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

class AttachmentService {

	static transactional = true
	def dataSource
	/**
	 * 下载PDF附件
	 * @param att
	 * @param request
	 * @param response
	 * @return
	 */
	def downloadAttPdf(AttachmentPdf att, HttpServletRequest request, HttpServletResponse response){
		request.setCharacterEncoding("UTF-8");
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;

		String downLoadPath = att.path;
		long fileLength = new File(downLoadPath).length();
		def browserType = request.getHeader("User-Agent")
		response.setContentType("application/octet-stream");
		byte[] bytes = browserType.contains("MSIE") ? att.fileName.getBytes() : att.fileName.getBytes("utf-8");
		response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
		response.setHeader("Content-Length", String.valueOf(fileLength));

		bis = new BufferedInputStream(new FileInputStream(downLoadPath));
		bos = new BufferedOutputStream(response.getOutputStream());
		byte[] buff = new byte[2048];
		int bytesRead;
		while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
			bos.write(buff, 0, bytesRead);
		}
		bis.close();
		bos.close();
	}

    def uploadFile(def file,def filePath,realName){
		Attachment attachment=null

		try{
			File pathFile = new File(filePath)
			if(!pathFile.exists()){
				pathFile.mkdirs()
			}
			String fileName= file.getOriginalFilename()
			fileName=fileName.substring(fileName.lastIndexOf("."));
			String fileUrlStr=filePath+realName+fileName
			attachment=new Attachment()
			attachment.realName=realName+fileName
			attachment.fileName=file.getOriginalFilename()
			attachment.fileType=file.getContentType()
			attachment.fileSize=file.getSize()
			attachment.path=fileUrlStr
			attachment.save()

			file.transferTo(new File(fileUrlStr));
		}catch(Exception e){
			e.printStackTrace()
			return null
		}
		return attachment
	}
	
	/**
	 * 启明验证环境专用附件方法
	 * @param file
	 * @param filePath
	 * @param realName
	 * @return
	 */
	def uploadFileExp(def file,def filePath,def realName){
		AttachmentExp attachment=null

		try{
			File pathFile = new File(filePath)
			if(!pathFile.exists()){
				pathFile.mkdirs()
			}
			String fileName= file.getOriginalFilename()
			String fileUrlStr=filePath+fileName
			attachment=new AttachmentExp()
			attachment.realName=fileName
			attachment.fileName=file.getOriginalFilename()
			attachment.fileType=file.getContentType()
			attachment.fileSize=file.getSize()
			attachment.path=fileUrlStr
			attachment.save()
			file.transferTo(new File(fileUrlStr));
		}catch(Exception e){
			e.printStackTrace()
			return null
		}
		return attachment
	}
	/**
	 * 启明验证平台 任务下发反馈html附件
	 * @param file
	 * @param filePath
	 * @param realName
	 * @return
	 */
	def uploadHtmlFile(def file,def filePath,def realName){
		AttExpHtml attachment=null

		try{
			File pathFile = new File(filePath)
			if(!pathFile.exists()){
				pathFile.mkdirs()
			}
			String fileName= file.getOriginalFilename()
			String fileUrlStr=filePath+fileName
			attachment=new AttExpHtml()
			attachment.realName=fileName
			attachment.fileName=file.getOriginalFilename()
			attachment.fileType=file.getContentType()
			attachment.fileSize=file.getSize()
			attachment.path=fileUrlStr
			attachment.save()
			file.transferTo(new File(fileUrlStr));
		}catch(Exception e){
			e.printStackTrace()
			return null
		}
		return attachment
	}
	
	/**
	 * 启明验证平台 任务下发反馈pdf附件
	 * @param file
	 * @param filePath
	 * @param realName
	 * @return
	 */
	def uploadPdfFile(def file,def filePath,def realName){
		AttExpPdf attachment=null

		try{
			File pathFile = new File(filePath)
			if(!pathFile.exists()){
				pathFile.mkdirs()
			}
			String fileName= file.getOriginalFilename()
			String fileUrlStr=filePath+fileName
			attachment=new AttExpPdf()
			attachment.realName=fileName
			attachment.fileName=file.getOriginalFilename()
			attachment.fileType=file.getContentType()
			attachment.fileSize=file.getSize()
			attachment.path=fileUrlStr
			attachment.save()
			file.transferTo(new File(fileUrlStr));
		}catch(Exception e){
			e.printStackTrace()
			return null
		}
		return attachment
	}

	/**
	 * 下载启明pdf附件
	 * @param att
	 * @param request
	 * @param response
	 * @return
	 */
	def downloadPdf(AttExpPdf att,HttpServletRequest request,HttpServletResponse response){
		request.setCharacterEncoding("UTF-8");
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;

		String downLoadPath = att.path;
		long fileLength = new File(downLoadPath).length();
		def browserType = request.getHeader("User-Agent")
		response.setContentType("application/octet-stream");
		byte[] bytes = browserType.contains("MSIE") ? att.fileName.getBytes() : att.fileName.getBytes("utf-8");
		response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
		response.setHeader("Content-Length", String.valueOf(fileLength));

		bis = new BufferedInputStream(new FileInputStream(downLoadPath));
		bos = new BufferedOutputStream(response.getOutputStream());
		byte[] buff = new byte[2048];
		int bytesRead;
		while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
			bos.write(buff, 0, bytesRead);
		}
		bis.close();
		bos.close();
	}
	
	/**
	 * 下载启明HTML附件
	 * @param att
	 * @param request
	 * @param response
	 * @return
	 */
	def downloadHtml(AttExpHtml att,HttpServletRequest request,HttpServletResponse response){
		request.setCharacterEncoding("UTF-8");
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;

		String downLoadPath = att.path;
		long fileLength = new File(downLoadPath).length();
		def browserType = request.getHeader("User-Agent")
		response.setContentType("application/octet-stream");
		byte[] bytes = browserType.contains("MSIE") ? att.fileName.getBytes() : att.fileName.getBytes("utf-8");
		response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
		response.setHeader("Content-Length", String.valueOf(fileLength));

		bis = new BufferedInputStream(new FileInputStream(downLoadPath));
		bos = new BufferedOutputStream(response.getOutputStream());
		byte[] buff = new byte[2048];
		int bytesRead;
		while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
			bos.write(buff, 0, bytesRead);
		}
		bis.close();
		bos.close();
	}
	
	/**
	 * 下载漏洞附件
	 * @param att
	 * @param request
	 * @param response
	 * @return
	 */
	def downloadAttFlaw(TUser user,Flaw flawInstance,Attachment att,HttpServletRequest request,HttpServletResponse response){
		try{

			//当前用户是否可见该漏洞
			def isPublic = false//是否漏洞公开可见
			if (!(!flawInstance || !att || flawInstance.status != 9 || flawInstance.isOpen != 1
					|| flawInstance.enable != 1 || new Date().compareTo(flawInstance.openTime)<0
					|| flawInstance.parentFlaw != null || flawInstance.isAttShow == 0 || flawInstance.isEvent == 1)){
				System.out.println("return 是公开漏洞=true");
				isPublic = true
			}
			if(user != null){
				if(!isPublic && flawInstance?.user?.id != user.id){
					System.out.println("判断用户相等");
					//判断当前用户是否有关联的漏洞
					if(user.getManufacturer() != null){
						def sql = "select * from tuser_flaw where manufacturer_id="+user.getManufacturer().getId()+" and flaw_id="+flawInstance.getId();
						def paramList = new ArrayList()
						def res = new groovy.sql.Sql(dataSource).rows(sql,paramList)
						if(res == null || res.size() == 0){
							System.out.println("return 判断当前用户是否有关联的漏洞");
							render(view:"/error")
							return
						}
						System.out.println("存在关联漏洞");
					}else{
						System.out.println("return 当前用户是否可见该漏洞");
						render(view:"/error")
						return
					}
				}
			}else if(!isPublic){
				System.out.println("return 用户未登录且未公开漏洞");
				render(view:"/error")
				return
			}

			//漏洞跟附件id是否一致
			if(flawInstance.getAttachment() == null || att == null || flawInstance.getAttachment().getId() != att.getId()) {
				System.out.println("return 漏洞跟附件id是否一致");
				render(view:"/error")
				return
			}
			//判断不是证书的才能下载
			def paramList = new ArrayList()
			def sql = "select * from certificate where pdf_attachment_id="+att.getId();
			def res = new groovy.sql.Sql(dataSource).rows(sql,paramList)
			System.out.println("res="+res);
			if(res != null && res.size()>0){
				System.out.println("return 判断不是证书的才能下载");
				render(view:"/error")
				return
			}
			System.out.println("============输出第4行======判断通过==");
			request.setCharacterEncoding("UTF-8");
			BufferedInputStream bis = null;
			BufferedOutputStream bos = null;

			String downLoadPath = att.path;
			long fileLength = new File(downLoadPath).length();
			def browserType = request.getHeader("User-Agent")
			response.setContentType("application/octet-stream");
			byte[] bytes = browserType.contains("MSIE") ? att.fileName.getBytes() : att.fileName.getBytes("utf-8");
			response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
			response.setHeader("Content-Length", String.valueOf(fileLength));

			bis = new BufferedInputStream(new FileInputStream(downLoadPath));
			bos = new BufferedOutputStream(response.getOutputStream());
			byte[] buff = new byte[2048];
			int bytesRead;
			while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
				bos.write(buff, 0, bytesRead);
			}
			bis.close();
			bos.close();
		}catch(Exception e){
			e.printStackTrace();
			render(view:"/error")
			return
		}
	}

	/**
	 * 下载文件
	 * @param att
	 * @param request
	 * @param response
	 * @return
	 */
	def downloadAttFile(Attachment att,HttpServletRequest request,HttpServletResponse response){
		try{
			request.setCharacterEncoding("UTF-8");
			BufferedInputStream bis = null;
			BufferedOutputStream bos = null;

			String downLoadPath = att.path;
			long fileLength = new File(downLoadPath).length();
			def browserType = request.getHeader("User-Agent")
			response.setContentType("application/octet-stream");
			byte[] bytes = browserType.contains("MSIE") ? att.fileName.getBytes() : att.fileName.getBytes("utf-8");
			response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
			response.setHeader("Content-Length", String.valueOf(fileLength));

			bis = new BufferedInputStream(new FileInputStream(downLoadPath));
			bos = new BufferedOutputStream(response.getOutputStream());
			byte[] buff = new byte[2048];
			int bytesRead;
			while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
				bos.write(buff, 0, bytesRead);
			}
			bis.close();
			bos.close();
		}catch(Exception e){
			e.printStackTrace();
		}
	}

	/**
	 * 下载补丁附件
	 * @param att
	 * @param request
	 * @param response
	 * @return
	 */
	def downloadAttPatch(TUser user,PatchInfo patchInfo, Attachment att, HttpServletRequest request, HttpServletResponse response){
		try{
			if(patchInfo == null){
				return ;
			}
			//当前用户是否可见该漏洞
			def isPublic = false//是否漏洞公开可见
			def flawInstance = patchInfo.flaw;
			if (!(!flawInstance || !att || flawInstance.status != 9 || flawInstance.isOpen != 1
					|| flawInstance.enable != 1 || new Date().compareTo(flawInstance.openTime)<0
					|| flawInstance.parentFlaw != null || flawInstance.isAttShow == 0 || flawInstance.isEvent == 1)){
				System.out.println("return 是公开漏洞=true");
				isPublic = true
			}
			if(user != null){
				if(!isPublic && flawInstance?.user?.id != user.id){
					System.out.println("判断用户相等");
					//判断补丁对应的漏洞，是否在影响我的资产漏洞列表中
					if(user.getManufacturer() != null){
						def hql = " from Flaw f where exists(select fp.id from FlawProduct fp where f.id=fp.flaw.id and exists( " +
								"select pi.id from ProductInfo pi where fp.product.id = pi.id and pi.manufacturer = ? " +
								")) and f.id="+flawInstance.id;
						def flawList = Flaw.executeQuery(hql, [user?.manufacturer], [max: 10000, offset: 0])
						if(flawList == null || flawList.size() == 0){
							System.out.println("return 判断补丁对应的漏洞，是否在影响我的资产漏洞列表中");
							render(view:"/error")
							return
						}
						System.out.println("存在关联漏洞");
					}else{
						System.out.println("return 当前用户是否可见该漏洞");
						render(view:"/error")
						return
					}
				}
			}else if(!isPublic){
				System.out.println("return 用户未登录且未公开漏洞");
				render(view:"/error")
				return
			}

			//补丁跟附件id是否一致
			if(patchInfo == null || att == null || patchInfo.getAttachment().getId() != att.getId()) {
				System.out.println("return 补丁跟附件id是否一致");
				render(view:"/error")
				return
			}

			System.out.println("============输出第4行======判断通过==");
			request.setCharacterEncoding("UTF-8");
			BufferedInputStream bis = null;
			BufferedOutputStream bos = null;

			String downLoadPath = att.path;
			long fileLength = new File(downLoadPath).length();
			def browserType = request.getHeader("User-Agent")
			response.setContentType("application/octet-stream");
			byte[] bytes = browserType.contains("MSIE") ? att.fileName.getBytes() : att.fileName.getBytes("utf-8");
			response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
			response.setHeader("Content-Length", String.valueOf(fileLength));

			bis = new BufferedInputStream(new FileInputStream(downLoadPath));
			bos = new BufferedOutputStream(response.getOutputStream());
			byte[] buff = new byte[2048];
			int bytesRead;
			while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
				bos.write(buff, 0, bytesRead);
			}
			bis.close();
			bos.close();
		}catch(Exception e){
			e.printStackTrace();
		}
	}


	/**
	 * 下载证书附件
	 * @param att
	 * @param request
	 * @param response
	 * @return
	 */
	def downloadAttCertificate(Attachment att,HttpServletRequest request,HttpServletResponse response){
		//判断不是证书的才能下载
		def paramList = new ArrayList()
		def sql = "select * from certificate where pdf_attachment_id="+att.getId();
		def res = new groovy.sql.Sql(dataSource).rows(sql,paramList)
		if(res == null || res.size()==0){
			render(view:"/error")
			return
		}

		request.setCharacterEncoding("UTF-8");
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;

		String downLoadPath = att.path;
		long fileLength = new File(downLoadPath).length();
		def browserType = request.getHeader("User-Agent")
		response.setContentType("application/octet-stream");
		byte[] bytes = browserType.contains("MSIE") ? att.fileName.getBytes() : att.fileName.getBytes("utf-8");
		response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
		response.setHeader("Content-Length", String.valueOf(fileLength));

		bis = new BufferedInputStream(new FileInputStream(downLoadPath));
		bos = new BufferedOutputStream(response.getOutputStream());
		byte[] buff = new byte[2048];
		int bytesRead;
		while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
			bos.write(buff, 0, bytesRead);
		}
		bis.close();
		bos.close();
	}
	
	def downloadFile(def filePath,def fileName,HttpServletRequest request,HttpServletResponse response){
		request.setCharacterEncoding("UTF-8");
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;

		long fileLength = new File(filePath).length();
		def browserType = request.getHeader("User-Agent")
		response.setContentType("application/octet-stream");
		byte[] bytes = browserType.contains("MSIE") ? fileName.getBytes() : fileName.getBytes("utf-8");
		response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
		response.setHeader("Content-Length", String.valueOf(fileLength));

		bis = new BufferedInputStream(new FileInputStream(filePath));
		bos = new BufferedOutputStream(response.getOutputStream());
		byte[] buff = new byte[2048];
		int bytesRead;
		while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
			bos.write(buff, 0, bytesRead);
		}
		bis.close();
		bos.close();
	}

	def checkFile(def file){
		println "file="+file
		def fileName = file.getOriginalFilename()
		println "fileName="+fileName
		def fileNameSuffix=fileName.substring(fileName.lastIndexOf("."))
		println "fileNameSuffix="+fileNameSuffix
		def fileSize = file.size
		InputStream is = new ByteArrayInputStream(file.getBytes())
		def filetype = FileTypeUtil.getFileByFile(is)

		if (fileNameSuffix.equals(".py")){
			filetype="application/msword"
		}
		println "filetype="+filetype

		println "filetype="+filetype
		
		if(file){
			if((fileNameSuffix.equals(".doc")||fileNameSuffix.equals(".docx")||fileNameSuffix.equals(".zip")||fileNameSuffix.equals(".py")||fileNameSuffix.equals(".rar"))&& fileSize>0 && ("application/msword".equals(filetype)||"application/zip".equals(filetype)||"rar".equals(filetype))){
				return true
			}else{
				return false
			}
		}else{
			return false
		}
	}

	def checkAvatar(def file){
		def fileName = file.getAbsolutePath()
		println "fileName="+fileName
		def fileNameSuffix=fileName.substring(fileName.lastIndexOf("."))
		println "fileNameSuffix="+fileNameSuffix
		
		if(!".jpg".equals(fileNameSuffix) && !".png".equals(fileNameSuffix) && !".gif".equals(fileNameSuffix)){
			return false;
		}
		
		String filetype = FileTypeUtil.getImageFileType(file)
		System.out.println(filetype);
		
		if(filetype){
			return true
		}else{
			return false
		}
	}
	
	def checkZipFile(def file){
		def fileName = file.getOriginalFilename()
		def fileNameSuffix=fileName.substring(fileName.lastIndexOf("."))
		
		InputStream is = new ByteArrayInputStream(file.getBytes())
		def filetype = FileTypeUtil.getFileByFile(is)
		
		if(file.size<=10*1024*1024){
			if(fileNameSuffix.equals(".zip") && "application/zip".equals(filetype)){
				return true
			}else{
				return false
			}
		}else{
			return false
		}
	}

	def checkExcelFile(def file){
		def fileName = file.getOriginalFilename()
		def fileNameSuffix=fileName.substring(fileName.lastIndexOf("."))

		InputStream is = new ByteArrayInputStream(file.getBytes())
		def filetype = FileTypeUtil.getFileByFile(is)

		if(file.size<=10*1024*1024){
			if(fileNameSuffix == ".xlsx"||fileNameSuffix == ".xls"){
				return true
			}else{
				return false
			}
		}else{
			return false
		}
	}

}
