package com.cnvd

class FileService {
	def grailsApplication
   def uploadFile(def file,def flash,def id,def filePath,def attachment){
	  
	   
	   try{
		String fileName= file.getOriginalFilename()
	   fileName=fileName.substring(fileName.lastIndexOf("."));
	   
	   String fileUrlStr=filePath+"/"+id+fileName

	   attachment.fileName=file.getOriginalFilename()
	   attachment.fileType=file.getContentType()
	   attachment.fileSize=file.getSize()
	   attachment.path=fileUrlStr
	   attachment.save()
	  
       file.transferTo(new File(fileUrlStr));
	   }catch(Exception e){
	   e.printStackTrace()
	    flash.message="上传文件失败"
	   	return false
	   }
	
	
	   
	   return true
   }
   def checkFile(def file,def flash){
	   //验证文件是否为空
	    if(file.empty){
			flash.message="上传文件不可为空"
			return false
		}
		//验证文件类型
		String fileName= file.getOriginalFilename()
		fileName=fileName.substring(fileName.lastIndexOf("."));
		if(!fileName.equals(".zip") && !fileName.equals(".rar")){
			flash.message='上传文件类型不正确'
			return false
		}
   		return true
   }
   def downLoadFile(){
	   
   }
   def createChartFile(def filePath,data){
	   File file=new File(filePath);
	   if (!file.getParentFile().exists()) {
				file.getParentFile().mkdirs();
		}
	   if(!file.exists()){
		   
			   file.createNewFile()
		   }
//	 Writer outTxt = new OutputStreamWriter(new FileOutputStream(file,false), "UTF-8");
////	 FileWriter fw = new FileWriter(file);
////	 fw.getEncoding().getBytes("utf-8");
//	 outTxt.write(data);
//	 outTxt.close()
	   OutputStreamWriter writer = new OutputStreamWriter(new FileOutputStream(file),"UTF-8");
	   BufferedWriter fw=new BufferedWriter(writer);
		 //FileWriter fw = new FileWriter(file);
		 //fw.getEncoding().getBytes("utf-8");
		 fw.write(data);
		 fw.close();
   }
}
