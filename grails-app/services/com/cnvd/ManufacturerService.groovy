package com.cnvd

import com.alibaba.fastjson.JSON
import com.cnvd.utils.DateUtil
import com.cnvd.utils.HttpUtil
import com.cnvd.utils.SQLUtil
import com.cnvd.vo.Response
import org.springframework.core.io.ClassPathResource

class ManufacturerService {


    def getList(def params) {
        List<Map<String, Object>> resultList = getSelectResult(params, 0)
        return resultList;
    }

    def getCount(def params) {
        List<Map<String, Object>> resultList = getSelectResult(params, 1)
        Long flawInstanceTotal = Long.valueOf(0)
        if (resultList != null && resultList.size() > 0) {
            flawInstanceTotal = Long.valueOf(resultList.get(0).get("countAs").toString())
        }
        return flawInstanceTotal

    }

    def getSelectResult(def params,Integer type){

        StringBuilder sql = new StringBuilder() //查询sql
        List<Object> paramList = new ArrayList() //参数集合

        sql.append(" SELECT")
			.append(" m.id,m.`name`,m.email,m.address,m.date_created,m.last_updated,m.description,m.keyword")
			.append("  FROM manufacturer m ")
			.append(" where 1=1 ")
		if (params.name) {
			sql.append(" and m.name like ?")
			paramList.add("%" + params.name.trim() +"%")
		}
        if (params.email) {
            sql.append(" and m.email = ?")
            paramList.add(params.email.trim())
        }

        //type(0-查询list 1-查询count)
        String newSql = sql.toString()
       // println "newSql的值为:"+newSql
        if(type!=null && 1==type) {
            newSql = "SELECT COUNT(*) AS countAs " + newSql.substring(newSql.indexOf("FROM"), newSql.length())
        }else{
            //String orderByStr = " ORDER BY m.id DESC"
            params.max = Math.min(params.max ? params.int('max') : 20, 50)
            params.offset = params.offset ? params.int('offset') : 0
            Integer maxResults = params.max//limit第二位
            Integer offset = params.offset //limit第一位
            println "sql的值为:"+sql
            println "maxResults的值为:"+maxResults
            println "offset的值为:"+offset
            sql.append(" LIMIT ").append(offset).append(",").append(maxResults)
            newSql = sql.toString()
        }

        println "newSql的值为:"+newSql


        List<Map<String, Object>> dataResult = SQLUtil.getResult(newSql, paramList)
        return dataResult

    }


}
