package com.cnvd

import com.cnvd.common.Attachment
import com.cnvd.common.AttachmentPdf
import com.cnvd.common.Task
import com.cnvd.flawInfo.Exploit
import com.cnvd.flawInfo.ExploitAttachment
import com.cnvd.flawInfo.ExploitAttachmentPdf
import com.cnvd.patchInfo.DisposalInfo
import com.cnvd.patchInfo.DisposalInfoAttachment
import com.cnvd.patchInfo.DisposalInfoAttachmentPdf
import com.cnvd.utils.Constants
import com.cnvd.utils.DateUtil
import com.cnvd.utils.DateUtils
import com.cnvd.utils.FileTypeUtil
import com.cnvd.utils.OfficeToPDF
import com.cnvd.utils.SendMsgProducer
import net.sf.json.JSONObject
import org.springframework.beans.BeanUtils
import org.springframework.core.io.ClassPathResource

import java.text.SimpleDateFormat

class TaskService {

	static transactional = true
	def attachmentService
	def grailsApplication
	def toJsonObjectService
	def submitTask(def params,def file) {
        println "exploitTimes="+params.exploitTimes
		println "params.attIds="+params.attIds
		def root =""
		//def root ="D:"
		String filePath = ""
		def taskInstance = Task.get(params.id)
		if(taskInstance.getType() == 1){
			filePath = "${grailsApplication.config.filePath.exploitAttFilePath}" //文件的路径
		}else if(taskInstance.getType() == 3){
			filePath = "${grailsApplication.config.filePath.patchInfoAttFilePath}" //文件的路径
		}

		if(taskInstance.status!=Constants.TASK_SUBMIT && taskInstance.status!=Constants.TASK_BACK && taskInstance.status!=Constants.TASK_ING){
			return false;
		}


		/*def taskHandleSynchro = new TaskHandleSynchro()
		taskHandleSynchro.dateCreated=new Date()
		taskHandleSynchro.synchroId=0
		taskHandleSynchro.status=0
		taskHandleSynchro.taskId=taskInstance.id*/

		def properties = org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(new ClassPathResource("app-config.properties"))
		def kafkaIp = properties.getProperty("kafka.ip")
		int kafkaCount = Integer.valueOf(properties.getProperty("kafka.count"))
		def array=new net.sf.json.JSONArray()

		JSONObject jsonImp = new JSONObject()
		jsonImp.put("time", DateUtil.get4yMdHms( new Date()))


		if(taskInstance.type==Constants.FLAW_EXPOIT){
			Exploit exploit= new Exploit();
			if(taskInstance.status==Constants.TASK_SUBMIT || taskInstance.status==Constants.TASK_BACK){
				exploit=taskInstance.exploit
				exploit.properties=params
			}else{
				exploit=new Exploit(params)
				exploit.exploitType=taskInstance.getTargetType()
				exploit.tuser=taskInstance.getTargetTUser()
				exploit.flaw=taskInstance.getFlaw()
				exploit.status=Constants.EXPLOIT_WAIT
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
				exploit.exploitTime = sdf.parse(params.exploitTimes)
			}
			def flawInfoInstance =taskInstance.flaw


			taskInstance.status=Constants.TASK_SUBMIT
				taskInstance.submitDate=new Date()
				if(!exploit.save(flush:true)){
				exploit.errors.allErrors.each{ println "error|"+it }
				//ExploitAttachment.executeUpdate('delete from ExploitAttachment where exploit = ?',[exploit])
			}else{
				JSONObject exploitJsonTemp = new JSONObject()
				JSONObject exploitJson= toJsonObjectService.toJsonObject(exploit)
				exploitJsonTemp.put("operate",0)
				exploitJsonTemp.put("flawId",exploit.getFlaw().getId())
				exploitJsonTemp.put("isPush",1)
				exploitJsonTemp.put("isLoophole",0)
				exploitJsonTemp.put("entity",exploitJson.toString())
				array.add(exploitJsonTemp)


				/*taskHandleSynchro.exploitId=exploit.id*/
				ExploitAttachment.executeUpdate('delete from ExploitAttachment where exploit = ?',[exploit])
				ExploitAttachment.executeUpdate('delete from ExploitAttachmentPdf where exploit = ?',[exploit])
				JSONObject exploitAttachmentJsonTemp = new JSONObject()
				exploitAttachmentJsonTemp.put("table","exploit_attachment")
				exploitAttachmentJsonTemp.put("operate",2)
				exploitAttachmentJsonTemp.put("column","exploit_id")
				exploitAttachmentJsonTemp.put("isPush",1)
				exploitAttachmentJsonTemp.put("isLoophole",0)
				exploitAttachmentJsonTemp.put("entity",exploit.getId())
				array.add(exploitAttachmentJsonTemp)
				JSONObject exploitAttachmentPdfJsonTemp = new JSONObject()
				exploitAttachmentPdfJsonTemp.put("table","exploit_attachment_pdf")
				exploitAttachmentPdfJsonTemp.put("operate",2)
				exploitAttachmentPdfJsonTemp.put("column","exploit_id")
				exploitAttachmentPdfJsonTemp.put("isPush",1)
				exploitAttachmentPdfJsonTemp.put("isLoophole",0)
				exploitAttachmentPdfJsonTemp.put("entity",exploit.getId())
				array.add(exploitAttachmentPdfJsonTemp)



				if(params.attIds){
					def attArr = params.attIds.split(";")
					attArr.each{
						def attachment = Attachment.get(it)
						JSONObject attachmentSaveJsonTemp = new JSONObject()
						JSONObject attachmentSaveJson= toJsonObjectService.toJsonObject(attachment)
						attachmentSaveJsonTemp.put("operate", 0)
						attachmentSaveJsonTemp.put("flawId", exploit.getFlaw().getId())
						attachmentSaveJsonTemp.put("isPush", 1)
						attachmentSaveJsonTemp.put("isLoophole", 0)
						attachmentSaveJsonTemp.put("entity", attachmentSaveJson.toString())
						array.add(attachmentSaveJsonTemp)
						/*if(attachment){
							def tempExploit = ExploitAttachment.findByAttachment(attachment)
							if(!tempExploit){*/
								def exploitAttachment = new ExploitAttachment()
								exploitAttachment.exploit = exploit
								exploitAttachment.attachment = attachment
								if (exploitAttachment.save(flush:true)){
									JSONObject exploitAttachmentSaveJsonTemp = new JSONObject()
									JSONObject exploitAttachmentSaveJson= toJsonObjectService.toJsonObject(exploitAttachment)
									exploitAttachmentSaveJsonTemp.put("operate", 0)
									exploitAttachmentSaveJsonTemp.put("flawId", exploit.getFlaw().getId())
									exploitAttachmentSaveJsonTemp.put("isPush", 1)
									exploitAttachmentSaveJsonTemp.put("isLoophole", 0)
									exploitAttachmentSaveJsonTemp.put("entity", exploitAttachmentSaveJson.toString())
									array.add(exploitAttachmentSaveJsonTemp)
								}

								def fileName=attachment.getFileName()
								def realName=DateUtils.getCurrentTime()
								def attachmentPdf = new AttachmentPdf()
								//生成pdf附件
								def fileNameSuffix=fileName.substring(fileName.lastIndexOf("."))
								//如果上传的附件是word格式
								if (fileNameSuffix.equals(".doc")||fileNameSuffix.endsWith(".docx")){
									def newFileName=fileName.substring(0, fileName.lastIndexOf("."))

									def result = OfficeToPDF.office2PDF(root+attachment.getPath(), root+filePath+realName+".pdf")
									//操作成功与否的提示信息. 如果返回 -1, 表示找不到源文件, 或url.properties配置错误; 如果返回 0,则表示操作成功; 返回1, 则表示转换失败; 返回2, 则表示打水印失败
									if (result==-1){
										println("PDF生成找不到源文件")
										return false
									}else if (result==1){
										println("PDF生成转换失败")
										return false
									}else if (result==2){
										println("PDF生成打水印失败")
										return false
									}else if (result==0){
										File pdfFile = new File(root+filePath+realName+"_pdf.pdf")
										//pdf附件保存
										attachmentPdf.realName=realName+"_pdf.pdf"
										attachmentPdf.fileName=newFileName+"_pdf.pdf"
										attachmentPdf.fileType="application/pdf"
										attachmentPdf.fileSize=pdfFile.length()
										attachmentPdf.path=filePath+realName+"_pdf.pdf"
										attachmentPdf.attachmentId=attachment.id
										if (attachmentPdf.save(flush:true)) {
											JSONObject attachmentPdfSaveJsonTemp = new JSONObject()
											JSONObject attachmentPdfSaveJson= toJsonObjectService.toJsonObject(attachmentPdf)
											attachmentPdfSaveJsonTemp.put("operate", 0)
											attachmentPdfSaveJsonTemp.put("flawId", exploit.getFlaw().getId())
											attachmentPdfSaveJsonTemp.put("isPush", 1)
											attachmentPdfSaveJsonTemp.put("isLoophole", 0)
											attachmentPdfSaveJsonTemp.put("entity", attachmentPdfSaveJson.toString())
											array.add(attachmentPdfSaveJsonTemp)
										}

										def exploitAttachmentPdf = new ExploitAttachmentPdf()
										exploitAttachmentPdf.exploit = exploit
										exploitAttachmentPdf.attachmentPdf=attachmentPdf
										if (exploitAttachmentPdf.save(flush:true)) {
											JSONObject exploitAttachmentPdfSaveJsonTemp = new JSONObject()
											JSONObject exploitAttachmentPdfSaveJson= toJsonObjectService.toJsonObject(exploitAttachmentPdf)
											exploitAttachmentPdfSaveJsonTemp.put("operate", 0)
											exploitAttachmentPdfSaveJsonTemp.put("flawId", exploit.getFlaw().getId())
											exploitAttachmentPdfSaveJsonTemp.put("isPush", 1)
											exploitAttachmentPdfSaveJsonTemp.put("isLoophole", 0)
											exploitAttachmentPdfSaveJsonTemp.put("entity", exploitAttachmentPdfSaveJson.toString())
											array.add(exploitAttachmentPdfSaveJsonTemp)

										}
									}
								}



							/*}
						}*/
					}
				}
			}

			taskInstance.exploit=exploit

			//增加处置信息-start
			if(taskInstance.isExpoitPatch!=null && taskInstance.isExpoitPatch==1) {
				DisposalInfo patchInfo = new DisposalInfo(params)
				patchInfo.createSource = taskInstance.getTargetType().toString()
				patchInfo.tuser = taskInstance.getTargetTUser()
				patchInfo.flaw = taskInstance.getFlaw()
				patchInfo.status = Constants.PATCH_WAIT
				if (patchInfo.save(flush: true)) {
					/*taskHandleSynchro.patchInfoId=patchInfo.id*/
					JSONObject patchInfoJsonTemp = new JSONObject()
					JSONObject patchInfoJson = toJsonObjectService.toJsonObject(patchInfo)
					patchInfoJsonTemp.put("operate", 0)
					patchInfoJsonTemp.put("flawId", patchInfo.getFlaw().getId())
					patchInfoJsonTemp.put("isPush", 1)
					patchInfoJsonTemp.put("isLoophole", 0)
					patchInfoJsonTemp.put("entity", patchInfoJson.toString())
					array.add(patchInfoJsonTemp)

					DisposalInfoAttachment.executeUpdate('delete from DisposalInfoAttachment where disposalInfo = ?', [patchInfo])
					DisposalInfoAttachmentPdf.executeUpdate('delete from DisposalInfoAttachmentPdf where disposalInfo = ?', [patchInfo])
					JSONObject patchInfoAttachmentJsonTemp = new JSONObject()
					patchInfoAttachmentJsonTemp.put("table", "patch_info_attachment")
					patchInfoAttachmentJsonTemp.put("operate", 2)
					patchInfoAttachmentJsonTemp.put("column", "patch_info_id")
					patchInfoAttachmentJsonTemp.put("isPush", 1)
					patchInfoAttachmentJsonTemp.put("isLoophole", 0)
					patchInfoAttachmentJsonTemp.put("entity", patchInfo.getId())
					array.add(patchInfoAttachmentJsonTemp)
					JSONObject patchInfoAttachmentPdfJsonTemp = new JSONObject()
					patchInfoAttachmentPdfJsonTemp.put("table", "patch_info_attachment_pdf")
					patchInfoAttachmentPdfJsonTemp.put("operate", 2)
					patchInfoAttachmentPdfJsonTemp.put("column", "patch_info_id")
					patchInfoAttachmentPdfJsonTemp.put("isPush", 1)
					patchInfoAttachmentPdfJsonTemp.put("isLoophole", 0)
					patchInfoAttachmentPdfJsonTemp.put("entity", patchInfo.getId())
					array.add(patchInfoAttachmentPdfJsonTemp)


					if (params.attIds) {
						def attArr = params.attIds.split(";")
						attArr.each {
							def attachment = Attachment.get(it)
							JSONObject attachmentSaveJsonTemp = new JSONObject()
							JSONObject attachmentSaveJson = toJsonObjectService.toJsonObject(attachment)
							attachmentSaveJsonTemp.put("operate", 0)
							attachmentSaveJsonTemp.put("flawId", patchInfo.getFlaw().getId())
							attachmentSaveJsonTemp.put("isPush", 1)
							attachmentSaveJsonTemp.put("isLoophole", 0)
							attachmentSaveJsonTemp.put("entity", attachmentSaveJson.toString())
							array.add(attachmentSaveJsonTemp)
							/*if(attachment){
							def tempPacthInfo = PatchInfoAttachment.findByAttachment(attachment)
							if(!tempPacthInfo){*/
							def patchInfoAttachment = new DisposalInfoAttachment()
							patchInfoAttachment.disposalInfo = patchInfo
							patchInfoAttachment.attachment = attachment
							if (patchInfoAttachment.save(flush: true)) {
								JSONObject patchInfoAttachmentSaveJsonTemp = new JSONObject()
								JSONObject patchInfoAttachmentSaveJson = toJsonObjectService.toJsonObject(patchInfoAttachment)
								patchInfoAttachmentSaveJsonTemp.put("operate", 0)
								patchInfoAttachmentSaveJsonTemp.put("flawId", patchInfo.getFlaw().getId())
								patchInfoAttachmentSaveJsonTemp.put("isPush", 1)
								patchInfoAttachmentSaveJsonTemp.put("isLoophole", 0)
								patchInfoAttachmentSaveJsonTemp.put("entity", patchInfoAttachmentSaveJson.toString())
								array.add(patchInfoAttachmentSaveJsonTemp)


							}
							def fileName = attachment.getFileName()
							def realName = DateUtils.getCurrentTime()
							def attachmentPdf = new AttachmentPdf()
							//生成pdf附件
							def fileNameSuffix = fileName.substring(fileName.lastIndexOf("."))
							//如果上传的附件是word格式
							if (fileNameSuffix.equals(".doc") || fileNameSuffix.endsWith(".docx")) {
								def newFileName = fileName.substring(0, fileName.lastIndexOf("."))

								def result = OfficeToPDF.office2PDF(root + attachment.getPath(), root + filePath + realName + ".pdf")
								//操作成功与否的提示信息. 如果返回 -1, 表示找不到源文件, 或url.properties配置错误; 如果返回 0,则表示操作成功; 返回1, 则表示转换失败; 返回2, 则表示打水印失败
								if (result == -1) {
									println("PDF生成找不到源文件")
									return false
								} else if (result == 1) {
									println("PDF生成转换失败")
									return false
								} else if (result == 2) {
									println("PDF生成打水印失败")
									return false
								} else if (result == 0) {
									File pdfFile = new File(root + filePath + realName + "_pdf.pdf")
									//pdf附件保存
									attachmentPdf.realName = realName + "_pdf.pdf"
									attachmentPdf.fileName = newFileName + "_pdf.pdf"
									attachmentPdf.fileType = "application/pdf"
									attachmentPdf.fileSize = pdfFile.length()
									attachmentPdf.path = filePath + realName + "_pdf.pdf"
									attachmentPdf.attachmentId = attachment.id
									if (attachmentPdf.save(flush: true)) {
										JSONObject attachmentPdfSaveJsonTemp = new JSONObject()
										JSONObject attachmentPdfSaveJson = toJsonObjectService.toJsonObject(attachmentPdf)
										attachmentPdfSaveJsonTemp.put("operate", 0)
										attachmentPdfSaveJsonTemp.put("flawId", patchInfo.getFlaw().getId())
										attachmentPdfSaveJsonTemp.put("isPush", 1)
										attachmentPdfSaveJsonTemp.put("isLoophole", 0)
										attachmentPdfSaveJsonTemp.put("entity", attachmentPdfSaveJson.toString())
										array.add(attachmentPdfSaveJsonTemp)
									}

									def patchInfoAttachmentPdf = new DisposalInfoAttachmentPdf()
									patchInfoAttachmentPdf.disposalInfo = patchInfo
									patchInfoAttachmentPdf.attachmentPdf = attachmentPdf
									if (patchInfoAttachmentPdf.save(flush: true)) {
										JSONObject patchInfoAttachmentPdfSaveJsonTemp = new JSONObject()
										JSONObject patchInfoAttachmentPdfSaveJson = toJsonObjectService.toJsonObject(patchInfoAttachmentPdf)
										patchInfoAttachmentPdfSaveJsonTemp.put("operate", 0)
										patchInfoAttachmentPdfSaveJsonTemp.put("flawId", patchInfo.getFlaw().getId())
										patchInfoAttachmentPdfSaveJsonTemp.put("isPush", 1)
										patchInfoAttachmentPdfSaveJsonTemp.put("isLoophole", 0)
										patchInfoAttachmentPdfSaveJsonTemp.put("entity", patchInfoAttachmentPdfSaveJson.toString())
										array.add(patchInfoAttachmentPdfSaveJsonTemp)
									}
								}
							}


							/*	}
						}*/
						}
					}
				}



				Task taskDisposalInfo = new Task()
//                ConvertUtils.register(new DateConverter(null), java.util.Date.class);
				BeanUtils.copyProperties(taskDisposalInfo, taskInstance)

				taskDisposalInfo.disposalInfo = patchInfo
				taskDisposalInfo.id = null
				taskDisposalInfo.type = 3
				taskDisposalInfo.isExpoitPatch = 0
				taskDisposalInfo.targetTUser = taskInstance.targetTUserDisposal
				if (!taskDisposalInfo.save(flush: true)) {
					taskDisposalInfo.errors.allErrors.each { println "taskDisposalInfo.save|"+it }
				}
			}
			//增加处置信息-end

			if (taskInstance.save()) {
				JSONObject taskInstanceJsonTemp = new JSONObject()
				JSONObject taskInstanceJsonJson= toJsonObjectService.toJsonObject(taskInstance)
				taskInstanceJsonTemp.put("operate", 1)
				taskInstanceJsonTemp.put("flawId", taskInstance.getFlaw().getId())
				taskInstanceJsonTemp.put("isPush", 1)
				taskInstanceJsonTemp.put("isLoophole", 0)
				taskInstanceJsonTemp.put("entity", taskInstanceJsonJson.toString())
				array.add(taskInstanceJsonTemp)

			}
			def flaw = taskInstance.flaw
			println "flaw="+flaw
			if ( flaw!=null && flaw.push== 1) {
				jsonImp.put("date",array.toString())

				for(int i = 0; i < kafkaCount; i++){
					def kafkaTopic = properties.getProperty("kafka.topic"+i)
					try{
						new SendMsgProducer(jsonImp,kafkaIp,kafkaTopic).start()
					}catch (Exception e) {
						def synchroErrData = new SynchroErrData();
						synchroErrData.errorData=jsonImp.toString();
						synchroErrData.errorReason=e.message.toString();
						synchroErrData.status=0;
						synchroErrData.topic=kafkaTopic;
						synchroErrData.save(flush: true)
					}
				}
			}
		}

		if(taskInstance.type==Constants.FLAW_PATCH){
			DisposalInfo patchInfo=null
			if(taskInstance.status==Constants.TASK_SUBMIT || taskInstance.status==Constants.TASK_BACK){
				patchInfo=taskInstance.disposalInfo
				if(params.isDeleteAtt!=null && params.int('isDeleteAtt')==1){
					patchInfo.attachment=null
				}
				patchInfo.properties=params
				println "patchInfo的值为："+ patchInfo
			}else{
				patchInfo=new DisposalInfo(params)
				patchInfo.createSource=taskInstance.getTargetType().toString()
				patchInfo.tuser=taskInstance.getTargetTUser()
				patchInfo.flaw=taskInstance.getFlaw()
				patchInfo.status=Constants.PATCH_WAIT
			}

			/*if(!file.isEmpty() ){
				if(!checkFile(file)){
					return false
				}
				//表示附件有内容filePath.exploitAttFilePath
				String filePath = "${grailsApplication.config.filePath.patchInfoAttFilePath}" //文件的路径
				String realName = CommentsUtil.getCurrentTime() //文件的真实文件名
				def attachment = attachmentService.uploadFile(file,filePath,realName)
				patchInfo.attachment = attachment
			}*/
			taskInstance.status=Constants.TASK_SUBMIT
			taskInstance.submitDate=new Date()
			if(patchInfo.save(flush:true)){
				/*taskHandleSynchro.patchInfoId=patchInfo.id*/
				JSONObject patchInfoJsonTemp = new JSONObject()
				JSONObject patchInfoJson= toJsonObjectService.toJsonObject(patchInfo)
				patchInfoJsonTemp.put("operate",0)
				patchInfoJsonTemp.put("flawId",patchInfo.getFlaw().getId())
				patchInfoJsonTemp.put("isPush",1)
				patchInfoJsonTemp.put("isLoophole",0)
				patchInfoJsonTemp.put("entity",patchInfoJson.toString())
				array.add(patchInfoJsonTemp)

				DisposalInfoAttachment.executeUpdate('delete from DisposalInfoAttachment where disposalInfo = ?',[patchInfo])
				DisposalInfoAttachmentPdf.executeUpdate('delete from DisposalInfoAttachmentPdf where disposalInfo = ?',[patchInfo])
				JSONObject patchInfoAttachmentJsonTemp = new JSONObject()
				patchInfoAttachmentJsonTemp.put("table","patch_info_attachment")
				patchInfoAttachmentJsonTemp.put("operate",2)
				patchInfoAttachmentJsonTemp.put("column","patch_info_id")
				patchInfoAttachmentJsonTemp.put("isPush",1)
				patchInfoAttachmentJsonTemp.put("isLoophole",0)
				patchInfoAttachmentJsonTemp.put("entity",patchInfo.getId())
				array.add(patchInfoAttachmentJsonTemp)
				JSONObject patchInfoAttachmentPdfJsonTemp = new JSONObject()
				patchInfoAttachmentPdfJsonTemp.put("table","patch_info_attachment_pdf")
				patchInfoAttachmentPdfJsonTemp.put("operate",2)
				patchInfoAttachmentPdfJsonTemp.put("column","patch_info_id")
				patchInfoAttachmentPdfJsonTemp.put("isPush",1)
				patchInfoAttachmentPdfJsonTemp.put("isLoophole",0)
				patchInfoAttachmentPdfJsonTemp.put("entity",patchInfo.getId())
				array.add(patchInfoAttachmentPdfJsonTemp)



				if(params.attIds){
					def attArr = params.attIds.split(";")
					attArr.each{
						def attachment = Attachment.get(it)
						println "attachment的值为：" + attachment
						JSONObject attachmentSaveJsonTemp = new JSONObject()
						JSONObject attachmentSaveJson= toJsonObjectService.toJsonObject(attachment)
						attachmentSaveJsonTemp.put("operate", 0)
						attachmentSaveJsonTemp.put("flawId", patchInfo.getFlaw().getId())
						attachmentSaveJsonTemp.put("isPush", 1)
						attachmentSaveJsonTemp.put("isLoophole", 0)
						attachmentSaveJsonTemp.put("entity", attachmentSaveJson.toString())
						array.add(attachmentSaveJsonTemp)
						/*if(attachment){
							def tempPacthInfo = PatchInfoAttachment.findByAttachment(attachment)
							if(!tempPacthInfo){*/
								def patchInfoAttachment = new DisposalInfoAttachment()
								patchInfoAttachment.disposalInfo = patchInfo
								patchInfoAttachment.attachment = attachment
								println "patchInfoAttachment的值为："+patchInfoAttachment
								if(patchInfoAttachment.save(flush:true)) {
									JSONObject patchInfoAttachmentSaveJsonTemp = new JSONObject()
									JSONObject patchInfoAttachmentSaveJson= toJsonObjectService.toJsonObject(patchInfoAttachment)
									patchInfoAttachmentSaveJsonTemp.put("operate", 0)
									patchInfoAttachmentSaveJsonTemp.put("flawId", patchInfo.getFlaw().getId())
									patchInfoAttachmentSaveJsonTemp.put("isPush", 1)
									patchInfoAttachmentSaveJsonTemp.put("isLoophole", 0)
									patchInfoAttachmentSaveJsonTemp.put("entity", patchInfoAttachmentSaveJson.toString())
									array.add(patchInfoAttachmentSaveJsonTemp)


								}
								def fileName=attachment.getFileName()
								def realName=DateUtils.getCurrentTime()
								def attachmentPdf = new AttachmentPdf()
								//生成pdf附件
								def fileNameSuffix=fileName.substring(fileName.lastIndexOf("."))
								//如果上传的附件是word格式
								if (fileNameSuffix.equals(".doc")||fileNameSuffix.endsWith(".docx")){
									def newFileName=fileName.substring(0, fileName.lastIndexOf("."))

									def result = OfficeToPDF.office2PDF(root+attachment.getPath(), root+filePath+realName+".pdf")
									//操作成功与否的提示信息. 如果返回 -1, 表示找不到源文件, 或url.properties配置错误; 如果返回 0,则表示操作成功; 返回1, 则表示转换失败; 返回2, 则表示打水印失败
									if (result==-1){
										println("PDF生成找不到源文件")
										return false
									}else if (result==1){
										println("PDF生成转换失败")
										return false
									}else if (result==2){
										println("PDF生成打水印失败")
										return false
									}else if (result==0){
										File pdfFile = new File(root+filePath+realName+"_pdf.pdf")
										//pdf附件保存
										attachmentPdf.realName=realName+"_pdf.pdf"
										attachmentPdf.fileName=newFileName+"_pdf.pdf"
										attachmentPdf.fileType="application/pdf"
										attachmentPdf.fileSize=pdfFile.length()
										attachmentPdf.path=filePath+realName+"_pdf.pdf"
										attachmentPdf.attachmentId=attachment.id
										if(attachmentPdf.save(flush:true)) {
											JSONObject attachmentPdfSaveJsonTemp = new JSONObject()
											JSONObject attachmentPdfSaveJson= toJsonObjectService.toJsonObject(attachmentPdf)
											attachmentPdfSaveJsonTemp.put("operate", 0)
											attachmentPdfSaveJsonTemp.put("flawId", patchInfo.getFlaw().getId())
											attachmentPdfSaveJsonTemp.put("isPush", 1)
											attachmentPdfSaveJsonTemp.put("isLoophole", 0)
											attachmentPdfSaveJsonTemp.put("entity", attachmentPdfSaveJson.toString())
											array.add(attachmentPdfSaveJsonTemp)
										}

										def patchInfoAttachmentPdf = new DisposalInfoAttachmentPdf()
										patchInfoAttachmentPdf.disposalInfo = patchInfo
										patchInfoAttachmentPdf.attachmentPdf = attachmentPdf
										if(patchInfoAttachmentPdf.save(flush: true)) {
											JSONObject patchInfoAttachmentPdfSaveJsonTemp = new JSONObject()
											JSONObject patchInfoAttachmentPdfSaveJson= toJsonObjectService.toJsonObject(patchInfoAttachmentPdf)
											patchInfoAttachmentPdfSaveJsonTemp.put("operate", 0)
											patchInfoAttachmentPdfSaveJsonTemp.put("flawId", patchInfo.getFlaw().getId())
											patchInfoAttachmentPdfSaveJsonTemp.put("isPush", 1)
											patchInfoAttachmentPdfSaveJsonTemp.put("isLoophole", 0)
											patchInfoAttachmentPdfSaveJsonTemp.put("entity", patchInfoAttachmentPdfSaveJson.toString())
											array.add(patchInfoAttachmentPdfSaveJsonTemp)
										}
									}
								}


						/*	}
						}*/
					}
				}
			}
			taskInstance.disposalInfo=patchInfo
			//增加处置信息-end
			if(taskInstance.save()) {
				JSONObject taskInstanceJsonTemp = new JSONObject()
				JSONObject taskInstanceJson= toJsonObjectService.toJsonObject(taskInstance)
				taskInstanceJsonTemp.put("operate", 1)
				taskInstanceJsonTemp.put("flawId", taskInstance.getFlaw().getId())
				taskInstanceJsonTemp.put("isPush", 1)
				taskInstanceJsonTemp.put("isLoophole", 0)
				taskInstanceJsonTemp.put("entity", taskInstanceJson.toString())
				array.add(taskInstanceJsonTemp)
			}

			def flaw = taskInstance.flaw
			if ( flaw.push== 1) {
				jsonImp.put("date",array.toString())

				for(int i = 0; i < kafkaCount; i++){
					def kafkaTopic = properties.getProperty("kafka.topic"+i)
					try{
						println "json|"+jsonImp.toString()
						new SendMsgProducer(jsonImp,kafkaIp,kafkaTopic).start()
					}catch (Exception e) {
						def synchroErrData = new SynchroErrData();
						synchroErrData.errorData=jsonImp.toString();
						synchroErrData.errorReason=e.message.toString();
						synchroErrData.status=0;
						synchroErrData.topic=kafkaTopic;
						synchroErrData.save(flush: true)
					}
				}
			}
		}
		return true
	}
	/*def checkFile(def file){
		def contentType=file.getContentType()
		def  fileName=file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
		println contentType
		if(!"application/octet-stream".equals(contentType)){
			return false
		}
		println fileName
		if(fileName!='.doc' && fileName!=".docx"){
			return false
		}
		return true
	}*/

	def checkFile(def file){
		def fileName = file.getOriginalFilename()
		println "fileName="+fileName
		println file.getContentType()
		def fileNameSuffix=fileName.substring(fileName.lastIndexOf("."))
		println "fileNameSuffix="+fileNameSuffix

		InputStream is = new ByteArrayInputStream(file.getBytes())
		def filetype = FileTypeUtil.getFileByFile(is)
		println "filetype="+filetype

		if(file.size<=10*1024*1024){
			println "文件的长度："+ file.size
			if((fileNameSuffix.equals(".doc")||fileNameSuffix.equals(".docx")) && ("application/msword".equals(filetype)||"application/zip".equals(filetype))){
				return true
			}else{
				return false
			}
		}else{
			return false
		}
	}
}
