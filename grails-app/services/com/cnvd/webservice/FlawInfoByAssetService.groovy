package com.cnvd.webservice

import java.security.cert.X509Certificate

import javax.servlet.http.HttpServletRequest

import com.cnvd.TUser
import com.cnvd.asset.AssetInfo
import com.cnvd.asset.AssetTask
import com.cnvd.cer.CA
import com.cnvd.flawInfo.FlawInfo
import com.cnvd.utils.WebServiceUtil
import com.mysql.jdbc.exceptions.MySQLSyntaxErrorException

class FlawInfoByAssetService {
	def dataSource
	static expose = ["xfire"]
	static transactional = true

	/**
	 * 获取任务id
	 * 判断证书
	 */
	String getTaskIdByAssetInfo(AssetInfo[] assetList,Date startDate,Date endDate){
		for(AssetInfo asset : assetList){
			println "asset.equipmentId="+asset.equipmentId
			println "asset.manufacturerName="+asset.manufacturerName
			println "asset.productCategoryName="+asset.productCategoryName
			println "asset.edition="+asset.edition
		}
		
		if(!endDate){
			endDate = new Date()
		}
		HttpServletRequest request = WebServiceUtil.getRequest();
		//获得请求的证书,然后按照不同的证书获得不同的内容
		X509Certificate[] certs = (X509Certificate[])request.getAttribute("javax.servlet.request.X509Certificate");
		println "certs="+certs
		println "getTaskIdByAssetInfo"
		if (certs != null && WebServiceUtil.verifyCertificate(certs[0])) {
			def cnStr = WebServiceUtil.getSubjectName("CN",certs[0])  //由证书中获得CN值
			println "cnStr="+cnStr
			def caInstance = CA.findByCnStr(cnStr)
			println "caInstance="+caInstance
			if(caInstance && caInstance.accessInterface.equals("flawInfoByAsset")){
				/**
				 * 新建AssetTask类 ,保存任务的一些状态
				 */
				def assetTaskInstance = new AssetTask()
				assetTaskInstance.user = caInstance.tuser
				assetTaskInstance.status = 0
				assetTaskInstance.startDate = startDate
				assetTaskInstance.endDate = endDate
				assetTaskInstance.ca = caInstance
				if(assetTaskInstance.save(flush:true)){
					def taskId = assetTaskInstance.id
					//将assetList保存到AssetInfo表中
					/*def insertSql = "insert into asset_info (version,equipment_id,equipment_name,equipment_model,"+
						"equipment_manu,equipment_type,os_name,os_version,manufacturer_name,"+
						"product_category_name,edition,task_id) values "
					for(AssetInfo asset : assetList){
						String equipmentId = asset.equipmentId?"'"+asset.equipmentId+"'":null
						String equipmentName = asset.equipmentName?"'"+asset.equipmentName+"'":null
						String equipmentModel = asset.equipmentModel?"'"+asset.equipmentModel+"'":null
						String equipmentManu = asset.equipmentManu?"'"+asset.equipmentManu+"'":null
						String equipmentType = asset.equipmentType?"'"+asset.equipmentType+"'":null
						String osName = asset.osName?"'"+asset.osName+"'":null
						String osVersion = asset.osVersion?"'"+asset.osVersion+"'":null
						String manufacturerName = asset.manufacturerName?"'"+asset.manufacturerName+"'":null
						String productCategoryName = asset.productCategoryName?"'"+asset.productCategoryName+"'":null
						String edition = asset.edition?"'"+asset.edition+"'":null
						
						insertSql += "(0,"+equipmentId+","+equipmentName+","+equipmentModel+","+
							equipmentManu+","+equipmentType+","+osName+","+osVersion+","+manufacturerName+","+
							productCategoryName+","+edition+","+taskId+"),"
					}
					insertSql = insertSql.substring(0,insertSql.length()-1)
					println "insertSql="+insertSql
					
					try{
						new groovy.sql.Sql(dataSource).execute(insertSql)
					}catch(MySQLSyntaxErrorException e){
						FileOutputStream fos = new FileOutputStream("d:/cnvd/logs/mysql.log",true);
						Writer out = new OutputStreamWriter(fos, "UTF-8");
						out.write(new Date()+"   "+taskId+"   "+insertSql+"\n");
						out.close();
						return "软件名异常，插入数据库错误"
					}*/
					def insertStart = System.currentTimeMillis()
					for(AssetInfo asset : assetList){
						asset.taskId = taskId
						if(!asset.save(flush:true)){
							asset.errors.allErrors.each{
								println it
							}
							FileOutputStream fos = new FileOutputStream("/cnvd/logs/mysql.log",true);
							Writer out = new OutputStreamWriter(fos, "UTF-8");
							out.write(new Date().toString()+"   "+taskId+"   "+asset.manufacturerName?asset.manufacturerName:''+"   "+asset.productCategoryName+"	"+asset.edition+"\n");
							out.close();
						}
					}
					println "insert cost time = "+(System.currentTimeMillis() - insertStart)
					return taskId.toString()
				}
			}else {
				return "证书错误"
			}
		}else{
			return "证书错误";
		}
	}
	
	/**
	 * 查询任务状态
	 * 判断证书
	 * 返回任务是否已处理完毕
	 */
	String getTaskStatus(String taskId){
		def assetTaskInstance = AssetTask.get(taskId)
		if(assetTaskInstance){
			HttpServletRequest request = WebServiceUtil.getRequest();
			//获得请求的证书,然后按照不同的证书获得不同的内容
			X509Certificate[] certs = (X509Certificate[])request.getAttribute("javax.servlet.request.X509Certificate");
			println "certs="+certs
			if (certs != null && WebServiceUtil.verifyCertificate(certs[0])) {
				def cnStr = WebServiceUtil.getSubjectName("CN",certs[0])  //由证书中获得CN值
				println "cnStr="+cnStr
				println "getTaskStatus"
				def caInstance = CA.findByCnStr(cnStr)
				println "caInstance="+caInstance
				if(caInstance && caInstance.accessInterface.equals("flawInfoByAsset")
					&& (caInstance.tuser.id == assetTaskInstance.user.id)){
					return assetTaskInstance.getStatusStr()
				}else{
					return "证书错误"
				}
			}else{
				return "证书错误"
			}
		}else{
			return "查询任务id有误"
		}
	}
	
	/**
	 * 查询漏洞结果
	 */
	FlawInfo[] getFlawInfoByTaskId(String taskId){
		def flawList = new ArrayList<FlawInfo>()
		def assetTaskInstance = AssetTask.get(taskId)
		if(assetTaskInstance && assetTaskInstance.status ==2){
			HttpServletRequest request = WebServiceUtil.getRequest();
			//获得请求的证书,然后按照不同的证书获得不同的内容
			X509Certificate[] certs = (X509Certificate[])request.getAttribute("javax.servlet.request.X509Certificate");
			println "certs="+certs
			if (certs != null && WebServiceUtil.verifyCertificate(certs[0])) {
				def cnStr = WebServiceUtil.getSubjectName("CN",certs[0])  //TODO 由证书中获得CN值
				println "cnStr="+cnStr
				println "getFlawInfoByTaskId"
				def caInstance = CA.findByCnStr(cnStr)
				println "caInstance="+caInstance
				if(caInstance && caInstance.accessInterface.equals("flawInfoByAsset")){
					//判断查询人与任务发起人是否为同一个人
					if(assetTaskInstance.user.id == caInstance.tuser.id){
						flawList = FlawInfo.findAllByTaskId(taskId)
						return flawList
					}
				}
			}else{
				return flawList
			}
		}else{
			return flawList
		}
	}
}