package com.cnvd.webservice

import com.cnvd.api.SyncFlawInfoUtil
import java.security.cert.X509Certificate
import javax.servlet.http.HttpServletRequest
import org.codehaus.xfire.transport.http.XFireServletController

class SyncFlawInfoService {

	static transactional = true
	static expose = ["xfire"]

	def getFlawInfo(String startTime, String endTime){

		//获得请求的证书,然后按照不同的证书获得不同的内容
		HttpServletRequest request = XFireServletController.getRequest();
		X509Certificate[] certs = (X509Certificate[]) request.getAttribute("javax.servlet.request.X509Certificate");

		if (certs != null && this.verifyCertificate(certs[0])) {

			//TODO 加token
			println "startTime="+startTime+" endTime="+endTime
			SyncFlawInfoUtil flawInfoUtil = new SyncFlawInfoUtil();
			String str = flawInfoUtil.getSyncStr(startTime, endTime);
			return str
			//			render(text:str,contentType:"text/xml",encoding:"UTF-8");

		} else {
			if ("https".equalsIgnoreCase(request.getScheme())) {
				return "这是一个HTTPS请求，但是没有可用的客户端证书";
			} else {
				return "这不是一个HTTPS请求，因此无法获得客户端证书列表 ";
			}
		}
	}

	/**
	 * 验证证书是否有效
	 * @param certificate
	 * @return
	 */
	public static boolean verifyCertificate(X509Certificate certificate){
		boolean valid = true;
		try {
			certificate.checkValidity();
		} catch (Exception e) {
			e.printStackTrace();
			valid = false;
		}
		return valid;
	}

	def serviceMethod() {

	}
}
