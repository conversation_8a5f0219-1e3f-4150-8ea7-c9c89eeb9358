package com.cnvd.webservice

import com.cnvd.utils.IPUtil

import java.security.cert.X509Certificate
import java.text.SimpleDateFormat

import javax.jws.WebMethod
import javax.servlet.http.HttpServletRequest

import com.cnvd.cer.CA
import com.cnvd.utils.WebServiceUtil

class FlawInfoService {

	//def cerVerifyService
	static expose = ["xfire"]
	
	def getFlawInfo(String startDateStr,String endDateStr){
		System.out.println("startDateStr="+startDateStr);
		System.out.println("endDateStr="+endDateStr);
		
		// 日期转换异常处理
		def format = new SimpleDateFormat("yyyy-MM-dd")
		def startDate = null
		try{
			startDate = format.parse(startDateStr)
		}catch(Exception e){
			return "开始时间格式化错误"
		}
		def endDate = null
		try{
			endDate = endDateStr?format.parse(endDateStr):new Date()
		}catch(Exception e){
			return "结束时间格式化错误"
		}
		
		//获得请求的证书,然后按照不同的证书获得不同的内容
		HttpServletRequest request = WebServiceUtil.getRequest()
		X509Certificate[] certs = (X509Certificate[]) request.getAttribute("javax.servlet.request.X509Certificate");
		
		if (certs != null && WebServiceUtil.verifyCertificate(certs[0])) {
			def CN = WebServiceUtil.getPrincipalName("CN",certs[0])
			CN = WebServiceUtil.getSubjectName("CN",certs[0])
			def caInstance = CA.findByCnStr(CN)
			def userIP = IPUtil.getIpAddress(request)
			if(caInstance && caInstance.accessInterface.equals("flawInfo")){
				String userIPWhiteList = caInstance.ipWhiteList
				if(userIPWhiteList == null || "".equals(userIPWhiteList)){
					caInstance.ipWhiteList = userIP
					if(!caInstance.save(flush: true)){
						return "IP"+userIP+"不在白名单内"
					}
				}else{
					String[] ipArray = userIPWhiteList.split(",")
					println("===ipArray" + ipArray)
					if(!Arrays.asList(ipArray).contains(userIP)){
						return "IP"+userIP+"不在白名单内"
					}
				}
				def caRoleInstance = caInstance.caRole
				println "caRoleInstance="+caRoleInstance
				println "field="+caRoleInstance.field
				println "CNStr="+caInstance.cnStr
				// 根据不同的证书获取相应漏洞字段信息
				
				return WebServiceUtil.queryFlawInfo2(new ArrayList(),caInstance,startDate,endDate)
				
			}else{
				return "证书错误";
			}
		} else {
			if ("https".equalsIgnoreCase(request.getScheme())) {
				return "这是一个HTTPS请求，但是没有可用的客户端证书";
			} else {
				return "这不是一个HTTPS请求，因此无法获得客户端证书列表 ";
			}
		}
	}
}
