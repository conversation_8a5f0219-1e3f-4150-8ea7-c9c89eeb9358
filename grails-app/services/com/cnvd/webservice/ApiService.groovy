package com.cnvd.webservice

import com.cnvd.info.Webinfo
import grails.converters.JSON
import java.security.cert.X509Certificate
import java.text.SimpleDateFormat
import javax.servlet.http.HttpServletRequest
import org.codehaus.xfire.transport.http.XFireServletController

class ApiService {

    static transactional = true
	static expose = ["xfire"]
	
	
	def getSync(String d){

		long date = Long.parseLong(d)
		//获得请求的证书,然后按照不同的证书获得不同的内容
		HttpServletRequest request = XFireServletController.getRequest();
		X509Certificate[] certs = (X509Certificate[]) request.getAttribute("javax.servlet.request.X509Certificate");

		if (certs != null && this.verifyCertificate(certs[0])) {
			def releaseTime = new Date(date)
			def webInfoCriteria = Webinfo.createCriteria()
			def webinfoInstanceList = webInfoCriteria.list {
				and {
					'in'('type',[1, 2, 14])
					eq("status",1)
					gt('releaseTime',releaseTime)
				}
			}
			def result = new ArrayList<Map<String,Object>>()
			for (Webinfo webinfo : webinfoInstanceList) {
				def resultMap = new HashMap<String,Object>()
				resultMap.put('id', webinfo.id)
				resultMap.put('title', webinfo.title)
				resultMap.put('content', webinfo.content)
				resultMap.put('type', webinfo.type)
				result.add(resultMap)
			}
			def resjson = result as JSON
			return resjson.toString()

		} else {
			if ("https".equalsIgnoreCase(request.getScheme())) {
				return "这是一个HTTPS请求，但是没有可用的客户端证书";
			} else {
				return "这不是一个HTTPS请求，因此无法获得客户端证书列表 ";
			}
		}
	}

	/**
	 * 验证证书是否有效
	 * @param certificate
	 * @return
	 */
	public static boolean verifyCertificate(X509Certificate certificate){
		boolean valid = true;
		try {
			certificate.checkValidity();
		} catch (Exception e) {
			e.printStackTrace();
			valid = false;
		}
		return valid;
	}
	
}
