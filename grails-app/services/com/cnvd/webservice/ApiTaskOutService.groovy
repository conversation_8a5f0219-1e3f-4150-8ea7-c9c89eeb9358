package com.cnvd.webservice

import java.security.cert.X509Certificate
import java.text.SimpleDateFormat
import javax.servlet.http.HttpServletRequest

import com.cnvd.utils.WebServiceUtil

import java.util.regex.Matcher
import java.util.regex.Pattern

import com.cnvd.utils.SQLUtil
import com.cnvd.utils.Aes;
import com.cnvd.ApiTaskLog
import com.cnvd.common.Task
import grails.converters.JSON

import org.apache.commons.collections.map.HashedMap

class ApiTaskOutService {

	//def cerVerifyService
	static expose = ["xfire"]

	/*def getApiTask(String date,String thekey){
		System.out.println("startDateStr="+date);
		println "这家公司的key值为"+thekey

		//获得请求的证书,然后按照不同的证书获得不同的内容
		HttpServletRequest request = WebServiceUtil.getRequest()
		X509Certificate[] certs = (X509Certificate[]) request.getAttribute("javax.servlet.request.X509Certificate");

		if (certs != null && WebServiceUtil.verifyCertificate(certs[0])) {

			List<Map<String, String>> maps = new ArrayList<Map<String, String>>();
			def keyi = thekey;
			def createdate = date;
			def keysub = keyi.substring(0, 32);

			def ApiTaskLogInstance = ApiTaskLog.findByKeyNumber(keyi)

			if(!ApiTaskLogInstance){	//判断接口记录中是否有该Key，没有则不允许调用
				Map<String, String> map = new HashedMap();
				map.put("error","params is error！");
				maps.add(map);
			}else if(createdate == null){  //校验date是否为空
				Map<String, String> map = new HashedMap();
				map.put("error","params is error！");
				maps.add(map);
			}else if(keyi == null){	 //校验key是否为空
				Map<String, String> map = new HashedMap();
				map.put("error","params is error！");
				maps.add(map);
			}else{
				try{
					//年月日验证规则
					String regEx = /^((((19|20)\d{2})-(0?[13-9]|1[012])-(0?[1-9]|[12]\d|30))|(((19|20)\d{2})-(0?[13578]|1[02])-31)|(((19|20)\d{2})-0?2-(0?[1-9]|1\d|2[0-8]))|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))-0?2-29))$/
					// 编译正则表达式
					Pattern pattern = Pattern.compile(regEx);
					Matcher matcher = pattern.matcher(createdate);
					boolean flag = matcher.matches();
					if(!flag){
						Map<String, String> map = new HashedMap();
						map.put("error","params is error！");
						maps.add(map);
						//							render maps as JSON
						//							return
						return maps as JSON
					}

					def key = Aes.decode(keysub);
					def intKey = Integer.valueOf(key);

					if(intKey instanceof Integer){

					String sql = "SELECT * FROM task t WHERE t.targettuser_id = '"+key+"' AND t.date_created LIKE '"+createdate+"%'";
					def apiInstanceList = SQLUtil.getList(sql);

					//判断是否为中国电信集团，如果是则增加输出属性
					if(intKey!=18262){
						for(int i = 0; i  < apiInstanceList.size() ; i++){
							int  yy = apiInstanceList.size();
							Map<String, String> map = new HashedMap();
							def id_task = apiInstanceList[i].id;
							def title = apiInstanceList[i].title;
							//时间
							def dateCreated = apiInstanceList[i].date_created;
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							String fdate = sdf.format(dateCreated);
							map.put("title",title);
							map.put("info", apiInstanceList[i].info);
							map.put("dateCreated", fdate);
							map.put("finishDate","48h");
							//
							def attid=0;
							def flawId = apiInstanceList[i].flaw_id;
							//def _val="http://localhost:8080/cnvd/apiTaskOut/download?id="
							def _val="http://www.cnvd.org.cn/apiTaskOut/download?id="
							if(flawId!=null){
								String sqlStr = "SELECT * FROM flaw t WHERE t.id = '"+flawId+"';";
								def flawInstance = SQLUtil.getList(sqlStr);

								attid=flawInstance[0].attachment_id
								if(attid!=null){
									_val+=Aes.encode(attid+"")
								}else{
									_val="无附件"
								}
							}else{
								_val="无附件"
							}
							map.put("downCode", _val);
							maps.add(map);
						}
					}else{
						//电信集团输出  2021-09-12  注释
						*//*for(int i = 0; i  < apiInstanceList.size() ; i++){
							int  yy = apiInstanceList.size();
							Map<String, String> map = new HashedMap();
							def id_task = apiInstanceList[i].id;
							def title = apiInstanceList[i].title;
							//时间
							def dateCreated = apiInstanceList[i].date_created;
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							String fdate = sdf.format(dateCreated);
							map.put("title",title);
							map.put("info", apiInstanceList[i].info);
							map.put("dateCreated", fdate);
							map.put("finishDate","48h");
							def attid=0;
							def flawId = apiInstanceList[i].flaw_id;
							//def _val="http://localhost:8080/cnvd/apiTaskOut/download?id="
							def _val="http://www.cnvd.org.cn/apiTaskOut/download?id="
							if(flawId!=null){
								String sqlStr = "SELECT * FROM flaw t WHERE t.id = '"+flawId+"';";
								def flawInstance = SQLUtil.getList(sqlStr);

								attid=flawInstance[0].attachment_id
								if(attid!=null){
									_val+=Aes.encode(attid+"")
								}else{
									_val="无附件"
								}
							}else{
								_val="无附件"
							}
							map.put("downCode", _val);
							//新增属性
							def taskInstance = Task.get(id_task.toInteger());
							//漏洞名称
							map.put("vulnerabillityName", taskInstance.flaw.title);
							//漏洞来源
							if(taskInstance.flaw.user.userName){
								map.put("vulnerabillitySource", taskInstance.flaw.user.userName);
							}else{
								map.put("vulnerabillitySource", taskInstance.flaw.user.nickName);
							}
							
							//漏洞编号
							if(taskInstance.flaw.number){
								map.put("CNVD", taskInstance.flaw.number);
							}else{
								map.put("CNVD", taskInstance.flaw.tempNumber);
							}
							//漏洞附件名称
							if(taskInstance.flaw.attachment.realName){
								map.put("vulnerabillityFileName", taskInstance.flaw.attachment.realName);
							}else{
								map.put("vulnerabillityFileName", "无附件");
							}
							
							maps.add(map);
						}*//*
					}

				}else{
						Map<String, String> map = new HashedMap();
						map.put("error","params is error！");
						maps.add(map);
					}
				}catch(Exception e){
					Map<String, String> map = new HashedMap();
					map.put("error","params is error！");
					maps.add(map);
				}finally{
				}
			}
			def mapsjson = maps as JSON
			return mapsjson.toString()

		} else {
			if ("https".equalsIgnoreCase(request.getScheme())) {
				return "这是一个HTTPS请求，但是没有可用的客户端证书";

			} else {
				return "这不是一个HTTPS请求，因此无法获得客户端证书列表 ";
			}
		}
	}*/
}
