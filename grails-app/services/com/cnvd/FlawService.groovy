package com.cnvd

import com.alibaba.fastjson.JSON
import com.cnvd.utils.DateUtil
import com.cnvd.utils.DateUtils
import com.cnvd.utils.HttpUtil
import com.cnvd.utils.RedisClusterTemplate
import com.cnvd.utils.SQLUtil
import com.cnvd.vo.Response
import org.apache.commons.lang.StringUtils
import org.springframework.core.io.ClassPathResource

import javax.servlet.http.HttpServletRequest
import java.text.ParseException
import java.text.SimpleDateFormat

class FlawService {

    static transactional = true
    def properties = org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(new ClassPathResource("app-config.properties"))
    def flawUrlQueryInterface = properties.getProperty("flaw.url.query.interface")
    def flawUrlSaveInterface = properties.getProperty("flaw.url.save.interface")


    /**
     * 获取list集合
     *
     * <AUTHOR>
     * @date :2020-03-25 15:37:09
     */
    def getList(def params,def actionType) {
        //List<Map<String, Object>> resultList = getSelectResult(params,actionType,0)
        List<Map<String, Object>> resultList = getQuerySelectResult(params,actionType,0)
        return resultList;
    }
    //返回访问IP地址
    def getIpAddr(HttpServletRequest request) {
        return request.getHeader("X-Real-IP");
        /*String ip = request.getHeader("x-forwarded-for");
        if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        System.out.println("ip1="+request.getHeader("x-forwarded-for")+"|ip2="+request.getHeader("WL-Proxy-Client-IP")+"|ip3="+request.getHeader("WL-Proxy-Client-IP")+"|ip4="+request.getRemoteAddr());
        return ip;*/
    }

    //防爬虫的方法，peiguanghao 2022.01.25
    def reptile(String ip,String url){
        String logDate=DateUtils.parseToString(new Date());
        RedisClusterTemplate r = new RedisClusterTemplate();
        //获取初始化的阈值
        Map<String,String> map = r.hgetAll("properties");
        if(map.size() == 0){//如果获取不到初始化参数，那就读取配置文件中并设置
            map.put("session.number", properties.getProperty("session.number"));
            map.put("session.date", properties.getProperty("session.date"));
            map.put("temp.index", properties.getProperty("temp.index"));
            map.put("temp.ip.date", properties.getProperty("temp.ip.date"));
            r.hmset("properties",map);
        }
        log.info(logDate+"|webCrawler|url="+url+"|ip="+ip+"|session.number="+map.get("session.number")+"|session.date="+map.get("session.date")+"|temp.index="+map.get("temp.index")+"|temp.ip.date="+map.get("temp.ip.date"));
        boolean bool = r.exists(ip);//判断临时黑名单
        if(bool){//此IP存在临时黑名单，返回false
            log.info(logDate+"|webCrawler|url="+url+"|ip="+ip+"|is tempblacklist");
            return false;
        }else {
            //判断永久黑名单
            bool = r.sismember("blacklist",ip);
            if(bool){
                log.info(logDate+"|webCrawler|url="+url+"|ip="+ip+"|is blacklist");
                return false;
            }else{
                //判断是否创建访问会话
                String value = r.get(ip+",session");
                String endDate = DateUtils.parseToString(new Date());//当前时间
                log.info(logDate+"webCrawler|ip="+ip+"|session="+value);
                if(StringUtils.isBlank(value) || 'null'.equals(value)){//没有访问会话
                    //创建会话，记录次数+1,有效期20分钟。
                    r.expire(ip+",session","1,"+endDate,"","",Integer.parseInt(map.get("session.date")));//添加
                    log.info(logDate+"|webCrawler|url="+url+"|ip="+ip+"|add session");
                    return true;
                }else{
                    String[] strs = value.split(",");
                    Integer num = Integer.parseInt(strs[0]);
                    //判断是否超出会话次数
                    if(num >= Integer.parseInt(map.get("session.number"))){//超出，加入临时黑名单或永久黑名单
                        //判断临时黑名单次数，如果等于3次那么此ip移动永久黑名单
                        long index = r.hincrBy("tmep_blacklist_num",ip);
                        log.info(logDate+"|webCrawler|url="+url+"|ip="+ip+"|index="+index);
                        if(index >= Integer.parseInt(map.get("temp.index"))){
                            //加入永久黑名单
                            r.hmset("blacklist", ip);
                            log.info(logDate+"|webCrawler|url="+url+"|ip="+ip+"|add blacklist");
                            //删除此ip的临时会话次数
                            r.hashHdel("tmep_blacklist_num",ip);
                        }else{
                            //加入临时黑名单
                            r.expire(ip,ip,"","",Integer.parseInt(map.get("temp.ip.date")));
                            //临时黑名单次数+1
                            r.hashIncrease("tmep_blacklist_num",ip,1l,-1);
                            log.info(logDate+"|webCrawler|url="+url+"|ip="+ip+"|add tmep_blacklist");
                            index = r.hincrBy("tmep_blacklist_num",ip);
                            log.info(logDate+"|webCrawler|url="+url+"|ip="+ip+"|index="+index+"|judge tmep_blacklist_num");
                            if(index >= Integer.parseInt(map.get("temp.index"))){//如果第3次了，就加入永久黑名单
                                //加入永久黑名单
                                r.hmset("blacklist", ip);
                                log.info(logDate+"|webCrawler|url="+url+"|ip="+ip+"|add blacklist");
                                //删除此ip的临时会话次数
                                r.hashHdel("tmep_blacklist_num",ip);
                            }
                        }
                        return false;
                    }else{//没超出
                        Integer tempNum=num+1;
                        long sec = DateUtils.diff(strs[1],endDate);//订算相关秒数，为了设置会话有效期
                        Integer surplusSec=Integer.parseInt(map.get("session.date"))-Integer.parseInt(String.valueOf(sec));
                        log.info(logDate+"|webCrawler|url="+url+"|ip="+ip+"|sessionNum="+tempNum+"|sessionStartDate="+strs[1]+"|endDate"+endDate+"|surplusSec="+surplusSec);
                        if(surplusSec < 1){//如果相关秒数据小于1，说明已经过期了，那就删除会话
                            r.del(ip+",session");
                        }else{
                            r.del(ip+",session");//先删除在添加
                            r.expire(ip+",session",String.valueOf(tempNum)+","+strs[1],"","",surplusSec);//添加
                        }
                        return true;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 获取总记录数
     *
     * <AUTHOR>
     * @date :2020-03-25 15:37:09
     */
    def getCount(def params,def actionType) {
        //List<Map<String, Object>> resultCount = getSelectResult(params,actionType,1)
        List<Map<String, Object>> resultCount = getQuerySelectResult(params,actionType,1)
        Long flawInstanceTotal = Long.valueOf(0)
        if (resultCount != null && resultCount.size() > 0) {
            flawInstanceTotal = Long.valueOf(resultCount.get(0).get("countAs").toString())
        }
        return flawInstanceTotal
    }

    /**
     *  获取漏洞处理数据记录数
     * @param params
     * @param actionType
     */
    def  getFlawCount(def params,def actionType){

        Map<String,Integer> flawDataMap = new HashMap();
        if (!params.keyword){
            return flawDataMap;
        }
        flawDataMap.put("title",params.keyword)
        //查询总数
        StringBuilder sql = new StringBuilder() //查询sql
        List<Object> paramList = new ArrayList() //参数集合
        sql.append("select f.title,f.is_open,f.isp,f.status, f.date_created  dateCreated from flaw  f  LEFT JOIN  detailed_info dInfo ON f.detailed_info_id = dInfo.id   where status = 9  AND is_open = 1")
        if(params.keyword){
            def kwd = URLDecoder.decode(params.keyword,"UTF-8")
            if("0".equals(params.keywordFlag)){
                if("1".equals(params.condition)){
                    sql.append(" AND f.title LIKE ?")
                    paramList.add("%\\" + kwd.trim() + "%")
                }
                if("0".equals(params.condition)){
                    sql.append(" AND (f.title LIKE ? OR dInfo.description LIKE ?)")
                    paramList.add("%\\" + kwd.trim() + "%")
                    paramList.add("%\\" + kwd.trim() + "%")
                }
            }else if("1".equals(params.keywordFlag)){
                if("1".equals(params.condition)){
                    sql.append(" OR f.title LIKE ?")
                    paramList.add("%\\" + kwd.trim() + "%")
                }
                if("0".equals.(params.condition)){
                    sql.append(" OR (f.title LIKE ? OR dInfo.description LIKE ?)")
                    paramList.add("%\\" + kwd.trim() + "%")
                    paramList.add("%\\" + kwd.trim() + "%")
                }
            }else{
                sql.append(" AND f.title LIKE ?")
                paramList.add("%\\" + kwd.trim() + "%")
            }
        }
        //开始时间
        if (params.startDate) {
            sql.append(" AND f.date_created >= ?")
            paramList.add(params.startDate.trim())
        }
        //结束时间
        if (params.endDate) {
            sql.append(" AND f.date_created < ?")
            paramList.add(DateUtil.getTomorrowStr(params.endDate.trim()))
        }
        String newSql = sql.toString()
        List<Map<String, Object>> dataResult = SQLUtil.getResult(newSql, paramList)

        if (dataResult != null && dataResult.size() > 0) {
            for(Map<String,Object> map: dataResult){
                if ( map.get("isp") == 1){
                    //已修复（已公开-有补丁、未公开-有补丁）
                    flawDataMap.put("isP",flawDataMap.get("isP") == null ? 1:flawDataMap.get("isP")+1);
                }
                if ( map.get("isp") == 0 && map.get("is_open") == 1){
                    //已修复（（没有补丁且公开））
                    Object dateObj = map.get("dateCreated");
                    if (dateObj != null && !"".equals(dateObj.toString())){
                        String timeString = dateObj.toString();
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        try {
                            Date date = dateFormat.parse(timeString);
                            //2021年以前的直接加进去
                            if (date.getTime() <=1609430400){
                                //已修复
                                flawDataMap.put("isP",flawDataMap.get("isP") == null ? 1:flawDataMap.get("isP")+1);
                            }

                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                    }
                }
//                if (map.get("isp") == 0 && map.get("is_open") == 1){
//                    //未修复（没有补丁且公开）
//                    flawDataMap.put("noP",flawDataMap.get("noP") == null ? 1:flawDataMap.get("noP")+1);
//                }
                if (map.get("isp") == 0 && map.get("is_open") == 0){
                    //处理中（没有补丁且未公开）
                    flawDataMap.put("process",flawDataMap.get("process") == null ? 1:flawDataMap.get("process")+1);
                }
                    //总数
                flawDataMap.put("archive",flawDataMap.get("archive") == null ? 1:flawDataMap.get("archive")+1);
            }
        }
        //填补0
        if (flawDataMap.get("isP") == null){
            flawDataMap.put("isP",0)
        }
        if (flawDataMap.get("noP") == null){
            flawDataMap.put("noP",0)
        }
        if (flawDataMap.get("process") == null){
            flawDataMap.put("process",0)
        }
        if (flawDataMap.get("archive") == null){
            flawDataMap.put("archive",0)
        }
        return flawDataMap

    }

    def getSelectResult(def params,def actionType,Integer type) {
        StringBuilder sql = new StringBuilder() //查询sql
        List<Object> paramList = new ArrayList() //参数集合


        sql.append(" SELECT")
                .append(" f.id,f.number,f.title,f.click_num AS clickNum,f.comment_count AS commentCount,f.concern_count AS concernCount,f.open_time AS openTime,di.value AS diValue,di.name AS diName")
                .append(" FROM flaw f LEFT JOIN  dictionary_info di ON f.serverity_id =  di.id")
                .append(getJoinSql(params))
                .append(" WHERE f.open_time<'"+ DateUtil.getTomorrowStrDefault()+"' AND f.status = 9 AND f.enable = 1 AND f.is_open=1 AND f.parent_flaw_id is null")


        //actionType  0-漏洞管理 1-一级审核 2-二级审核 3-三级审核-目前没用到

        //漏洞编号
        if (params.number && !"请输入精确编号".equals(params.number)) {
            sql.append(" AND (f.number = ? OR f.old_number = ?)")
            paramList.add(params.number.trim())
            paramList.add(params.number.trim())
        }
        //开始时间
        if (params.startDate) {
            sql.append(" AND f.open_time >= ?")
            paramList.add(params.startDate.trim())
        }
        //结束时间
        if (params.endDate) {
            sql.append(" AND f.open_time < ?")
            paramList.add(DateUtil.getTomorrowStr(params.endDate.trim()))
        }

        //参考信息
        if(params.refenceInfo){
            sql.append(" AND ri.reference_number LIKE ?")
            paramList.add("%" + params.refenceInfo.trim() + "%")
        }
        if(params.referenceScope && !"-1".equals(params.referenceScope)){
            sql.append(" AND ri.reference_type_id = ?")
            paramList.add(params.long('referenceScope'))
        }

        //厂商信息
        if(params.manufacturerId && !"-1".equals(params.manufacturerId)){
            if(params.categoryId && !"-1".equals(params.categoryId)){
                sql.append(" AND pi.name LIKE ?")
                if(params.editionId && !"-1".equals(params.editionId)) {
                    paramList.add(" CONCAT('%\\', (SELECT name FROM manufacturer WHERE id = "+params.manufacturerId+"),' ',(SELECT name FROM product_category WHERE id = "+params.categoryId+"),' "+params.editionId+"','%')")
                }else{
                    paramList.add(" CONCAT('%\\', (SELECT name FROM manufacturer WHERE id = "+params.manufacturerId+"),' ',(SELECT name FROM product_category WHERE id = "+params.categoryId+"),'%')")
                }
            }else{
                sql.append(" AND f.manufacturer_id = ?")
                paramList.add(params.long('manufacturerId'))
            }
        }

        if(params.causeIdStr){
            def ids = params.causeIdStr.trim()
            ids = ids.substring(0,ids.length()-1)
            sql.append(" AND f.cause_id IN (?)")
            paramList.add(ids)
        }
        if(params.threadIdStr){
            def ids = params.threadIdStr.trim()
            ids = ids.substring(0,ids.length()-1)
            sql.append(" AND f.thread_id IN (?)")
            paramList.add(ids)
        }
        if(params.serverityIdStr){
            def ids = params.serverityIdStr.trim()
            ids = ids.substring(0,ids.length()-1)
            sql.append(" AND f.serverity_id IN (?)")
            paramList.add(ids)
        }
        if(params.positionIdStr){
            def ids = params.positionIdStr.trim()
            ids = ids.substring(0,ids.length()-1)
            sql.append(" AND f.position_id IN (?)")
            paramList.add(ids)
        }
        //关键字
        if(params.keyword){
            def kwd = URLDecoder.decode(params.keyword,"UTF-8")
            if("0".equals(params.keywordFlag)){
                if("1".equals(params.condition)){
                    sql.append(" AND f.title LIKE ?")
                    paramList.add("%\\" + kwd.trim() + "%")
                }
                if("0".equals(params.condition)){
                    sql.append(" AND (f.title LIKE ? OR detailedInfo.description LIKE ?)")
                    paramList.add("%\\" + kwd.trim() + "%")
                    paramList.add("%\\" + kwd.trim() + "%")
                }
            }else if("1".equals(params.keywordFlag)){
                if("1".equals(params.condition)){
                    sql.append(" OR f.title LIKE ?")
                    paramList.add("%\\" + kwd.trim() + "%")
                }
                if("0".equals.(params.condition)){
                    sql.append(" OR (f.title LIKE ? OR detailedInfo.description LIKE ?)")
                    paramList.add("%\\" + kwd.trim() + "%")
                    paramList.add("%\\" + kwd.trim() + "%")
                }
            }else{
                sql.append(" AND f.title LIKE ?")
                paramList.add("%\\" + kwd.trim() + "%")
            }
        }
        //CNVD编号
        if(params.cnvdId){
            if("0".equals(params.cnvdIdFlag)){
                sql.append(" AND (f.number = ? OR f.old_number = ?)")
                paramList.add(params.cnvdId.trim())
                paramList.add(params.cnvdId.trim())
            }else if("1".equals(params.cnvdIdFlag)){
                sql.append(" OR (f.number = ? OR f.old_number = ?)")
                paramList.add(params.cnvdId.trim())
                paramList.add(params.cnvdId.trim())
            }
        }

        //发布信息
        if(params.baseinfoBeanFlag && "0".equals(params.baseinfoBeanFlag)){
            if(params.baseinfoBeanbeginTime){
                sql.append(" AND f.open_time >= ?")
                paramList.add(params.baseinfoBeanbeginTime.trim())
            }
            if(params.baseinfoBeanendTime){
                sql.append(" AND f.open_time < ?")
                paramList.add(DateUtil.getTomorrowStr(params.baseinfoBeanendTime.trim()))
            }
        }else if(params.baseinfoBeanFlag && "1".equals(params.baseinfoBeanFlag)){
            if(params.baseinfoBeanbeginTime && params.baseinfoBeanendTime){
                sql.append(" OR (f.open_time >= ? AND f.open_time < ?)")
                paramList.add(params.baseinfoBeanbeginTime.trim())
                paramList.add(DateUtil.getTomorrowStr(params.baseinfoBeanendTime.trim()))
            }
            if(params.baseinfoBeanbeginTime && !params.baseinfoBeanendTime){
                sql.append(" OR f.open_time >= ?")
                paramList.add(params.baseinfoBeanbeginTime.trim())
            }
            if(params.baseinfoBeanendTime && !params.baseinfoBeanbeginTime){
                sql.append(" OR f.open_time < ?")

                paramList.add(DateUtil.getTomorrowStr(params.baseinfoBeanendTime.trim()))
            }
        }

        if(params.kwd){
            def kwd = params.kwd.trim()
            sql.append(" AND f.title LIKE ?")
            paramList.add("%" + kwd.decodeHTML().decodeHTML() + "%")
        }

        //type(0-查询list 1-查询count)
        String newSql = sql.toString()
        if(type!=null && 1==type) {
            newSql = "SELECT COUNT(*) AS countAs " + newSql.substring(newSql.indexOf("FROM"), newSql.length())
        }else{
            String orderByStr = " ORDER BY f.open_time DESC"
            if(params.field){
                orderByStr = " ORDER BY "+params.field.trim()
                if(params.order){
                    orderByStr+=" "+params.order.trim()
                }
            }
            Integer maxResults = params.int('numPerPage') //limit第二位

            params.offset = params.offset ? params.int('offset') : 0
            //Integer maxResults = params.max//limit第二位
            Integer offset = params.offset //limit第一位

            sql.append(orderByStr).append(" LIMIT ").append(offset).append(",").append(maxResults)
            newSql = sql.toString()
        }
        log.info("查询Sql|" + newSql)
        log.info("参数集合|" + paramList.toString())
        List<Map<String, Object>> dataResult = SQLUtil.getResult(newSql, paramList)
        return dataResult
    }
    def getQuerySelectResult(def params,def actionType,Integer type) {
        StringBuilder sql = new StringBuilder() //查询sql
        List<Object> paramList = new ArrayList() //参数集合
        StringBuilder querySql = new StringBuilder() //查询sql
        sql.append(" SELECT")
                .append(" f.id,f.number,f.title,f.click_num AS clickNum,f.comment_count AS commentCount,f.concern_count AS concernCount,f.open_time AS openTime,di.value AS diValue,di.name AS diName")
                .append(" FROM flaw f,dictionary_info di ")
                .append(getQueryJoinSql(params))
                .append(" WHERE f.serverity_id =  di.id and f.open_time<'"+ DateUtil.getTomorrowStrDefault()+"' AND f.status = 9 AND f.enable = 1 AND f.is_open=1 AND f.parent_flaw_id is null")
                .append(getWhereSql(params))

        querySql.append(" SELECT")
                .append(" f.id,f.number,f.title,f.click_num AS clickNum,f.comment_count AS commentCount,f.concern_count AS concernCount,f.open_time AS openTime,di.value AS diValue,di.name AS diName")
                .append(" FROM flaw f LEFT JOIN  dictionary_info di ON f.serverity_id =  di.id")
                .append(getJoinSql(params))
                .append(" WHERE f.open_time<'"+ DateUtil.getTomorrowStrDefault()+"' AND f.status = 9 AND f.enable = 1 AND f.is_open=1 AND f.parent_flaw_id is null")

        //actionType  0-漏洞管理 1-一级审核 2-二级审核 3-三级审核-目前没用到

        //漏洞编号
        if (params.number && !"请输入精确编号".equals(params.number)) {
            sql.append(" AND (f.number = ? OR f.old_number = ?)")
            paramList.add(params.number.trim())
            paramList.add(params.number.trim())
        }
        //开始时间
        if (params.startDate) {
            sql.append(" AND f.open_time >= ?")
            paramList.add(params.startDate.trim())
        }
        //结束时间
        if (params.endDate) {
            sql.append(" AND f.open_time < ?")
            paramList.add(DateUtil.getTomorrowStr(params.endDate.trim()))
        }

        //参考信息
        if(params.refenceInfo){
            sql.append(" AND ri.reference_number LIKE ?")
            paramList.add("%" + params.refenceInfo.trim() + "%")
        }
        if(params.referenceScope && !"-1".equals(params.referenceScope)){
            sql.append(" AND ri.reference_type_id = ?")
            paramList.add(params.long('referenceScope'))
        }

        //厂商信息
        if(params.manufacturerId && !"-1".equals(params.manufacturerId)){
            if(params.categoryId && !"-1".equals(params.categoryId)){
                sql.append(" AND pi.name LIKE ?")
                if(params.editionId && !"-1".equals(params.editionId)) {
                    paramList.add(" CONCAT('%\\', (SELECT name FROM manufacturer WHERE id = "+params.manufacturerId+"),' ',(SELECT name FROM product_category WHERE id = "+params.categoryId+"),' "+params.editionId+"','%')")
                }else{
                    paramList.add(" CONCAT('%\\', (SELECT name FROM manufacturer WHERE id = "+params.manufacturerId+"),' ',(SELECT name FROM product_category WHERE id = "+params.categoryId+"),'%')")
                }
            }else{
                sql.append(" AND f.manufacturer_id = ?")
                paramList.add(params.long('manufacturerId'))
            }
        }

        if(params.causeIdStr){
            def ids = params.causeIdStr.trim()
            ids = ids.substring(0,ids.length()-1)
            sql.append(" AND f.cause_id IN (?)")
            paramList.add(ids)
        }
        if(params.threadIdStr){
            def ids = params.threadIdStr.trim()
            ids = ids.substring(0,ids.length()-1)
            sql.append(" AND f.thread_id IN (?)")
            paramList.add(ids)
        }
        if(params.serverityIdStr){
            def ids = params.serverityIdStr.trim()
            ids = ids.substring(0,ids.length()-1)
            sql.append(" AND f.serverity_id IN (?)")
            paramList.add(ids)
        }
        if(params.positionIdStr){
            def ids = params.positionIdStr.trim()
            ids = ids.substring(0,ids.length()-1)
            sql.append(" AND f.position_id IN (?)")
            paramList.add(ids)
        }
        //关键字
        if(params.keyword){
            def kwd = URLDecoder.decode(params.keyword,"UTF-8")
            if("0".equals(params.keywordFlag)){
                if("1".equals(params.condition)){
                    sql.append(" AND f.title LIKE ?")
                    paramList.add("%\\" + kwd.trim() + "%")
                }
                if("0".equals(params.condition)){
                    sql.append(" AND (f.title LIKE ? OR detailedInfo.description LIKE ?)")
                    paramList.add("%\\" + kwd.trim() + "%")
                    paramList.add("%\\" + kwd.trim() + "%")
                }
            }else if("1".equals(params.keywordFlag)){
                if("1".equals(params.condition)){
                    sql.append(" OR f.title LIKE ?")
                    paramList.add("%\\" + kwd.trim() + "%")
                }
                if("0".equals.(params.condition)){
                    sql.append(" OR (f.title LIKE ? OR detailedInfo.description LIKE ?)")
                    paramList.add("%\\" + kwd.trim() + "%")
                    paramList.add("%\\" + kwd.trim() + "%")
                }
            }else{
                sql.append(" AND f.title LIKE ?")
                paramList.add("%\\" + kwd.trim() + "%")
            }
        }
        //CNVD编号
        if(params.cnvdId){
            if("0".equals(params.cnvdIdFlag)){
                sql.append(" AND (f.number = ? OR f.old_number = ?)")
                paramList.add(params.cnvdId.trim())
                paramList.add(params.cnvdId.trim())
            }else if("1".equals(params.cnvdIdFlag)){
                sql.append(" OR (f.number = ? OR f.old_number = ?)")
                paramList.add(params.cnvdId.trim())
                paramList.add(params.cnvdId.trim())
            }
        }

        //发布信息
        if(params.baseinfoBeanFlag && "0".equals(params.baseinfoBeanFlag)){
            if(params.baseinfoBeanbeginTime){
                sql.append(" AND f.open_time >= ?")
                paramList.add(params.baseinfoBeanbeginTime.trim())
            }
            if(params.baseinfoBeanendTime){
                sql.append(" AND f.open_time < ?")
                paramList.add(DateUtil.getTomorrowStr(params.baseinfoBeanendTime.trim()))
            }
        }else if(params.baseinfoBeanFlag && "1".equals(params.baseinfoBeanFlag)){
            if(params.baseinfoBeanbeginTime && params.baseinfoBeanendTime){
                sql.append(" OR (f.open_time >= ? AND f.open_time < ?)")
                paramList.add(params.baseinfoBeanbeginTime.trim())
                paramList.add(DateUtil.getTomorrowStr(params.baseinfoBeanendTime.trim()))
            }
            if(params.baseinfoBeanbeginTime && !params.baseinfoBeanendTime){
                sql.append(" OR f.open_time >= ?")
                paramList.add(params.baseinfoBeanbeginTime.trim())
            }
            if(params.baseinfoBeanendTime && !params.baseinfoBeanbeginTime){
                sql.append(" OR f.open_time < ?")

                paramList.add(DateUtil.getTomorrowStr(params.baseinfoBeanendTime.trim()))
            }
        }

        if(params.kwd){
            def kwd = params.kwd.trim()
            sql.append(" AND f.title LIKE ?")
            paramList.add("%" + kwd.decodeHTML().decodeHTML() + "%")
        }

        //type(0-查询list 1-查询count)
        String newSql = sql.toString()
        if(type!=null && 1==type) {
            //如果没有传参那么使用左连接做统计
            if(paramList.size() == 0){
                String date=DateUtil.get4yMd(new Date());
                //2022-02-22，如果默认查询那么总数量从Redis缓存中获取
                RedisClusterTemplate r = new RedisClusterTemplate();
                String count=r.get("count-"+date);
                String sd1 = DateUtil.get4yMdHms(new Date())
                log.info("|"+sd1+"|webCrawler|count="+count);
                if(StringUtils.isBlank(count)) {
                    newSql="SELECT COUNT(*) AS countAs FROM flaw f LEFT JOIN  dictionary_info di ON f.serverity_id =  di.id"+
                            " WHERE f.open_time<'"+ DateUtil.getTomorrowStrDefault()+"' AND f.status = 9 AND f.enable = 1 AND f.is_open=1 AND f.parent_flaw_id is null";
                    List<Map<String, Object>> countResult = SQLUtil.getResult(newSql, paramList)
                    if (countResult != null && countResult.size() > 0) {
                        //把总数的缓存时间设置成24小时
                        r.expire("count-"+date, countResult.get(0).get("countAs").toString(), "NX", "PX", 86400000);
                        log.info("|"+sd1+"|webCrawler|setCount="+countResult.get(0).get("countAs").toString());
                    }
                    return countResult;
                }else{
                    List<Map<String, Object>> countResult=new ArrayList<Map<String, Object>>();
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("countAs",count);
                    countResult.add(map);
                    log.info("|"+sd1+"|webCrawler|getCount="+count);
                    return countResult;
                }

            }else{
                newSql = "SELECT COUNT(*) AS countAs " + newSql.substring(newSql.indexOf("FROM"), newSql.length())
            }

        }else{
            String orderByStr = " ORDER BY f.open_time DESC"
            if(params.field){
                orderByStr = " ORDER BY "+params.field.trim()
                if(params.order){
                    orderByStr+=" "+params.order.trim()
                }
            }
            Integer maxResults = params.int('numPerPage') //limit第二位

            params.offset = params.offset ? params.int('offset') : 0
            //Integer maxResults = params.max//limit第二位
            Integer offset = params.offset //limit第一位
            if(paramList.size() == 0){
                querySql.append(orderByStr).append(" LIMIT ").append(offset).append(",").append(maxResults)
                newSql = querySql.toString()
            }else{
                sql.append(orderByStr).append(" LIMIT ").append(offset).append(",").append(maxResults)
                newSql = sql.toString()
            }
        }
        log.info("新优化的查询Sql|" + newSql)
        log.info("新优化的参数集合|" + paramList.toString())
        List<Map<String, Object>> dataResult = SQLUtil.getResult(newSql, paramList)
        return dataResult
    }

    def getJoinSql(def params) {
        StringBuilder sql = new StringBuilder() //根据前端传递参数拼接join-sql

        //参考信息
        if(params.refenceInfo || (params.referenceScope && !"-1".equals(params.referenceScope))){
            sql.append(" LEFT JOIN reference_info ri ON f.id = ri.flaw_id")
        }

        //厂商信息
        if(params.manufacturerId && !"-1".equals(params.manufacturerId)){
            if(params.categoryId && !"-1".equals(params.categoryId)){
                sql.append(" LEFT JOIN flaw_product fp ON f.id = fp.flaw_id")
                sql.append(" LEFT JOIN product_info pi ON fp.product_id = pi.id")
            }
        }
        //关键字
        if(params.keyword){
            if("0".equals(params.keywordFlag)){
                if("0".equals(params.condition)){
                    sql.append(" LEFT JOIN detailed_info detailedInfo ON f.detailed_info_id = detailedInfo.id")
                }
            }else if("1".equals(params.keywordFlag)){
                if("0".equals.(params.condition)){
                    sql.append(" LEFT JOIN detailed_info detailedInfo ON f.detailed_info_id = detailedInfo.id")
                }
            }
        }

        return sql.toString()
    }

    def getQueryJoinSql(def params) {
        StringBuilder sql = new StringBuilder()

        //参考信息
        if(params.refenceInfo || (params.referenceScope && !"-1".equals(params.referenceScope))){
            sql.append(",reference_info ri")
        }

        //厂商信息
        if(params.manufacturerId && !"-1".equals(params.manufacturerId)){
            if(params.categoryId && !"-1".equals(params.categoryId)){
                sql.append(",flaw_product fp")
                sql.append(",product_info pi")
            }
        }
        //关键字
        if(params.keyword){
            if("0".equals(params.keywordFlag)){
                if("0".equals(params.condition)){
                    sql.append(",detailed_info detailedInfo")
                }
            }else if("1".equals(params.keywordFlag)){
                if("0".equals.(params.condition)){
                    sql.append(",detailed_info detailedInfo")
                }
            }
        }

        return sql.toString()
    }

    def getWhereSql(def params) {
        StringBuilder sql = new StringBuilder()

        //参考信息
        if(params.refenceInfo || (params.referenceScope && !"-1".equals(params.referenceScope))){
            sql.append(" and f.id = ri.flaw_id")
        }

        //厂商信息
        if(params.manufacturerId && !"-1".equals(params.manufacturerId)){
            if(params.categoryId && !"-1".equals(params.categoryId)){
                sql.append(" and f.id = fp.flaw_id")
                sql.append(" and fp.product_id = pi.id")
            }
        }
        //关键字
        if(params.keyword){
            if("0".equals(params.keywordFlag)){
                if("0".equals(params.condition)){
                    sql.append(" and f.detailed_info_id = detailedInfo.id")
                }
            }else if("1".equals(params.keywordFlag)){
                if("0".equals.(params.condition)){
                    sql.append(" and f.detailed_info_id = detailedInfo.id")
                }
            }
        }

        return sql.toString()
    }


    def setFlawUrl(def flawId,def url) {
        try {
            Map<String, Object> paramsValue = new HashMap<String, Object>()
            paramsValue.put("flawId", flawId)
            paramsValue.put("url", url)
            String responseStr = HttpUtil.doPost(flawUrlSaveInterface, paramsValue)
            Response response = JSON.parseObject(responseStr, Response.class)
            if (Response.SUCCESS == response.getCode()) {
                log.info("setFlawUrl成功")
            } else {
                log.error("setFlawUrl失败|" + response.getMessage())
            }
        }catch(Exception e){
            log.error("setFlawUrl失败|",e)
        }
    }
}
