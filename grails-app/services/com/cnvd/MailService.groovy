package com.cnvd

import com.cnvd.utils.*
import org.apache.commons.lang.StringUtils
import org.springframework.core.io.ClassPathResource

import java.text.SimpleDateFormat


class MailService {
    def grailsApplication

    /**
     * 发送邮件模板判断和加载
     *
     * <AUTHOR>
     * @date :2022-02-24 15:37:09
     */
    def getMailContent(def paramsMap) {
        String type = paramsMap.get("type");
        String url= "${ grailsApplication.config.url_config.server_url }";
        if ("regist".equals(type)) {//用户注册
            def user = TUser.findById(Long.valueOf(paramsMap.get("userId")))
            if(user == null){
                return null;
            }
            String content = "<table width='600' border='0' cellpadding='0' cellspacing='0' style='font-size:12px;font-famliy:SimSun;line-height:20px;color:#555;'>" +
                    "<tbody><tr><td>" +
                    "<table width='598' border='0' cellpadding='0' cellspacing='0' bordercolor='fa5d0b' style='padding:10px;border:1px solid #ffe9de;'>" +
                    "<tbody><tr><td></td></tr>";
            if (user!=null && StringUtils.isNotBlank(user.getNickName())) {
                //您好
                content = content + "<tr><td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;'><p style='color: #393c3d; font-family:Arial, Helvetica, sans-serif; font-size: 16px; font-weight: bold; line-height:20px; margin: 0px 0px 12px;' >" + user.getNickName() + "，Hello！</p></td></tr>";
            }
            if (user.status == Constants.userStatusMap['UNACTIVE']) {
                content = content + "<tr>" +
                        //您现在就可以激活并使用国家信息安全漏洞共享平台。
                        "<td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;margin-left: 24px'>Activation of the National Information Security Vulnerability Sharing Platform (CNVD) is now available for immediate use.</td>" +
                        "</tr><tr>" +
                        //点击激活国家信息安全漏洞共享平台账户
                        "<td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;margin-left: 24px'><a href='https://" + url + "/user/activate?activeCode=" + user.getActiveCode()+ "' target='_blank'>To activate your National Information Security Vulnerability Sharing Platform (CNVD) account, please</a></td>" +
                        "</tr>";
            } else {
                content = content + "<tr>" +
                        //您注册的账户正在审核中，请耐心等待，谢谢
                        "<td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;margin-left: 24px'>Your account registration is under review. Please wait patiently. Thank you</td></tr>";
            }
            content = content + "</tbody></table>" +
                    "</td></tr></tbody><tfoot><tr>" +
                    //国家信息安全漏洞共享平台
                    "<td style='font-famliy:Tahoma,Helvetica,Arial,sans-serif;padding:5px 0;font-size:12px;color:#999;text-align:center;'>&copy; Copyright 2010 <a href='https://www.cnvd.org.cn' target='_blank'>www.cnvd.org.cn</a> <a href='https://www.cnvd.org.cn' target='_blank'>National Information Security Vulnerability Sharing Platform (CNVD)</a> Copyright</td>" +
                    "</tr></tfoot> </table>";
            return content;
        } else if ("resetPwd".equals(type)) {//重置密码
            String content = "<table width='600' border='0' cellpadding='0' cellspacing='0' style='font-size:12px;font-famliy:SimSun;line-height:20px;color:#555;'>" +
                    "<tbody><tr><td>" +
                    "<table width='598' border='0' cellpadding='0' cellspacing='0' bordercolor='fa5d0b' style='padding:10px;border:1px solid #ffe9de;'>"+
                    "<tbody><tr><td colspan='2'"+
            "style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px;'><div style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px; line-height: 1.5;; padding: 12px 0px 1px;' class='template-content'>"+
                    "<p style='color: #00000; font-family: Arial, Helvetica, sans-serif; font-size: 16px; font-weight: bold; line-height: 1.5;; margin: 0pt 0pt 12px;' class='bigheader'>"+paramsMap.get("userNickName")+"</p>"+
                    "</div></td></tr><tr><td colspan='2' height='24' valign='middle' style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>"+
                    //请点击以下链接进行密码重置
                "Please click the link below to reset your password: <a id='aLink' href='https://"+url+"/user/resetPwd?t="+paramsMap.get("t")+"' target='_blank'>"+
                    "https://"+url+"/user/resetPwd?t="+paramsMap.get("t")+"</a></td></tr></tbody></table>";
            content = content + "</td></tr></tbody><tfoot><tr>" +
                    "<td style='font-famliy:Tahoma,Helvetica,Arial,sans-serif;padding:5px 0;font-size:12px;color:#999;text-align:center;'>&copy; Copyright 2010 <a href='https://www.cnvd.org.cn' target='_blank'>www.cnvd.org.cn</a> <a href='https://www.cnvd.org.cn' target='_blank'>National Information Security Vulnerability Sharing Platform (CNVD)</a> Copyright</td>" +
                    "</tr></tfoot> </table>";
            return content;
        }else if("regist1".equals(type)){//账号开通
            def user = TUser.findById(Long.valueOf(paramsMap.get("userId")))
            if(user == null){
                return null;
            }
            String content = "<table width='600' border='0' cellpadding='0' cellspacing='0' style='font-size:12px;font-famliy:SimSun;line-height:20px;color:#555;'>" +
                    "<tbody><tr><td>" +
                    "<table width='598' border='0' cellpadding='0' cellspacing='0' bordercolor='fa5d0b' style='padding:10px;border:1px solid #ffe9de;'>"+
                    "<tbody><tr><td>&nbsp;</td></tr>"+
                    "<tr>"+
                    "<td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;'><p style='color: #393c3d; font-family:Arial, Helvetica, sans-serif; font-size: 16px; font-weight: bold; line-height:20px; margin: 0px 0px 12px;' >"+user.getNickName()+"，Hello！</p></td>"+
                    "</tr><tr>"+
//                    /账号已开通，您现在可以登陆并使用国家信息安全漏洞共享平台
                    "<td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;margin-left: 24px'>Your account has been activated. You may now log in and use the National Information Security Vulnerability Sharing Platform (CNVD).</td>"+
	                "</tr><tr>"+
                    //账号
                    "<td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;margin-left: 24px'>Account："+user.getEmail()+"</td>"+
                    "</tr><tr>"+
                    //密码
                    "<td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;margin-left: 24px'>Password："+user.getPassword()+"</td>"+
                    "</tr><tr>"+
                    //点击登陆国家信息安全漏洞共享平台并完善资料
                    "<td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;margin-left: 24px'><a href='https://"+url+"/user/login' target='_blank'>Click to log in to the National Information Security Vulnerability Sharing Platform (CNVD) and complete your profile</a></td>"+
                    "</tr></tbody></table>"+
                    "</td></tr></tbody><tfoot><tr>" +
                    "<td style='font-famliy:Tahoma,Helvetica,Arial,sans-serif;padding:5px 0;font-size:12px;color:#999;text-align:center;'>&copy; Copyright 2010 <a href='https://www.cnvd.org.cn' target='_blank'>www.cnvd.org.cn</a> <a href='https://www.cnvd.org.cn' target='_blank'>National Information Security Vulnerability Sharing Platform (CNVD)</a> Copyright</td>" +
                    "</tr></tfoot> </table>";
            return content;
        }else if("examine".equals(type)){//用户审核通过
            def user = TUser.findById(Long.valueOf(paramsMap.get("userId")))
            if(user == null){
                return null;
            }
            String content = "<table width='600' border='0' cellpadding='0' cellspacing='0' style='font-size:12px;font-famliy:SimSun;line-height:20px;color:#555;'>" +
                    "<tbody><tr><td>" +
                    "<table width='598' border='0' cellpadding='0' cellspacing='0' bordercolor='fa5d0b' style='padding:10px;border:1px solid #ffe9de;'>"+
                    "<tbody><tr><td>&nbsp;</td></tr>"+
                    "<tr>"+
                    "<td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;'><p style='color: #393c3d; font-family:Arial, Helvetica, sans-serif; font-size: 16px; font-weight: bold; line-height:20px; margin: 0px 0px 12px;' >"+user.getNickName()+"，Hello！</p></td>"+
                    "</tr><tr>"+
                    //账号已开通，您现在可以登陆并使用国家信息安全漏洞共享平台
                    "<td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;margin-left: 24px'>Your account has been successfully activated. You may now log in and access the National Information Security Vulnerability Sharing Platform (CNVD)</td>"+
                    "</tr> <tr>"+
                    //点击登录国家信息安全漏洞共享平台并完善资料
                    "<td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;margin-left: 24px'><a href='https://"+url+"/user/login' target='_blank'>Click to log in to the National Information Security Vulnerability Sharing Platform (CNVD) and complete your profile information</a></td>"+
                    "</tr></tbody></table>"+
                    "</td></tr></tbody><tfoot><tr>" +
                    "<td style='font-famliy:Tahoma,Helvetica,Arial,sans-serif;padding:5px 0;font-size:12px;color:#999;text-align:center;'>&copy; Copyright 2010 <a href='https://www.cnvd.org.cn' target='_blank'>www.cnvd.org.cn</a> <a href='https://www.cnvd.org.cn' target='_blank'>National Information Security Vulnerability Sharing Platform (CNVD)</a> Copyright</td>" +
                    "</tr></tfoot> </table>";
            return content;
        }else if("reportFlaw".equals(type)){//漏洞上报
            def flaw = Flaw.get(Long.valueOf(paramsMap.get("flawId")));
            if(flaw == null){
                return null;
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String content = "<table width='600' border='0' cellpadding='0' cellspacing='0' style='font-size:12px;font-famliy:SimSun;line-height:20px;color:#555;'>" +
                    "<tbody><tr><td>" +
                    "<table width='598' border='0' cellpadding='0' cellspacing='0' bordercolor='fa5d0b' style='padding:10px;border:1px solid #ffe9de;'>"+
                    "<tbody><tr>"+
                    "<td style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>"+
                    "<div style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px; line-height: 1.5;; padding: 12px 0px 1px;' class='template-content'>"+
                    "<p style='color: #00000; font-family: Arial, Helvetica, sans-serif; font-size: 16px; font-weight: bold; line-height: 1.5;; margin: 0pt 0pt 12px;' class='bigheader'>"+
                    flaw.getTitle()+"</p></div></td></tr><tr>"+
                    "<td height='24' valign='middle' style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>"+
                    //上报者
                    "Reporter："+flaw.getUser().getNickName()+"</td></tr><tr>"+
                    "<td height='24' valign='middle' style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>"+
                    //上报者ID
                    "Reporter ID："+flaw.getUser().getId()+
                    "</td></tr><tr>"+
                    "<td height='24' valign='middle' style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>"+
                    //发现日期
                    "Discovery Date："+sdf.format(flaw.getFoundTime())+
                    "</td></tr><tr>"+
                    "<td height='24' valign='middle' style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>"+
                    //是否公开
                    "Disclosure Status："+(flaw.getIsOpen()==1?"是":"否")+
                    "</td></tr><tr>"+
                    "<td height='24' valign='middle' style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>"+
                    //发现者
                    "Discoverer："+flaw.getDiscovererName()+
                    "</td></tr><tr>"+
                    "<td height='24' valign='middle' style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>"+
                    //漏洞名称
                    "Vulnerability Name："+flaw.getTitle()+
                    "</td></tr><tr>"+
                    "<td height='24' valign='middle' style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>"+
                    //漏洞描述
                    "Vulnerability Description："+flaw.getDetailedInfo().getDescription()+
                    "</td></tr><tr>"+
                    "<td height='24' valign='middle' style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>"+
                    //解决方案
                    "Solution："+(flaw.getDetailedInfo().getFormalWay()!=null?flaw.getDetailedInfo().getFormalWay():"")+
                    "</td></tr><tr>"+
                    "<td height='24' valign='middle' style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>"+
                    //附件
                    "Attachment：";
                    if(flaw.getAttachment() != null){
                        content =content+"<a href='https://"+url+"/flaw/attDown?cd="+flaw.getDownCode()+"'>"+flaw.getAttachment().getFileName()+"</a>";
                    }else{
                        //暂无
                        content =content+"(N/A)";
                    }
                    content =content+"</td></tr><tr>"+
                    "<td height='24' valign='middle' style='color: #444444; font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>"+
                    //上报者邮箱
                    "Reporter's Email："+flaw.getUser().getEmail()+
                    "</td></tr></tbody></table>"+
                    "</td></tr></tbody><tfoot><tr>" +
                    "<td style='font-famliy:Tahoma,Helvetica,Arial,sans-serif;padding:5px 0;font-size:12px;color:#999;text-align:center;'>&copy; Copyright 2010 <a href='https://www.cnvd.org.cn' target='_blank'>www.cnvd.org.cn</a> <a href='https://www.cnvd.org.cn' target='_blank'>National Information Security Vulnerability Sharing Platform (CNVD)</a> Copyright</td>" +
                    "</tr></tfoot> </table>";
        }else if("examineSuccess".equals(type)){//漏洞审核通过
            def flaw = Flaw.get(Long.valueOf(paramsMap.get("flawId")));
            if(flaw == null){
                return null;
            }
            String content = "<table width='600' border='0' cellpadding='0' cellspacing='0' style='font-size:12px;font-famliy:SimSun;line-height:20px;color:#555;'>" +
                    "<tbody><tr><td>" +
                    "<table width='598' border='0' cellpadding='0' cellspacing='0' bordercolor='fa5d0b' style='padding:10px;border:1px solid #ffe9de;'>"+
                    "<tbody><tr><td>&nbsp;</td></tr>"+
                    "<tr>"+
                    "<td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;'><p style='color: #393c3d; font-family:Arial, Helvetica, sans-serif; font-size: 16px; font-weight: bold; line-height:20px; margin: 0px 0px 12px;' >"+flaw.getUser().getNickName()+"，Hello！</p></td>"+
                    "</tr><tr>"+
                    //您上报的漏洞   已后台审核通过，请    登录
                    "<td width='520px' style='color:#848484; font-family:Arial, Helvetica, sans-serif; font-size: 12px;margin-left: 24px'>The vulnerability you reported（"+flaw.getNumber()+"）has been approved through backend review，Please<a href='http://"+url+"/user/login' target='_blank'>login</a>" +
                    //个人用户中心进行查看
                    "to check in your Personal User Center</td>"+
                    "</tr></tbody></table>"+
                    "</td></tr></tbody><tfoot><tr>" +
                    "<td style='font-famliy:Tahoma,Helvetica,Arial,sans-serif;padding:5px 0;font-size:12px;color:#999;text-align:center;'>&copy; Copyright 2010 <a href='https://www.cnvd.org.cn' target='_blank'>www.cnvd.org.cn</a> <a href='https://www.cnvd.org.cn' target='_blank'>National Information Security Vulnerability Sharing Platform (CNVD)</a> Copyright</td>" +
                    "</tr></tfoot> </table>";
            return content;
        }
    }
}
