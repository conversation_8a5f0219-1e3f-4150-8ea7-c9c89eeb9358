package com.cnvd

import java.io.FileWriter;
import java.text.SimpleDateFormat

import com.cnvd.flawInfo.DictionaryInfo
import com.cnvd.flawInfo.Exploit
import com.cnvd.flawInfo.FlawProduct
import com.cnvd.flawInfo.ReferenceInfo

class SendFTPTxtService {
	
	def createFlawTxt(def txtPath,def txtName,def txtOkName,def flawList){
		File file = new File(txtPath)
		if(!file.exists()){
			file.mkdirs()
		}
		
		SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMdd");
		SimpleDateFormat sdf1=new SimpleDateFormat("yyyy.MM.dd");
		
		OutputStreamWriter out = new OutputStreamWriter(new FileOutputStream(txtPath+"/"+txtName),"UTF-8");
		StringBuilder tmpstr=new StringBuilder();
		tmpstr.append("SOURCE_TYPE\tSOURCE\tVUL_ID_N\tVUL_NAME\tVUL_DESCRIPTION\tTIME\tDD\tVUL_WORKAROUND\tVUL_SOLUTION\tVUL_CAUSE\tVUL_SEVERITY\tVUL_THREAD\tVUL_POSITION\tVUL_SOFT_STYLE\tVUL_CONTRIBUTOR_NAME\tVUL_AFFECTED_PRODUCT\tVUL_REFERENCE_INFO\tVUL_EXPLOIT\n");
		
		String tmp="";
		flawList.each{
			tmpstr.append("REPORT");
			tmpstr.append("\t");
			
			tmpstr.append("CNVD");
			tmpstr.append("\t");
			
			//漏洞编号
			tmpstr.append(it.number);
			tmpstr.append("\t");
			//漏洞标题
			tmpstr.append(it.title?it.title.replaceAll("\t","095C0B").trim():"");
			tmpstr.append("\t");
			//漏洞描述
			tmpstr.append(it.detailedInfo?.description?it.detailedInfo?.description.replaceAll("\r", "0D5C0B").replaceAll("\n", "0A5C0B").replaceAll("\t","095C0B"):"暂无");
			tmpstr.append("\t");
			//漏洞公开时间
			tmpstr.append(sdf1.format(it.openTime));
			tmpstr.append("\t");
			//漏洞时间
			tmpstr.append(sdf.format(it.openTime));
			tmpstr.append("\t");
			//漏洞临时解决办法
			if(it.detailedInfo?.tempWay){
				tmp=it.detailedInfo?.tempWay?.replaceAll("\n", "0A5C0B").replaceAll("\r", "0D5C0B").replaceAll("\t","095C0B");
			}else{
				tmp="暂无";
			}
			tmpstr.append(tmp);
			tmpstr.append("\t");
			
			//漏洞正式解决办法
			tmp=it.detailedInfo?.formalWay?it.detailedInfo?.formalWay.replaceAll("\r", "0D5C0B").replaceAll("\n", "0A5C0B").replaceAll("\t","095C0B"):"暂无";
			tmpstr.append(tmp);
			tmpstr.append("\t");
			
			//漏洞产生原因
			if(it.causeId){
				def cause = DictionaryInfo.get(it.causeId)
				tmp=cause?.name.replaceAll("\r", "0D5C0B").replaceAll("\n", "0A5C0B").replaceAll("\t","095C0B");
				tmpstr.append(tmp);			
				tmpstr.append("\t");
			}else{
				tmpstr.append("暂无\t");
			}
			
			//漏洞危害级别
			if(it.serverityId){
				def serverity = DictionaryInfo.get(it.serverityId)
				tmp=serverity.name?.replaceAll("/r", "0D5C0B").replaceAll("\n", "0A5C0B").replaceAll("\t","095C0B");
				tmpstr.append(tmp);
				tmpstr.append("\t");
			}else{
				tmpstr.append("暂无\t");
			}
			
			//漏洞引发的危险
			if(it.threadId){
				def thread = DictionaryInfo.get(it.threadId)
				tmp=thread?.name?.replaceAll("\r", "0D5C0B").replaceAll("\n", "0A5C0B").replaceAll("\t","095C0B");
				tmpstr.append(tmp);
				tmpstr.append("\t");
			}else{
				tmpstr.append("暂无\t");
			}
			
			//漏洞攻击的位置
			if(it.positionId){
				def position = DictionaryInfo.get(it.positionId)
				tmp = position?.name.replaceAll("\r", "0D5C0B").replaceAll("\n", "0A5C0B").replaceAll("\t","095C0B");
				tmpstr.append(tmp);
				tmpstr.append("\t");
			}else{
				tmpstr.append("暂未描述\t");
			}
			
			//漏洞影响的类型
			if(it.softStyleId){
				def softStyle=DictionaryInfo.get(it.softStyleId)
				tmp = softStyle?.name.replaceAll("\r", "0D5C0B").replaceAll("\n", "0A5C0B").replaceAll("\t","095C0B");
				tmpstr.append(tmp);
				tmpstr.append("\t");
			}else{
				tmpstr.append("暂未描述\t");
			}
			
			//贡献者姓名
			if(it.user){
				tmp = it.user.nickName.replaceAll("\r", "0D5C0B").replaceAll("\n", "0A5C0B").replaceAll("\t","095C0B");
				tmpstr.append(tmp);
				tmpstr.append("\t");
			}else{
				tmpstr.append("暂无\t");
			}
			
			//影响产品
			def flawProductList = FlawProduct.findByFlaw(it)
			if(flawProductList){
				flawProductList.each{
					tmp+=it.product.name.replaceAll("\r", "0D5C0B").replaceAll("\n", "0A5C0B").replaceAll("\t","095C0B")+","
				}
				
				tmpstr.append(tmp.substring(0,tmp.length()-1));
				tmpstr.append("\t");
			}else{
				tmpstr.append("暂无描述");
				tmpstr.append("\t");
			}
			
			//漏洞参考信息
			String referenc="";
			def referenceInfoList = ReferenceInfo.findAllByFlaw(it)
			if(referenceInfoList){
				referenceInfoList.each{
					def typeName = it.referenceType?.name
					typeName?typeName:"暂无"
					def referenceNumber = it.referenceNumber ? it.referenceNumber:"暂无"
					def linkUrl = it.linkUrl?it.linkUrl:"暂无"
					referenc+=typeName+"&&"+referenceNumber+"&&链接:"+linkUrl
				}
				tmpstr.append(referenc.replaceAll("\r", "0D5C0B").replaceAll("\n", "0A5C0B").replaceAll("\t","095C0B"));
				tmpstr.append("\t");
			}else{
				tmpstr.append("暂无链接");
				tmpstr.append("\t");
			}
			

			//漏洞验证信息
			def exploit=Exploit.findByFlaw(it)
			if(exploit){
				tmpstr.append(exploit.concept?exploit.concept?.replaceAll("\r", "0D5C0B").replaceAll("\n", "0A5C0B").replaceAll("\t","095C0B"):"暂未描述");
				tmpstr.append("\t");
			}else{
				tmpstr.append("暂未描述");
				tmpstr.append("\t");
			}
			if(exploit){
				tmpstr.append(exploit.poc?exploit.poc?.replaceAll("\r", "0D5C0B").replaceAll("\n", "0A5C0B").replaceAll("\t","095C0B"):"暂未描述");
				tmpstr.append("\t");
			}else{
				tmpstr.append("暂未描述");
				tmpstr.append("\t");
			}
			tmpstr.append("\n");
		}
		
		out.write(tmpstr.toString());
		out.flush();
		out.close();
		
		FileWriter fw = new FileWriter(txtPath+"/"+txtOkName);
		tmpstr=new StringBuilder();
	   
		tmpstr.append("\n");
		fw.write(tmpstr.toString());
		fw.close();
	}
}
