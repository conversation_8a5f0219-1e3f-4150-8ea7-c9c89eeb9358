package com.cnvd

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

import org.jdom.Document;
import org.jdom.Element;
import org.jdom.output.XMLOutputter;
import org.springframework.aop.aspectj.RuntimeTestWalker.ThisInstanceOfResidueTestVisitor;

class CreateXMLService {
	
	public String[] parseStr(String userStr){
		//把userStr字符串用";"进行切割并保存到一个String数组中
		String[] userNameArr = userStr.split(";");
		return userNameArr;
	}
   
   public void createXMLAction(String abbreviationStr,String userStr,String countStr,String colorStr,String filePath){
	   String[] abbreviationArr = this.parseStr(abbreviationStr);
	   String[] userNameArr = this.parseStr(userStr);
	   String[] flawCountArr = this.parseStr(countStr);
	   String[] colorArr = this.parseStr(colorStr);
	   
	   //生成根和不是叶节点的xml节点
	   Element rootElt = new Element("chart");
	   Element seriesElt = new Element("series");
	   Element graphsElt = new Element("graphs");
	   Element graphElet = new Element("graph");

	   for(int i=0;i<userNameArr.length && userNameArr.length>0;i++){
		   // 做遍历 生成XML节点,然后添加到seriesElt或者graphElet节点上去
		   Element value = new Element("value");
		   value.setAttribute("xid", ""+i);
		   if(abbreviationArr[i] != 'null'){
			   value.setText(userNameArr[i]+","+abbreviationArr[i]);
		   }else{
			   value.setText(userNameArr[i]);
		   }
		   seriesElt.addContent(value);
		   Element gvalue = new Element("value");
		   gvalue.setAttribute("xid", ""+i);
		   gvalue.setAttribute("color", colorArr[i]);
		   gvalue.setText(flawCountArr[i]);
		   graphElet.addContent(gvalue);
	   }
	   
	   graphsElt.addContent(graphElet);
	   
	   rootElt.addContent(seriesElt);
	   rootElt.addContent(graphsElt);

	   //输出xml
	   Document document = new Document(rootElt);
	   XMLOutputter out = new  XMLOutputter();
	   
	   
	   String subFilePath = filePath.substring(0,filePath.lastIndexOf("/"));
	   File file = new File(subFilePath);
	   if(!file.exists()){
		   file.mkdirs();
	   }
	   try {
		   //写到指定的文件
		   FileOutputStream fos = new FileOutputStream(filePath);
		   out.output(document, fos);
		   fos.close();
	   } catch (FileNotFoundException e) {
		   e.printStackTrace();
	   } catch (IOException e) {
		   e.printStackTrace();
	   }
   }
   
   /**
    * 创建日期趋势xml文件
    */
   public void createWeekTrendXMLAction(String weekTrendStr,String weekTrendCountStr,String colorStr,String weekTrendFilePath){
	   String weekDayStr = "周一;周二;周三;周四;周五;周六;周日;";
	   String[] weekDayArr = this.parseStr(weekDayStr);
	   String[] weekTrendArr = this.parseStr(weekTrendStr);//1 2 3 4
	   String[] weekTrendCountArr = this.parseStr(weekTrendCountStr);
	   String[] colorArr = this.parseStr(colorStr);
	   Map<String,Integer> weekTrendMap = new HashMap<String,Integer>();
	   for(String weekDay : weekDayArr){
		   weekTrendMap.put(weekDay,0);
	   }
	   for(int i=0;i<weekTrendArr.length;i++){
		   def index = Integer.parseInt(weekTrendArr[i]);
		   if(weekTrendMap.containsKey(weekDayArr[index])){//如果有值就累加
			   weekTrendMap.put(weekDayArr[index], weekTrendMap.get(weekDayArr[index])+Integer.parseInt(weekTrendCountArr[i]));
		   }else{
			   weekTrendMap.put(weekDayArr[index], Integer.parseInt(weekTrendCountArr[i]));
		   }
	   }
	   
	   //生成根和不是叶节点的xml节点
	   Element rootElt = new Element("chart");
	   Element seriesElt = new Element("series");
	   Element graphsElt = new Element("graphs");
	   Element graphElet = new Element("graph");
	   
	   for(int i=0;i<weekDayArr.length && weekDayArr.length>0;i++){
		   // 做遍历 生成XML节点,然后添加到seriesElt或者graphElet节点上去
		   Element value = new Element("value");
		   value.setAttribute("xid", ""+i);
		   value.setText(weekDayArr[i]);
		   seriesElt.addContent(value);
		   Element gvalue = new Element("value");
		   gvalue.setAttribute("xid", ""+i);
		   gvalue.setAttribute("color", colorArr[i]);
		   gvalue.setText(weekTrendMap.get(weekDayArr[i])+"");
		   graphElet.addContent(gvalue);
	   }
	   
	   graphsElt.addContent(graphElet);
	   
	   rootElt.addContent(seriesElt);
	   rootElt.addContent(graphsElt);
	   
	   //输出xml
	   Document document = new Document(rootElt);
	   XMLOutputter out = new  XMLOutputter();
	   
	   
	   String subFilePath = weekTrendFilePath.substring(0,weekTrendFilePath.lastIndexOf("/"));
	   File file = new File(subFilePath);
	   if(!file.exists()){
		   file.mkdirs();
	   }
	   try {
		   //写到指定的文件
		   FileOutputStream fos = new FileOutputStream(weekTrendFilePath);
		   out.output(document, fos);
		   fos.close();
	   } catch (FileNotFoundException e) {
		   e.printStackTrace();
	   } catch (IOException e) {
		   e.printStackTrace();
	   }
   }
   
   public void createYearTrendXMLAction(String quarterNumStr,String yearTrendCountStr,String colorStr,String yearTrendFilePath){
	   String quarterStr = "第一季度;第二季度;第三季度;第四季度;";
	   String[] quarterArr = this.parseStr(quarterStr);
	   String[] quarterNumArr = this.parseStr(quarterNumStr);
	   String[] countArr = this.parseStr(yearTrendCountStr);
	   String[] colorArr = this.parseStr(colorStr);
	   
	   //生成根和不是叶节点的xml节点
	   Element rootElt = new Element("chart");
	   Element seriesElt = new Element("series");
	   Element graphsElt = new Element("graphs");
	   Element graphElet = new Element("graph");
	   
	   for(int i=0;i<quarterArr.length && quarterArr.length>0;i++){
		   // 做遍历 生成XML节点,然后添加到seriesElt或者graphElet节点上去
		   Element value = new Element("value");
		   value.setAttribute("xid", ""+i);
		   value.setText(quarterArr[i]);
		   seriesElt.addContent(value);
		   Element gvalue = new Element("value");
		   gvalue.setAttribute("xid", ""+i);
		   gvalue.setAttribute("color", colorArr[i]);
		   gvalue.setText(countArr[i]);
		   graphElet.addContent(gvalue);
	   }
	   graphsElt.addContent(graphElet);
	   
	   rootElt.addContent(seriesElt);
	   rootElt.addContent(graphsElt);
	   
	   //输出xml
	   Document document = new Document(rootElt);
	   XMLOutputter out = new  XMLOutputter();
	   
	   
	   String subFilePath = yearTrendFilePath.substring(0,yearTrendFilePath.lastIndexOf("/"));
	   File file = new File(subFilePath);
	   if(!file.exists()){
		   file.mkdirs();
	   }
	   try {
		   //写到指定的文件
		   FileOutputStream fos = new FileOutputStream(yearTrendFilePath);
		   out.output(document, fos);
		   fos.close();
	   } catch (FileNotFoundException e) {
		   e.printStackTrace();
	   } catch (IOException e) {
		   e.printStackTrace();
	   }
   }
   
   /**
    * 生成月漏洞趋势图数据文件
    * @param quarterNumStr
    * @param prevMonthDaysCount
    * @param prevMonthTrendCount
    * @param colorStr
    * @param monthTrendFilePath
    */
   public void createMonthTrendXMLAction(String prevMonthNumStr,String prevMonthDaysCount,
	   String prevMonthTrendCount,String colorStr,String monthTrendFilePath){
	   String[] daysArr = this.parseStr(prevMonthNumStr);
	   String[] colorArr = this.parseStr(colorStr);
	   String[] countArr = this.parseStr(prevMonthTrendCount);
	   //生成根和不是叶节点的xml节点
	   Element rootElt = new Element("chart");
	   Element seriesElt = new Element("series");
	   Element graphsElt = new Element("graphs");
	   Element graphElet = new Element("graph");
	   
	   for(int i=0;i<daysArr.length && daysArr.length>0;i++){
		   // 做遍历 生成XML节点,然后添加到seriesElt或者graphElet节点上去
		   Element value = new Element("value");
		   value.setAttribute("xid", ""+i);
		   value.setText(daysArr[i]);
		   seriesElt.addContent(value);
		   Element gvalue = new Element("value");
		   gvalue.setAttribute("xid", ""+i);
		   gvalue.setAttribute("color", colorArr[i]);
		   gvalue.setText(countArr[i]);
		   graphElet.addContent(gvalue);
	   }
	   graphsElt.addContent(graphElet);
	   
	   rootElt.addContent(seriesElt);
	   rootElt.addContent(graphsElt);
	   
	   //输出xml
	   Document document = new Document(rootElt);
	   XMLOutputter out = new  XMLOutputter();
	   
	   
	   String subFilePath = monthTrendFilePath.substring(0,monthTrendFilePath.lastIndexOf("/"));
	   File file = new File(subFilePath);
	   if(!file.exists()){
		   file.mkdirs();
	   }
	   try {
		   //写到指定的文件
		   FileOutputStream fos = new FileOutputStream(monthTrendFilePath);
		   out.output(document, fos);
		   fos.close();
	   } catch (FileNotFoundException e) {
		   e.printStackTrace();
	   } catch (IOException e) {
		   e.printStackTrace();
	   }
   }
}
