package com.cnvd
class UserService {

	static transactional = true
	
	/**
	 * 用户积分的更新及更新积分明细表
	 * @param tuser
	 * @param scoreType
	 * @param scoreValue
	 * <AUTHOR>
	 * @return
	 */
	def addUserScore(TUser tuser,Integer scoreType,Integer scoreValue) {
		tuser.integValue=tuser.integValue ? tuser.integValue+scoreValue : 0 +scoreValue
		tuser.save(flush:true)
		def integralInfo=new IntegralInfo()
		integralInfo.user=tuser
		integralInfo.integralType=scoreType
		integralInfo.integralValue=scoreValue
		integralInfo.save(flush:true)
    }
	
	/**
	 * Register a user and send an email to him.
	 * @param user
	 * @param session
	 * @return
	 */
	def doRegist(def user, def session) {

		try {
			if(user.save(flush:true)) {
				
			} else {
				println user.errors
				return false
			}
			new Mail(fromEmail: null, toEmail: user.email, title: "恭喜您注册成功", content: "type#:regist#,userId#:${user.id}",isMust:1,isHandled:0,createDate:new Date()).save()
		
		} catch(Exception e) {
			e.printStackTrace()
			return false
		}
		return true
	}
}
