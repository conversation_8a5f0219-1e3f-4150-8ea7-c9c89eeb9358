package com.cnvd

import java.security.cert.X509Certificate

import sun.security.provider.certpath.OCSPChecker;

class CerVerifyService {
	
	/**
	 * 验证证书是否有效
	 * @param certificate
	 * @return
	 */
	public static boolean verifyCertificate(X509Certificate certificate){
		boolean valid = true;
		try {
			certificate.checkValidity();
		} catch (Exception e) {
			e.printStackTrace();
			valid = false;
		}
		return valid;
	}
	
	
	public static boolean checkCRL(){
		return false;
	}
	
	/**
	 * 获取发行者信息
	 * @param name
	 * @param certificate
	 * @return
	 */
	public static String getPrincipalName(String name, X509Certificate certificate){
		String principalName = certificate.getIssuerX500Principal().getName()
		Map<String,String> principalNameMap = new HashMap<String,String>();
		String[] nameArr = principalName.split(",")
		for(String n : nameArr){
			String[] arr = n.split("=")
			principalNameMap.put(arr[0],arr[1])
		}
		return principalNameMap.get(name);
	}
	
	/**
	 * 获得证书的主题名中各个字段信息
	 * @param name
	 * @param certificate
	 * @return
	 */
	public static String getSubjectName(String name, X509Certificate certificate){
		String principalName = certificate.getSubjectX500Principal().getName()
		Map<String,String> principalNameMap = new HashMap<String,String>();
		String[] nameArr = principalName.split(",")
		for(String n : nameArr){
			String[] arr = n.split("=")
			principalNameMap.put(arr[0],arr[1])
		}
		return principalNameMap.get(name);
	}
}
