package com.cnvd.utils

import org.apache.tools.zip.ZipEntry
import org.apache.tools.zip.ZipFile

import java.util.zip.ZipException

class UnZip {

    static void deCompressZipFile(String fileName)
            throws IOException, FileNotFoundException, ZipException {
        BufferedInputStream bi;
        // 解压目录：原文件名去掉后缀 + "/"
        String decompressDir = fileName.substring(0, fileName.lastIndexOf(".")) + "/";

        ZipFile zf = new ZipFile(fileName, "GBK"); // 支持中文
        Enumeration<? extends ZipEntry> e = zf.getEntries();

        while (e.hasMoreElements()) {
            ZipEntry ze2 = e.nextElement();
            String entryName = ze2.getName();
            System.out.println("解压文件: " + entryName);

            String path = decompressDir + entryName; // 直接使用 entryName 作为路径

            if (ze2.isDirectory()) {
                // 如果是目录，直接创建
                File decompressDirFile = new File(path);
                if (!decompressDirFile.exists()) {
                    decompressDirFile.mkdirs();
                }
            } else {
                // 如果是文件，确保父目录存在
                File file = new File(path);
                File parentDir = file.getParentFile();
                if (parentDir != null && !parentDir.exists()) {
                    parentDir.mkdirs();
                }

                // 写入文件，保留原始文件名
                BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(path));
                bi = new BufferedInputStream(zf.getInputStream(ze2));

                byte[] readContent = new byte[1024];
                int readCount;
                while ((readCount = bi.read(readContent)) != -1) {
                    bos.write(readContent, 0, readCount);
                }

                bos.close();
                bi.close();
            }
        }
        zf.close();
    }

    static void main(String[] args) {
        String archive = "/cnvd/12345.zip"; // 压缩包路径
        try {
            deCompressZipFile(archive);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}